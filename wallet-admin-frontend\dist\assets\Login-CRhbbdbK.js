import{r as i,I as h,_ as m,u as g,a as u,j as e,T as y,R as b,B as j}from"./index-CW-Whzws.js";import{F as t}from"./index-CeljsNjF.js";import{C as v,s as l}from"./index-BVbup1Oj.js";import{I as d,R}from"./index-2rLJYotV.js";import"./row-fflaQe-U.js";import"./Input-Cr3didPW.js";var I={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 10-56 0z"}}]},name:"lock",theme:"outlined"},z=function(s,r){return i.createElement(h,m({},s,{ref:r,icon:I}))},k=i.forwardRef(z);const{Title:w,Text:c,Link:B}=y,F=()=>{const[n]=t.useForm(),[s,r]=i.useState(!1),{login:x}=g(),p=u(),f=async o=>{r(!0);try{await x(o),l.success("登录成功！"),p("/dashboard")}catch(a){l.error(a instanceof Error?a.message:"登录失败，请重试")}finally{r(!1)}};return e.jsx("div",{style:{height:"100vh",display:"flex",justifyContent:"center",alignItems:"center",background:"linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)",padding:"20px"},children:e.jsxs(v,{style:{width:480,boxShadow:"0 15px 35px rgba(0,0,0,0.2)",borderRadius:"16px",border:"1px solid #e8e8e8",overflow:"hidden"},bodyStyle:{padding:"40px 48px"},children:[e.jsx("div",{style:{textAlign:"center",marginBottom:32},children:e.jsx(w,{level:2,style:{color:"#1890ff",marginBottom:0,fontSize:"24px",fontWeight:600},children:"磐石钱包管理系统"})}),e.jsx("div",{style:{border:"1px solid #e8e8e8",borderRadius:"12px",padding:"32px",background:"#fafafa"},children:e.jsxs(t,{form:n,name:"login",onFinish:f,autoComplete:"off",size:"large",layout:"vertical",children:[e.jsx(t.Item,{label:"用户名：",name:"username",rules:[{required:!0,message:"请输入用户名!"},{min:3,message:"用户名至少3个字符!"}],style:{marginBottom:"20px"},children:e.jsx(d,{prefix:e.jsx(b,{style:{color:"#bfbfbf"}}),placeholder:"请输入用户名",style:{height:"44px",borderRadius:"8px"}})}),e.jsx(t.Item,{label:"密码：",name:"password",rules:[{required:!0,message:"请输入密码!"},{min:6,message:"密码至少6个字符!"}],style:{marginBottom:"24px"},children:e.jsx(d.Password,{prefix:e.jsx(k,{style:{color:"#bfbfbf"}}),placeholder:"请输入密码",iconRender:o=>e.jsx(R,{style:{color:o?"#1890ff":"#bfbfbf"}}),style:{height:"44px",borderRadius:"8px"}})}),e.jsx(t.Item,{style:{marginBottom:"16px"},children:e.jsx(j,{type:"primary",htmlType:"submit",loading:s,style:{width:"100%",height:"48px",borderRadius:"8px",fontSize:"16px",fontWeight:500,background:"linear-gradient(135deg, #1890ff 0%, #722ed1 100%)",border:"none"},children:s?"登录中...":"立即登录"})}),e.jsx("div",{style:{textAlign:"center",marginTop:"16px"},children:e.jsx(c,{type:"secondary",style:{fontSize:"13px"},children:"忘记密码？"})})]})}),e.jsxs("div",{style:{marginTop:"32px",paddingTop:"20px",borderTop:"1px solid #f0f0f0"},children:[e.jsx("div",{style:{display:"flex",justifyContent:"flex-start",alignItems:"center",marginBottom:"12px"},children:e.jsx(B,{href:"https://t.me/panshishequ1",target:"_blank",style:{fontSize:"12px",color:"#1890ff"},children:"磐石社区"})}),e.jsx("div",{style:{textAlign:"center"},children:e.jsx(c,{type:"secondary",style:{fontSize:"11px"},children:"©2025 Panshi Wallet All Rights Reserved."})})]})]})})};export{F as default};
