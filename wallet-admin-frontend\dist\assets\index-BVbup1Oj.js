import{r,I as De,_ as oe,ai as ae,x as H,G as qe,c as j,t as ne,ao as Se,b as le,aB as kn,D as He,k as Pe,bw as Gn,o as sn,g as Ae,m as Xe,al as $t,f as et,bp as Wn,C as pe,a0 as St,ar as Fn,am as Kn,aT as Vn,e as y,ag as tt,h as qn,w as At,H as Xn,ch as Un,ci as Yn,cj as Jn,bz as mt,be as Zn,aA as Qn,z as ea,ae as kt,bm as ta,aL as na,c7 as Gt,bK as xt,bt as cn,ap as yt,i as dn,ck as aa,bn as nt,a4 as oa,br as ra,a8 as ia,bq as la}from"./index-CW-Whzws.js";var sa={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"}}]},name:"check-circle",theme:"filled"},ca=function(t,a){return r.createElement(De,oe({},t,{ref:a,icon:sa}))},da=r.forwardRef(ca),ua={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"}}]},name:"close",theme:"outlined"},fa=function(t,a){return r.createElement(De,oe({},t,{ref:a,icon:ua}))},un=r.forwardRef(fa),va={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"}}]},name:"exclamation-circle",theme:"filled"},ma=function(t,a){return r.createElement(De,oe({},t,{ref:a,icon:va}))},ga=r.forwardRef(ma),ba={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272zm-32-344a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"}}]},name:"info-circle",theme:"filled"},pa=function(t,a){return r.createElement(De,oe({},t,{ref:a,icon:ba}))},ha=r.forwardRef(pa),$a=`accept acceptCharset accessKey action allowFullScreen allowTransparency
    alt async autoComplete autoFocus autoPlay capture cellPadding cellSpacing challenge
    charSet checked classID className colSpan cols content contentEditable contextMenu
    controls coords crossOrigin data dateTime default defer dir disabled download draggable
    encType form formAction formEncType formMethod formNoValidate formTarget frameBorder
    headers height hidden high href hrefLang htmlFor httpEquiv icon id inputMode integrity
    is keyParams keyType kind label lang list loop low manifest marginHeight marginWidth max maxLength media
    mediaGroup method min minLength multiple muted name noValidate nonce open
    optimum pattern placeholder poster preload radioGroup readOnly rel required
    reversed role rowSpan rows sandbox scope scoped scrolling seamless selected
    shape size sizes span spellCheck src srcDoc srcLang srcSet start step style
    summary tabIndex target title type useMap value width wmode wrap`,ya=`onCopy onCut onPaste onCompositionEnd onCompositionStart onCompositionUpdate onKeyDown
    onKeyPress onKeyUp onFocus onBlur onChange onInput onSubmit onClick onContextMenu onDoubleClick
    onDrag onDragEnd onDragEnter onDragExit onDragLeave onDragOver onDragStart onDrop onMouseDown
    onMouseEnter onMouseLeave onMouseMove onMouseOut onMouseOver onMouseUp onSelect onTouchCancel
    onTouchEnd onTouchMove onTouchStart onScroll onWheel onAbort onCanPlay onCanPlayThrough
    onDurationChange onEmptied onEncrypted onEnded onError onLoadedData onLoadedMetadata
    onLoadStart onPause onPlay onPlaying onProgress onRateChange onSeeked onSeeking onStalled onSuspend onTimeUpdate onVolumeChange onWaiting onLoad onError`,Ca="".concat($a," ").concat(ya).split(/[\s\n]+/),Sa="aria-",xa="data-";function Wt(e,t){return e.indexOf(t)===0}function Ea(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,a;t===!1?a={aria:!0,data:!0,attr:!0}:t===!0?a={aria:!0}:a=ae({},t);var n={};return Object.keys(e).forEach(function(o){(a.aria&&(o==="role"||Wt(o,Sa))||a.data&&Wt(o,xa)||a.attr&&Ca.includes(o))&&(n[o]=e[o])}),n}var fn=r.forwardRef(function(e,t){var a=e.prefixCls,n=e.style,o=e.className,i=e.duration,l=i===void 0?4.5:i,s=e.showProgress,c=e.pauseOnHover,d=c===void 0?!0:c,f=e.eventKey,u=e.content,m=e.closable,h=e.closeIcon,_=h===void 0?"x":h,$=e.props,b=e.onClick,C=e.onNoticeClose,S=e.times,P=e.hovering,N=r.useState(!1),R=H(N,2),O=R[0],W=R[1],x=r.useState(0),v=H(x,2),p=v[0],g=v[1],I=r.useState(0),E=H(I,2),M=E[0],V=E[1],z=P||O,A=l>0&&s,q=function(){C(f)},w=function(L){(L.key==="Enter"||L.code==="Enter"||L.keyCode===Se.ENTER)&&q()};r.useEffect(function(){if(!z&&l>0){var k=Date.now()-M,L=setTimeout(function(){q()},l*1e3-M);return function(){d&&clearTimeout(L),V(Date.now()-k)}}},[l,z,S]),r.useEffect(function(){if(!z&&A&&(d||M===0)){var k=performance.now(),L,D=function U(){cancelAnimationFrame(L),L=requestAnimationFrame(function(re){var Z=re+M-k,ie=Math.min(Z/(l*1e3),1);g(ie*100),ie<1&&U()})};return D(),function(){d&&cancelAnimationFrame(L)}}},[l,M,z,A,S]);var ee=r.useMemo(function(){return qe(m)==="object"&&m!==null?m:m?{closeIcon:_}:{}},[m,_]),te=Ea(ee,!0),J=100-(!p||p<0?0:p>100?100:p),B="".concat(a,"-notice");return r.createElement("div",oe({},$,{ref:t,className:j(B,o,ne({},"".concat(B,"-closable"),m)),style:n,onMouseEnter:function(L){var D;W(!0),$==null||(D=$.onMouseEnter)===null||D===void 0||D.call($,L)},onMouseLeave:function(L){var D;W(!1),$==null||(D=$.onMouseLeave)===null||D===void 0||D.call($,L)},onClick:b}),r.createElement("div",{className:"".concat(B,"-content")},u),m&&r.createElement("a",oe({tabIndex:0,className:"".concat(B,"-close"),onKeyDown:w,"aria-label":"Close"},te,{onClick:function(L){L.preventDefault(),L.stopPropagation(),q()}}),ee.closeIcon),A&&r.createElement("progress",{className:"".concat(B,"-progress"),max:"100",value:J},J+"%"))}),vn=le.createContext({}),wa=function(t){var a=t.children,n=t.classNames;return le.createElement(vn.Provider,{value:{classNames:n}},a)},Ft=8,Kt=3,Vt=16,Pa=function(t){var a={offset:Ft,threshold:Kt,gap:Vt};if(t&&qe(t)==="object"){var n,o,i;a.offset=(n=t.offset)!==null&&n!==void 0?n:Ft,a.threshold=(o=t.threshold)!==null&&o!==void 0?o:Kt,a.gap=(i=t.gap)!==null&&i!==void 0?i:Vt}return[!!t,a]},Oa=["className","style","classNames","styles"],_a=function(t){var a=t.configList,n=t.placement,o=t.prefixCls,i=t.className,l=t.style,s=t.motion,c=t.onAllNoticeRemoved,d=t.onNoticeClose,f=t.stack,u=r.useContext(vn),m=u.classNames,h=r.useRef({}),_=r.useState(null),$=H(_,2),b=$[0],C=$[1],S=r.useState([]),P=H(S,2),N=P[0],R=P[1],O=a.map(function(z){return{config:z,key:String(z.key)}}),W=Pa(f),x=H(W,2),v=x[0],p=x[1],g=p.offset,I=p.threshold,E=p.gap,M=v&&(N.length>0||O.length<=I),V=typeof s=="function"?s(n):s;return r.useEffect(function(){v&&N.length>1&&R(function(z){return z.filter(function(A){return O.some(function(q){var w=q.key;return A===w})})})},[N,O,v]),r.useEffect(function(){var z;if(v&&h.current[(z=O[O.length-1])===null||z===void 0?void 0:z.key]){var A;C(h.current[(A=O[O.length-1])===null||A===void 0?void 0:A.key])}},[O,v]),le.createElement(kn,oe({key:n,className:j(o,"".concat(o,"-").concat(n),m==null?void 0:m.list,i,ne(ne({},"".concat(o,"-stack"),!!v),"".concat(o,"-stack-expanded"),M)),style:l,keys:O,motionAppear:!0},V,{onAllRemoved:function(){c(n)}}),function(z,A){var q=z.config,w=z.className,ee=z.style,te=z.index,J=q,B=J.key,k=J.times,L=String(B),D=q,U=D.className,re=D.style,Z=D.classNames,ie=D.styles,ue=He(D,Oa),fe=O.findIndex(function(Re){return Re.key===L}),ce={};if(v){var G=O.length-1-(fe>-1?fe:te-1),se=n==="top"||n==="bottom"?"-50%":"0";if(G>0){var de,xe,Oe;ce.height=M?(de=h.current[L])===null||de===void 0?void 0:de.offsetHeight:b==null?void 0:b.offsetHeight;for(var Me=0,Y=0;Y<G;Y++){var K;Me+=((K=h.current[O[O.length-1-Y].key])===null||K===void 0?void 0:K.offsetHeight)+E}var he=(M?Me:G*g)*(n.startsWith("top")?1:-1),_e=!M&&b!==null&&b!==void 0&&b.offsetWidth&&(xe=h.current[L])!==null&&xe!==void 0&&xe.offsetWidth?((b==null?void 0:b.offsetWidth)-g*2*(G<3?G:3))/((Oe=h.current[L])===null||Oe===void 0?void 0:Oe.offsetWidth):1;ce.transform="translate3d(".concat(se,", ").concat(he,"px, 0) scaleX(").concat(_e,")")}else ce.transform="translate3d(".concat(se,", 0, 0)")}return le.createElement("div",{ref:A,className:j("".concat(o,"-notice-wrapper"),w,Z==null?void 0:Z.wrapper),style:ae(ae(ae({},ee),ce),ie==null?void 0:ie.wrapper),onMouseEnter:function(){return R(function(ye){return ye.includes(L)?ye:[].concat(Pe(ye),[L])})},onMouseLeave:function(){return R(function(ye){return ye.filter(function(ve){return ve!==L})})}},le.createElement(fn,oe({},ue,{ref:function(ye){fe>-1?h.current[L]=ye:delete h.current[L]},prefixCls:o,classNames:Z,styles:ie,className:j(U,m==null?void 0:m.notice),style:re,times:k,key:B,eventKey:B,onNoticeClose:d,hovering:v&&N.length>0})))})},Ra=r.forwardRef(function(e,t){var a=e.prefixCls,n=a===void 0?"rc-notification":a,o=e.container,i=e.motion,l=e.maxCount,s=e.className,c=e.style,d=e.onAllRemoved,f=e.stack,u=e.renderNotifications,m=r.useState([]),h=H(m,2),_=h[0],$=h[1],b=function(v){var p,g=_.find(function(I){return I.key===v});g==null||(p=g.onClose)===null||p===void 0||p.call(g),$(function(I){return I.filter(function(E){return E.key!==v})})};r.useImperativeHandle(t,function(){return{open:function(v){$(function(p){var g=Pe(p),I=g.findIndex(function(V){return V.key===v.key}),E=ae({},v);if(I>=0){var M;E.times=(((M=p[I])===null||M===void 0?void 0:M.times)||0)+1,g[I]=E}else E.times=0,g.push(E);return l>0&&g.length>l&&(g=g.slice(-l)),g})},close:function(v){b(v)},destroy:function(){$([])}}});var C=r.useState({}),S=H(C,2),P=S[0],N=S[1];r.useEffect(function(){var x={};_.forEach(function(v){var p=v.placement,g=p===void 0?"topRight":p;g&&(x[g]=x[g]||[],x[g].push(v))}),Object.keys(P).forEach(function(v){x[v]=x[v]||[]}),N(x)},[_]);var R=function(v){N(function(p){var g=ae({},p),I=g[v]||[];return I.length||delete g[v],g})},O=r.useRef(!1);if(r.useEffect(function(){Object.keys(P).length>0?O.current=!0:O.current&&(d==null||d(),O.current=!1)},[P]),!o)return null;var W=Object.keys(P);return Gn.createPortal(r.createElement(r.Fragment,null,W.map(function(x){var v=P[x],p=r.createElement(_a,{key:x,configList:v,placement:x,prefixCls:n,className:s==null?void 0:s(x),style:c==null?void 0:c(x),motion:i,onNoticeClose:b,onAllNoticeRemoved:R,stack:f});return u?u(p,{prefixCls:n,key:x}):p})),o)}),Ia=["getContainer","motion","prefixCls","maxCount","className","style","onAllRemoved","stack","renderNotifications"],Na=function(){return document.body},qt=0;function Ma(){for(var e={},t=arguments.length,a=new Array(t),n=0;n<t;n++)a[n]=arguments[n];return a.forEach(function(o){o&&Object.keys(o).forEach(function(i){var l=o[i];l!==void 0&&(e[i]=l)})}),e}function Ta(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=e.getContainer,a=t===void 0?Na:t,n=e.motion,o=e.prefixCls,i=e.maxCount,l=e.className,s=e.style,c=e.onAllRemoved,d=e.stack,f=e.renderNotifications,u=He(e,Ia),m=r.useState(),h=H(m,2),_=h[0],$=h[1],b=r.useRef(),C=r.createElement(Ra,{container:_,ref:b,prefixCls:o,motion:n,maxCount:i,className:l,style:s,onAllRemoved:c,stack:d,renderNotifications:f}),S=r.useState([]),P=H(S,2),N=P[0],R=P[1],O=sn(function(x){var v=Ma(u,x);(v.key===null||v.key===void 0)&&(v.key="rc-notification-".concat(qt),qt+=1),R(function(p){return[].concat(Pe(p),[{type:"open",config:v}])})}),W=r.useMemo(function(){return{open:O,close:function(v){R(function(p){return[].concat(Pe(p),[{type:"close",key:v}])})},destroy:function(){R(function(v){return[].concat(Pe(v),[{type:"destroy"}])})}}},[]);return r.useEffect(function(){$(a())}),r.useEffect(function(){if(b.current&&N.length){N.forEach(function(p){switch(p.type){case"open":b.current.open(p.config);break;case"close":b.current.close(p.key);break;case"destroy":b.current.destroy();break}});var x,v;R(function(p){return(x!==p||!v)&&(x=p,v=p.filter(function(g){return!N.includes(g)})),v})}},[N]),[W,C]}const La=e=>{const{componentCls:t,iconCls:a,boxShadow:n,colorText:o,colorSuccess:i,colorError:l,colorWarning:s,colorInfo:c,fontSizeLG:d,motionEaseInOutCirc:f,motionDurationSlow:u,marginXS:m,paddingXS:h,borderRadiusLG:_,zIndexPopup:$,contentPadding:b,contentBg:C}=e,S=`${t}-notice`,P=new $t("MessageMoveIn",{"0%":{padding:0,transform:"translateY(-100%)",opacity:0},"100%":{padding:h,transform:"translateY(0)",opacity:1}}),N=new $t("MessageMoveOut",{"0%":{maxHeight:e.height,padding:h,opacity:1},"100%":{maxHeight:0,padding:0,opacity:0}}),R={padding:h,textAlign:"center",[`${t}-custom-content`]:{display:"flex",alignItems:"center"},[`${t}-custom-content > ${a}`]:{marginInlineEnd:m,fontSize:d},[`${S}-content`]:{display:"inline-block",padding:b,background:C,borderRadius:_,boxShadow:n,pointerEvents:"all"},[`${t}-success > ${a}`]:{color:i},[`${t}-error > ${a}`]:{color:l},[`${t}-warning > ${a}`]:{color:s},[`${t}-info > ${a},
      ${t}-loading > ${a}`]:{color:c}};return[{[t]:Object.assign(Object.assign({},et(e)),{color:o,position:"fixed",top:m,width:"100%",pointerEvents:"none",zIndex:$,[`${t}-move-up`]:{animationFillMode:"forwards"},[`
        ${t}-move-up-appear,
        ${t}-move-up-enter
      `]:{animationName:P,animationDuration:u,animationPlayState:"paused",animationTimingFunction:f},[`
        ${t}-move-up-appear${t}-move-up-appear-active,
        ${t}-move-up-enter${t}-move-up-enter-active
      `]:{animationPlayState:"running"},[`${t}-move-up-leave`]:{animationName:N,animationDuration:u,animationPlayState:"paused",animationTimingFunction:f},[`${t}-move-up-leave${t}-move-up-leave-active`]:{animationPlayState:"running"},"&-rtl":{direction:"rtl",span:{direction:"rtl"}}})},{[t]:{[`${S}-wrapper`]:Object.assign({},R)}},{[`${t}-notice-pure-panel`]:Object.assign(Object.assign({},R),{padding:0,textAlign:"start"})}]},ja=e=>({zIndexPopup:e.zIndexPopupBase+Wn+10,contentBg:e.colorBgElevated,contentPadding:`${(e.controlHeightLG-e.fontSize*e.lineHeight)/2}px ${e.paddingSM}px`}),mn=Ae("Message",e=>{const t=Xe(e,{height:150});return[La(t)]},ja);var za=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(a[n[o]]=e[n[o]]);return a};const Ba={info:r.createElement(ha,null),success:r.createElement(da,null),error:r.createElement(Kn,null),warning:r.createElement(ga,null),loading:r.createElement(Fn,null)},gn=({prefixCls:e,type:t,icon:a,children:n})=>r.createElement("div",{className:j(`${e}-custom-content`,`${e}-${t}`)},a||Ba[t],r.createElement("span",null,n)),Ha=e=>{const{prefixCls:t,className:a,type:n,icon:o,content:i}=e,l=za(e,["prefixCls","className","type","icon","content"]),{getPrefixCls:s}=r.useContext(pe),c=t||s("message"),d=St(c),[f,u,m]=mn(c,d);return f(r.createElement(fn,Object.assign({},l,{prefixCls:c,className:j(a,u,`${c}-notice-pure-panel`,m,d),eventKey:"pure",duration:null,content:r.createElement(gn,{prefixCls:c,type:n,icon:o},i)})))};function Da(e,t){return{motionName:t??`${e}-move-up`}}function Et(e){let t;const a=new Promise(o=>{t=e(()=>{o(!0)})}),n=()=>{t==null||t()};return n.then=(o,i)=>a.then(o,i),n.promise=a,n}var Aa=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(a[n[o]]=e[n[o]]);return a};const ka=8,Ga=3,Wa=({children:e,prefixCls:t})=>{const a=St(t),[n,o,i]=mn(t,a);return n(r.createElement(wa,{classNames:{list:j(o,i,a)}},e))},Fa=(e,{prefixCls:t,key:a})=>r.createElement(Wa,{prefixCls:t,key:a},e),Ka=r.forwardRef((e,t)=>{const{top:a,prefixCls:n,getContainer:o,maxCount:i,duration:l=Ga,rtl:s,transitionName:c,onAllRemoved:d}=e,{getPrefixCls:f,getPopupContainer:u,message:m,direction:h}=r.useContext(pe),_=n||f("message"),$=()=>({left:"50%",transform:"translateX(-50%)",top:a??ka}),b=()=>j({[`${_}-rtl`]:s??h==="rtl"}),C=()=>Da(_,c),S=r.createElement("span",{className:`${_}-close-x`},r.createElement(un,{className:`${_}-close-icon`})),[P,N]=Ta({prefixCls:_,style:$,className:b,motion:C,closable:!1,closeIcon:S,duration:l,getContainer:()=>(o==null?void 0:o())||(u==null?void 0:u())||document.body,maxCount:i,onAllRemoved:d,renderNotifications:Fa});return r.useImperativeHandle(t,()=>Object.assign(Object.assign({},P),{prefixCls:_,message:m})),N});let Xt=0;function bn(e){const t=r.useRef(null);return Vn(),[r.useMemo(()=>{const n=c=>{var d;(d=t.current)===null||d===void 0||d.close(c)},o=c=>{if(!t.current){const O=()=>{};return O.then=()=>{},O}const{open:d,prefixCls:f,message:u}=t.current,m=`${f}-notice`,{content:h,icon:_,type:$,key:b,className:C,style:S,onClose:P}=c,N=Aa(c,["content","icon","type","key","className","style","onClose"]);let R=b;return R==null&&(Xt+=1,R=`antd-message-${Xt}`),Et(O=>(d(Object.assign(Object.assign({},N),{key:R,content:r.createElement(gn,{prefixCls:f,type:$,icon:_},h),placement:"top",className:j($&&`${m}-${$}`,C,u==null?void 0:u.className),style:Object.assign(Object.assign({},u==null?void 0:u.style),S),onClose:()=>{P==null||P(),O()}})),()=>{n(R)}))},l={open:o,destroy:c=>{var d;c!==void 0?n(c):(d=t.current)===null||d===void 0||d.destroy()}};return["info","success","warning","error","loading"].forEach(c=>{const d=(f,u,m)=>{let h;f&&typeof f=="object"&&"content"in f?h=f:h={content:f};let _,$;typeof u=="function"?$=u:(_=u,$=m);const b=Object.assign(Object.assign({onClose:$,duration:_},h),{type:c});return o(b)};l[c]=d}),l},[]),r.createElement(Ka,Object.assign({key:"message-holder"},e,{ref:t}))]}function Va(e){return bn(e)}const at=e=>{const{prefixCls:t,className:a,style:n,size:o,shape:i}=e,l=j({[`${t}-lg`]:o==="large",[`${t}-sm`]:o==="small"}),s=j({[`${t}-circle`]:i==="circle",[`${t}-square`]:i==="square",[`${t}-round`]:i==="round"}),c=r.useMemo(()=>typeof o=="number"?{width:o,height:o,lineHeight:`${o}px`}:{},[o]);return r.createElement("span",{className:j(t,l,s,a),style:Object.assign(Object.assign({},c),n)})},qa=new $t("ant-skeleton-loading",{"0%":{backgroundPosition:"100% 50%"},"100%":{backgroundPosition:"0 50%"}}),ot=e=>({height:e,lineHeight:y(e)}),Be=e=>Object.assign({width:e},ot(e)),Xa=e=>({background:e.skeletonLoadingBackground,backgroundSize:"400% 100%",animationName:qa,animationDuration:e.skeletonLoadingMotionDuration,animationTimingFunction:"ease",animationIterationCount:"infinite"}),gt=(e,t)=>Object.assign({width:t(e).mul(5).equal(),minWidth:t(e).mul(5).equal()},ot(e)),Ua=e=>{const{skeletonAvatarCls:t,gradientFromColor:a,controlHeight:n,controlHeightLG:o,controlHeightSM:i}=e;return{[t]:Object.assign({display:"inline-block",verticalAlign:"top",background:a},Be(n)),[`${t}${t}-circle`]:{borderRadius:"50%"},[`${t}${t}-lg`]:Object.assign({},Be(o)),[`${t}${t}-sm`]:Object.assign({},Be(i))}},Ya=e=>{const{controlHeight:t,borderRadiusSM:a,skeletonInputCls:n,controlHeightLG:o,controlHeightSM:i,gradientFromColor:l,calc:s}=e;return{[n]:Object.assign({display:"inline-block",verticalAlign:"top",background:l,borderRadius:a},gt(t,s)),[`${n}-lg`]:Object.assign({},gt(o,s)),[`${n}-sm`]:Object.assign({},gt(i,s))}},Ut=e=>Object.assign({width:e},ot(e)),Ja=e=>{const{skeletonImageCls:t,imageSizeBase:a,gradientFromColor:n,borderRadiusSM:o,calc:i}=e;return{[t]:Object.assign(Object.assign({display:"inline-flex",alignItems:"center",justifyContent:"center",verticalAlign:"middle",background:n,borderRadius:o},Ut(i(a).mul(2).equal())),{[`${t}-path`]:{fill:"#bfbfbf"},[`${t}-svg`]:Object.assign(Object.assign({},Ut(a)),{maxWidth:i(a).mul(4).equal(),maxHeight:i(a).mul(4).equal()}),[`${t}-svg${t}-svg-circle`]:{borderRadius:"50%"}}),[`${t}${t}-circle`]:{borderRadius:"50%"}}},bt=(e,t,a)=>{const{skeletonButtonCls:n}=e;return{[`${a}${n}-circle`]:{width:t,minWidth:t,borderRadius:"50%"},[`${a}${n}-round`]:{borderRadius:t}}},pt=(e,t)=>Object.assign({width:t(e).mul(2).equal(),minWidth:t(e).mul(2).equal()},ot(e)),Za=e=>{const{borderRadiusSM:t,skeletonButtonCls:a,controlHeight:n,controlHeightLG:o,controlHeightSM:i,gradientFromColor:l,calc:s}=e;return Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({[a]:Object.assign({display:"inline-block",verticalAlign:"top",background:l,borderRadius:t,width:s(n).mul(2).equal(),minWidth:s(n).mul(2).equal()},pt(n,s))},bt(e,n,a)),{[`${a}-lg`]:Object.assign({},pt(o,s))}),bt(e,o,`${a}-lg`)),{[`${a}-sm`]:Object.assign({},pt(i,s))}),bt(e,i,`${a}-sm`))},Qa=e=>{const{componentCls:t,skeletonAvatarCls:a,skeletonTitleCls:n,skeletonParagraphCls:o,skeletonButtonCls:i,skeletonInputCls:l,skeletonImageCls:s,controlHeight:c,controlHeightLG:d,controlHeightSM:f,gradientFromColor:u,padding:m,marginSM:h,borderRadius:_,titleHeight:$,blockRadius:b,paragraphLiHeight:C,controlHeightXS:S,paragraphMarginTop:P}=e;return{[t]:{display:"table",width:"100%",[`${t}-header`]:{display:"table-cell",paddingInlineEnd:m,verticalAlign:"top",[a]:Object.assign({display:"inline-block",verticalAlign:"top",background:u},Be(c)),[`${a}-circle`]:{borderRadius:"50%"},[`${a}-lg`]:Object.assign({},Be(d)),[`${a}-sm`]:Object.assign({},Be(f))},[`${t}-content`]:{display:"table-cell",width:"100%",verticalAlign:"top",[n]:{width:"100%",height:$,background:u,borderRadius:b,[`+ ${o}`]:{marginBlockStart:f}},[o]:{padding:0,"> li":{width:"100%",height:C,listStyle:"none",background:u,borderRadius:b,"+ li":{marginBlockStart:S}}},[`${o}> li:last-child:not(:first-child):not(:nth-child(2))`]:{width:"61%"}},[`&-round ${t}-content`]:{[`${n}, ${o} > li`]:{borderRadius:_}}},[`${t}-with-avatar ${t}-content`]:{[n]:{marginBlockStart:h,[`+ ${o}`]:{marginBlockStart:P}}},[`${t}${t}-element`]:Object.assign(Object.assign(Object.assign(Object.assign({display:"inline-block",width:"auto"},Za(e)),Ua(e)),Ya(e)),Ja(e)),[`${t}${t}-block`]:{width:"100%",[i]:{width:"100%"},[l]:{width:"100%"}},[`${t}${t}-active`]:{[`
        ${n},
        ${o} > li,
        ${a},
        ${i},
        ${l},
        ${s}
      `]:Object.assign({},Xa(e))}}},eo=e=>{const{colorFillContent:t,colorFill:a}=e,n=t,o=a;return{color:n,colorGradientEnd:o,gradientFromColor:n,gradientToColor:o,titleHeight:e.controlHeight/2,blockRadius:e.borderRadiusSM,paragraphMarginTop:e.marginLG+e.marginXXS,paragraphLiHeight:e.controlHeight/2}},ke=Ae("Skeleton",e=>{const{componentCls:t,calc:a}=e,n=Xe(e,{skeletonAvatarCls:`${t}-avatar`,skeletonTitleCls:`${t}-title`,skeletonParagraphCls:`${t}-paragraph`,skeletonButtonCls:`${t}-button`,skeletonInputCls:`${t}-input`,skeletonImageCls:`${t}-image`,imageSizeBase:a(e.controlHeight).mul(1.5).equal(),borderRadius:100,skeletonLoadingBackground:`linear-gradient(90deg, ${e.gradientFromColor} 25%, ${e.gradientToColor} 37%, ${e.gradientFromColor} 63%)`,skeletonLoadingMotionDuration:"1.4s"});return[Qa(n)]},eo,{deprecatedTokens:[["color","gradientFromColor"],["colorGradientEnd","gradientToColor"]]}),to=e=>{const{prefixCls:t,className:a,rootClassName:n,active:o,shape:i="circle",size:l="default"}=e,{getPrefixCls:s}=r.useContext(pe),c=s("skeleton",t),[d,f,u]=ke(c),m=tt(e,["prefixCls","className"]),h=j(c,`${c}-element`,{[`${c}-active`]:o},a,n,f,u);return d(r.createElement("div",{className:h},r.createElement(at,Object.assign({prefixCls:`${c}-avatar`,shape:i,size:l},m))))},no=e=>{const{prefixCls:t,className:a,rootClassName:n,active:o,block:i=!1,size:l="default"}=e,{getPrefixCls:s}=r.useContext(pe),c=s("skeleton",t),[d,f,u]=ke(c),m=tt(e,["prefixCls"]),h=j(c,`${c}-element`,{[`${c}-active`]:o,[`${c}-block`]:i},a,n,f,u);return d(r.createElement("div",{className:h},r.createElement(at,Object.assign({prefixCls:`${c}-button`,size:l},m))))},ao="M365.714286 329.142857q0 45.714286-32.036571 77.677714t-77.677714 32.036571-77.677714-32.036571-32.036571-77.677714 32.036571-77.677714 77.677714-32.036571 77.677714 32.036571 32.036571 77.677714zM950.857143 548.571429l0 256-804.571429 0 0-109.714286 182.857143-182.857143 91.428571 91.428571 292.571429-292.571429zM1005.714286 146.285714l-914.285714 0q-7.460571 0-12.873143 5.412571t-5.412571 12.873143l0 694.857143q0 7.460571 5.412571 12.873143t12.873143 5.412571l914.285714 0q7.460571 0 12.873143-5.412571t5.412571-12.873143l0-694.857143q0-7.460571-5.412571-12.873143t-12.873143-5.412571zM1097.142857 164.571429l0 694.857143q0 37.741714-26.843429 64.585143t-64.585143 26.843429l-914.285714 0q-37.741714 0-64.585143-26.843429t-26.843429-64.585143l0-694.857143q0-37.741714 26.843429-64.585143t64.585143-26.843429l914.285714 0q37.741714 0 64.585143 26.843429t26.843429 64.585143z",oo=e=>{const{prefixCls:t,className:a,rootClassName:n,style:o,active:i}=e,{getPrefixCls:l}=r.useContext(pe),s=l("skeleton",t),[c,d,f]=ke(s),u=j(s,`${s}-element`,{[`${s}-active`]:i},a,n,d,f);return c(r.createElement("div",{className:u},r.createElement("div",{className:j(`${s}-image`,a),style:o},r.createElement("svg",{viewBox:"0 0 1098 1024",xmlns:"http://www.w3.org/2000/svg",className:`${s}-image-svg`},r.createElement("title",null,"Image placeholder"),r.createElement("path",{d:ao,className:`${s}-image-path`})))))},ro=e=>{const{prefixCls:t,className:a,rootClassName:n,active:o,block:i,size:l="default"}=e,{getPrefixCls:s}=r.useContext(pe),c=s("skeleton",t),[d,f,u]=ke(c),m=tt(e,["prefixCls"]),h=j(c,`${c}-element`,{[`${c}-active`]:o,[`${c}-block`]:i},a,n,f,u);return d(r.createElement("div",{className:h},r.createElement(at,Object.assign({prefixCls:`${c}-input`,size:l},m))))},io=e=>{const{prefixCls:t,className:a,rootClassName:n,style:o,active:i,children:l}=e,{getPrefixCls:s}=r.useContext(pe),c=s("skeleton",t),[d,f,u]=ke(c),m=j(c,`${c}-element`,{[`${c}-active`]:i},f,a,n,u);return d(r.createElement("div",{className:m},r.createElement("div",{className:j(`${c}-image`,a),style:o},l)))},lo=(e,t)=>{const{width:a,rows:n=2}=t;if(Array.isArray(a))return a[e];if(n-1===e)return a},so=e=>{const{prefixCls:t,className:a,style:n,rows:o=0}=e,i=Array.from({length:o}).map((l,s)=>r.createElement("li",{key:s,style:{width:lo(s,e)}}));return r.createElement("ul",{className:j(t,a),style:n},i)},co=({prefixCls:e,className:t,width:a,style:n})=>r.createElement("h3",{className:j(e,t),style:Object.assign({width:a},n)});function ht(e){return e&&typeof e=="object"?e:{}}function uo(e,t){return e&&!t?{size:"large",shape:"square"}:{size:"large",shape:"circle"}}function fo(e,t){return!e&&t?{width:"38%"}:e&&t?{width:"50%"}:{}}function vo(e,t){const a={};return(!e||!t)&&(a.width="61%"),!e&&t?a.rows=3:a.rows=2,a}const Ge=e=>{const{prefixCls:t,loading:a,className:n,rootClassName:o,style:i,children:l,avatar:s=!1,title:c=!0,paragraph:d=!0,active:f,round:u}=e,{getPrefixCls:m,direction:h,className:_,style:$}=qn("skeleton"),b=m("skeleton",t),[C,S,P]=ke(b);if(a||!("loading"in e)){const N=!!s,R=!!c,O=!!d;let W;if(N){const p=Object.assign(Object.assign({prefixCls:`${b}-avatar`},uo(R,O)),ht(s));W=r.createElement("div",{className:`${b}-header`},r.createElement(at,Object.assign({},p)))}let x;if(R||O){let p;if(R){const I=Object.assign(Object.assign({prefixCls:`${b}-title`},fo(N,O)),ht(c));p=r.createElement(co,Object.assign({},I))}let g;if(O){const I=Object.assign(Object.assign({prefixCls:`${b}-paragraph`},vo(N,R)),ht(d));g=r.createElement(so,Object.assign({},I))}x=r.createElement("div",{className:`${b}-content`},p,g)}const v=j(b,{[`${b}-with-avatar`]:N,[`${b}-active`]:f,[`${b}-rtl`]:h==="rtl",[`${b}-round`]:u},_,n,o,S,P);return C(r.createElement("div",{className:v,style:Object.assign(Object.assign({},$),i)},W,x))}return l??null};Ge.Button=no;Ge.Avatar=to;Ge.Input=ro;Ge.Image=oo;Ge.Node=io;const mo=e=>{const{componentCls:t}=e;return{[t]:{display:"flex",flexFlow:"row wrap",minWidth:0,"&::before, &::after":{display:"flex"},"&-no-wrap":{flexWrap:"nowrap"},"&-start":{justifyContent:"flex-start"},"&-center":{justifyContent:"center"},"&-end":{justifyContent:"flex-end"},"&-space-between":{justifyContent:"space-between"},"&-space-around":{justifyContent:"space-around"},"&-space-evenly":{justifyContent:"space-evenly"},"&-top":{alignItems:"flex-start"},"&-middle":{alignItems:"center"},"&-bottom":{alignItems:"flex-end"}}}},go=e=>{const{componentCls:t}=e;return{[t]:{position:"relative",maxWidth:"100%",minHeight:1}}},bo=(e,t)=>{const{prefixCls:a,componentCls:n,gridColumns:o}=e,i={};for(let l=o;l>=0;l--)l===0?(i[`${n}${t}-${l}`]={display:"none"},i[`${n}-push-${l}`]={insetInlineStart:"auto"},i[`${n}-pull-${l}`]={insetInlineEnd:"auto"},i[`${n}${t}-push-${l}`]={insetInlineStart:"auto"},i[`${n}${t}-pull-${l}`]={insetInlineEnd:"auto"},i[`${n}${t}-offset-${l}`]={marginInlineStart:0},i[`${n}${t}-order-${l}`]={order:0}):(i[`${n}${t}-${l}`]=[{"--ant-display":"block",display:"block"},{display:"var(--ant-display)",flex:`0 0 ${l/o*100}%`,maxWidth:`${l/o*100}%`}],i[`${n}${t}-push-${l}`]={insetInlineStart:`${l/o*100}%`},i[`${n}${t}-pull-${l}`]={insetInlineEnd:`${l/o*100}%`},i[`${n}${t}-offset-${l}`]={marginInlineStart:`${l/o*100}%`},i[`${n}${t}-order-${l}`]={order:l});return i[`${n}${t}-flex`]={flex:`var(--${a}${t}-flex)`},i},Ct=(e,t)=>bo(e,t),po=(e,t,a)=>({[`@media (min-width: ${y(t)})`]:Object.assign({},Ct(e,a))}),ho=()=>({}),$o=()=>({}),Lr=Ae("Grid",mo,ho),yo=e=>({xs:e.screenXSMin,sm:e.screenSMMin,md:e.screenMDMin,lg:e.screenLGMin,xl:e.screenXLMin,xxl:e.screenXXLMin}),jr=Ae("Grid",e=>{const t=Xe(e,{gridColumns:24}),a=yo(t);return delete a.xs,[go(t),Ct(t,""),Ct(t,"-xs"),Object.keys(a).map(n=>po(t,a[n],`-${n}`)).reduce((n,o)=>Object.assign(Object.assign({},n),o),{})]},$o),Co=le.createContext({});var So={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"}}]},name:"search",theme:"outlined"},xo=function(t,a){return r.createElement(De,oe({},t,{ref:a,icon:So}))},zr=r.forwardRef(xo),Eo={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"}},{tag:"path",attrs:{d:"M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"}}]},name:"plus",theme:"outlined"},wo=function(t,a){return r.createElement(De,oe({},t,{ref:a,icon:Eo}))},Po=r.forwardRef(wo);const rt=r.createContext(null);var Oo=function(t){var a=t.activeTabOffset,n=t.horizontal,o=t.rtl,i=t.indicator,l=i===void 0?{}:i,s=l.size,c=l.align,d=c===void 0?"center":c,f=r.useState(),u=H(f,2),m=u[0],h=u[1],_=r.useRef(),$=le.useCallback(function(C){return typeof s=="function"?s(C):typeof s=="number"?s:C},[s]);function b(){At.cancel(_.current)}return r.useEffect(function(){var C={};if(a)if(n){C.width=$(a.width);var S=o?"right":"left";d==="start"&&(C[S]=a[S]),d==="center"&&(C[S]=a[S]+a.width/2,C.transform=o?"translateX(50%)":"translateX(-50%)"),d==="end"&&(C[S]=a[S]+a.width,C.transform="translateX(-100%)")}else C.height=$(a.height),d==="start"&&(C.top=a.top),d==="center"&&(C.top=a.top+a.height/2,C.transform="translateY(-50%)"),d==="end"&&(C.top=a.top+a.height,C.transform="translateY(-100%)");return b(),_.current=At(function(){var P=m&&C&&Object.keys(C).every(function(N){var R=C[N],O=m[N];return typeof R=="number"&&typeof O=="number"?Math.round(R)===Math.round(O):R===O});P||h(C)}),b},[JSON.stringify(a),n,o,d,$]),{style:m}},Yt={width:0,height:0,left:0,top:0};function _o(e,t,a){return r.useMemo(function(){for(var n,o=new Map,i=t.get((n=e[0])===null||n===void 0?void 0:n.key)||Yt,l=i.left+i.width,s=0;s<e.length;s+=1){var c=e[s].key,d=t.get(c);if(!d){var f;d=t.get((f=e[s-1])===null||f===void 0?void 0:f.key)||Yt}var u=o.get(c)||ae({},d);u.right=l-u.left-u.width,o.set(c,u)}return o},[e.map(function(n){return n.key}).join("_"),t,a])}function Jt(e,t){var a=r.useRef(e),n=r.useState({}),o=H(n,2),i=o[1];function l(s){var c=typeof s=="function"?s(a.current):s;c!==a.current&&t(c,a.current),a.current=c,i({})}return[a.current,l]}var Ro=.1,Zt=.01,Qe=20,Qt=Math.pow(.995,Qe);function Io(e,t){var a=r.useState(),n=H(a,2),o=n[0],i=n[1],l=r.useState(0),s=H(l,2),c=s[0],d=s[1],f=r.useState(0),u=H(f,2),m=u[0],h=u[1],_=r.useState(),$=H(_,2),b=$[0],C=$[1],S=r.useRef();function P(v){var p=v.touches[0],g=p.screenX,I=p.screenY;i({x:g,y:I}),window.clearInterval(S.current)}function N(v){if(o){var p=v.touches[0],g=p.screenX,I=p.screenY;i({x:g,y:I});var E=g-o.x,M=I-o.y;t(E,M);var V=Date.now();d(V),h(V-c),C({x:E,y:M})}}function R(){if(o&&(i(null),C(null),b)){var v=b.x/m,p=b.y/m,g=Math.abs(v),I=Math.abs(p);if(Math.max(g,I)<Ro)return;var E=v,M=p;S.current=window.setInterval(function(){if(Math.abs(E)<Zt&&Math.abs(M)<Zt){window.clearInterval(S.current);return}E*=Qt,M*=Qt,t(E*Qe,M*Qe)},Qe)}}var O=r.useRef();function W(v){var p=v.deltaX,g=v.deltaY,I=0,E=Math.abs(p),M=Math.abs(g);E===M?I=O.current==="x"?p:g:E>M?(I=p,O.current="x"):(I=g,O.current="y"),t(-I,-I)&&v.preventDefault()}var x=r.useRef(null);x.current={onTouchStart:P,onTouchMove:N,onTouchEnd:R,onWheel:W},r.useEffect(function(){function v(E){x.current.onTouchStart(E)}function p(E){x.current.onTouchMove(E)}function g(E){x.current.onTouchEnd(E)}function I(E){x.current.onWheel(E)}return document.addEventListener("touchmove",p,{passive:!1}),document.addEventListener("touchend",g,{passive:!0}),e.current.addEventListener("touchstart",v,{passive:!0}),e.current.addEventListener("wheel",I,{passive:!1}),function(){document.removeEventListener("touchmove",p),document.removeEventListener("touchend",g)}},[])}function pn(e){var t=r.useState(0),a=H(t,2),n=a[0],o=a[1],i=r.useRef(0),l=r.useRef();return l.current=e,Xn(function(){var s;(s=l.current)===null||s===void 0||s.call(l)},[n]),function(){i.current===n&&(i.current+=1,o(i.current))}}function No(e){var t=r.useRef([]),a=r.useState({}),n=H(a,2),o=n[1],i=r.useRef(typeof e=="function"?e():e),l=pn(function(){var c=i.current;t.current.forEach(function(d){c=d(c)}),t.current=[],i.current=c,o({})});function s(c){t.current.push(c),l()}return[i.current,s]}var en={width:0,height:0,left:0,top:0,right:0};function Mo(e,t,a,n,o,i,l){var s=l.tabs,c=l.tabPosition,d=l.rtl,f,u,m;return["top","bottom"].includes(c)?(f="width",u=d?"right":"left",m=Math.abs(a)):(f="height",u="top",m=-a),r.useMemo(function(){if(!s.length)return[0,0];for(var h=s.length,_=h,$=0;$<h;$+=1){var b=e.get(s[$].key)||en;if(Math.floor(b[u]+b[f])>Math.floor(m+t)){_=$-1;break}}for(var C=0,S=h-1;S>=0;S-=1){var P=e.get(s[S].key)||en;if(P[u]<m){C=S+1;break}}return C>=_?[0,0]:[C,_]},[e,t,n,o,i,m,c,s.map(function(h){return h.key}).join("_"),d])}function tn(e){var t;return e instanceof Map?(t={},e.forEach(function(a,n){t[n]=a})):t=e,JSON.stringify(t)}var To="TABS_DQ";function hn(e){return String(e).replace(/"/g,To)}function wt(e,t,a,n){return!(!a||n||e===!1||e===void 0&&(t===!1||t===null))}var $n=r.forwardRef(function(e,t){var a=e.prefixCls,n=e.editable,o=e.locale,i=e.style;return!n||n.showAdd===!1?null:r.createElement("button",{ref:t,type:"button",className:"".concat(a,"-nav-add"),style:i,"aria-label":(o==null?void 0:o.addAriaLabel)||"Add tab",onClick:function(s){n.onEdit("add",{event:s})}},n.addIcon||"+")}),nn=r.forwardRef(function(e,t){var a=e.position,n=e.prefixCls,o=e.extra;if(!o)return null;var i,l={};return qe(o)==="object"&&!r.isValidElement(o)?l=o:l.right=o,a==="right"&&(i=l.right),a==="left"&&(i=l.left),i?r.createElement("div",{className:"".concat(n,"-extra-content"),ref:t},i):null}),Lo=r.forwardRef(function(e,t){var a=e.prefixCls,n=e.id,o=e.tabs,i=e.locale,l=e.mobile,s=e.more,c=s===void 0?{}:s,d=e.style,f=e.className,u=e.editable,m=e.tabBarGutter,h=e.rtl,_=e.removeAriaLabel,$=e.onTabClick,b=e.getPopupContainer,C=e.popupClassName,S=r.useState(!1),P=H(S,2),N=P[0],R=P[1],O=r.useState(null),W=H(O,2),x=W[0],v=W[1],p=c.icon,g=p===void 0?"More":p,I="".concat(n,"-more-popup"),E="".concat(a,"-dropdown"),M=x!==null?"".concat(I,"-").concat(x):null,V=i==null?void 0:i.dropdownAriaLabel;function z(B,k){B.preventDefault(),B.stopPropagation(),u.onEdit("remove",{key:k,event:B})}var A=r.createElement(Un,{onClick:function(k){var L=k.key,D=k.domEvent;$(L,D),R(!1)},prefixCls:"".concat(E,"-menu"),id:I,tabIndex:-1,role:"listbox","aria-activedescendant":M,selectedKeys:[x],"aria-label":V!==void 0?V:"expanded dropdown"},o.map(function(B){var k=B.closable,L=B.disabled,D=B.closeIcon,U=B.key,re=B.label,Z=wt(k,D,u,L);return r.createElement(Yn,{key:U,id:"".concat(I,"-").concat(U),role:"option","aria-controls":n&&"".concat(n,"-panel-").concat(U),disabled:L},r.createElement("span",null,re),Z&&r.createElement("button",{type:"button","aria-label":_||"remove",tabIndex:0,className:"".concat(E,"-menu-item-remove"),onClick:function(ue){ue.stopPropagation(),z(ue,U)}},D||u.removeIcon||"×"))}));function q(B){for(var k=o.filter(function(Z){return!Z.disabled}),L=k.findIndex(function(Z){return Z.key===x})||0,D=k.length,U=0;U<D;U+=1){L=(L+B+D)%D;var re=k[L];if(!re.disabled){v(re.key);return}}}function w(B){var k=B.which;if(!N){[Se.DOWN,Se.SPACE,Se.ENTER].includes(k)&&(R(!0),B.preventDefault());return}switch(k){case Se.UP:q(-1),B.preventDefault();break;case Se.DOWN:q(1),B.preventDefault();break;case Se.ESC:R(!1);break;case Se.SPACE:case Se.ENTER:x!==null&&$(x,B);break}}r.useEffect(function(){var B=document.getElementById(M);B&&B.scrollIntoView&&B.scrollIntoView(!1)},[x]),r.useEffect(function(){N||v(null)},[N]);var ee=ne({},h?"marginRight":"marginLeft",m);o.length||(ee.visibility="hidden",ee.order=1);var te=j(ne({},"".concat(E,"-rtl"),h)),J=l?null:r.createElement(Jn,oe({prefixCls:E,overlay:A,visible:o.length?N:!1,onVisibleChange:R,overlayClassName:j(te,C),mouseEnterDelay:.1,mouseLeaveDelay:.1,getPopupContainer:b},c),r.createElement("button",{type:"button",className:"".concat(a,"-nav-more"),style:ee,"aria-haspopup":"listbox","aria-controls":I,id:"".concat(n,"-more"),"aria-expanded":N,onKeyDown:w},g));return r.createElement("div",{className:j("".concat(a,"-nav-operations"),f),style:d,ref:t},J,r.createElement($n,{prefixCls:a,locale:i,editable:u}))});const jo=r.memo(Lo,function(e,t){return t.tabMoving});var zo=function(t){var a=t.prefixCls,n=t.id,o=t.active,i=t.focus,l=t.tab,s=l.key,c=l.label,d=l.disabled,f=l.closeIcon,u=l.icon,m=t.closable,h=t.renderWrapper,_=t.removeAriaLabel,$=t.editable,b=t.onClick,C=t.onFocus,S=t.onBlur,P=t.onKeyDown,N=t.onMouseDown,R=t.onMouseUp,O=t.style,W=t.tabCount,x=t.currentPosition,v="".concat(a,"-tab"),p=wt(m,f,$,d);function g(z){d||b(z)}function I(z){z.preventDefault(),z.stopPropagation(),$.onEdit("remove",{key:s,event:z})}var E=r.useMemo(function(){return u&&typeof c=="string"?r.createElement("span",null,c):c},[c,u]),M=r.useRef(null);r.useEffect(function(){i&&M.current&&M.current.focus()},[i]);var V=r.createElement("div",{key:s,"data-node-key":hn(s),className:j(v,ne(ne(ne(ne({},"".concat(v,"-with-remove"),p),"".concat(v,"-active"),o),"".concat(v,"-disabled"),d),"".concat(v,"-focus"),i)),style:O,onClick:g},r.createElement("div",{ref:M,role:"tab","aria-selected":o,id:n&&"".concat(n,"-tab-").concat(s),className:"".concat(v,"-btn"),"aria-controls":n&&"".concat(n,"-panel-").concat(s),"aria-disabled":d,tabIndex:d?null:o?0:-1,onClick:function(A){A.stopPropagation(),g(A)},onKeyDown:P,onMouseDown:N,onMouseUp:R,onFocus:C,onBlur:S},i&&r.createElement("div",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"Tab ".concat(x," of ").concat(W)),u&&r.createElement("span",{className:"".concat(v,"-icon")},u),c&&E),p&&r.createElement("button",{type:"button",role:"tab","aria-label":_||"remove",tabIndex:o?0:-1,className:"".concat(v,"-remove"),onClick:function(A){A.stopPropagation(),I(A)}},f||$.removeIcon||"×"));return h?h(V):V},Bo=function(t,a){var n=t.offsetWidth,o=t.offsetHeight,i=t.offsetTop,l=t.offsetLeft,s=t.getBoundingClientRect(),c=s.width,d=s.height,f=s.left,u=s.top;return Math.abs(c-n)<1?[c,d,f-a.left,u-a.top]:[n,o,l,i]},ze=function(t){var a=t.current||{},n=a.offsetWidth,o=n===void 0?0:n,i=a.offsetHeight,l=i===void 0?0:i;if(t.current){var s=t.current.getBoundingClientRect(),c=s.width,d=s.height;if(Math.abs(c-o)<1)return[c,d]}return[o,l]},Ze=function(t,a){return t[a?0:1]},an=r.forwardRef(function(e,t){var a=e.className,n=e.style,o=e.id,i=e.animated,l=e.activeKey,s=e.rtl,c=e.extra,d=e.editable,f=e.locale,u=e.tabPosition,m=e.tabBarGutter,h=e.children,_=e.onTabClick,$=e.onTabScroll,b=e.indicator,C=r.useContext(rt),S=C.prefixCls,P=C.tabs,N=r.useRef(null),R=r.useRef(null),O=r.useRef(null),W=r.useRef(null),x=r.useRef(null),v=r.useRef(null),p=r.useRef(null),g=u==="top"||u==="bottom",I=Jt(0,function(F,T){g&&$&&$({direction:F>T?"left":"right"})}),E=H(I,2),M=E[0],V=E[1],z=Jt(0,function(F,T){!g&&$&&$({direction:F>T?"top":"bottom"})}),A=H(z,2),q=A[0],w=A[1],ee=r.useState([0,0]),te=H(ee,2),J=te[0],B=te[1],k=r.useState([0,0]),L=H(k,2),D=L[0],U=L[1],re=r.useState([0,0]),Z=H(re,2),ie=Z[0],ue=Z[1],fe=r.useState([0,0]),ce=H(fe,2),G=ce[0],se=ce[1],de=No(new Map),xe=H(de,2),Oe=xe[0],Me=xe[1],Y=_o(P,Oe,D[0]),K=Ze(J,g),he=Ze(D,g),_e=Ze(ie,g),Re=Ze(G,g),ye=Math.floor(K)<Math.floor(he+_e),ve=ye?K-Re:K-_e,En="".concat(S,"-nav-operations-hidden"),Ee=0,Ie=0;g&&s?(Ee=0,Ie=Math.max(0,he-ve)):(Ee=Math.min(0,ve-he),Ie=0);function lt(F){return F<Ee?Ee:F>Ie?Ie:F}var st=r.useRef(null),wn=r.useState(),Pt=H(wn,2),Ue=Pt[0],Ot=Pt[1];function ct(){Ot(Date.now())}function dt(){st.current&&clearTimeout(st.current)}Io(W,function(F,T){function X(Q,$e){Q(function(me){var Le=lt(me+$e);return Le})}return ye?(g?X(V,F):X(w,T),dt(),ct(),!0):!1}),r.useEffect(function(){return dt(),Ue&&(st.current=setTimeout(function(){Ot(0)},100)),dt},[Ue]);var Pn=Mo(Y,ve,g?M:q,he,_e,Re,ae(ae({},e),{},{tabs:P})),_t=H(Pn,2),On=_t[0],_n=_t[1],Rt=sn(function(){var F=arguments.length>0&&arguments[0]!==void 0?arguments[0]:l,T=Y.get(F)||{width:0,height:0,left:0,right:0,top:0};if(g){var X=M;s?T.right<M?X=T.right:T.right+T.width>M+ve&&(X=T.right+T.width-ve):T.left<-M?X=-T.left:T.left+T.width>-M+ve&&(X=-(T.left+T.width-ve)),w(0),V(lt(X))}else{var Q=q;T.top<-q?Q=-T.top:T.top+T.height>-q+ve&&(Q=-(T.top+T.height-ve)),V(0),w(lt(Q))}}),Rn=r.useState(),It=H(Rn,2),we=It[0],We=It[1],In=r.useState(!1),Nt=H(In,2),Nn=Nt[0],Mt=Nt[1],Ce=P.filter(function(F){return!F.disabled}).map(function(F){return F.key}),Te=function(T){var X=Ce.indexOf(we||l),Q=Ce.length,$e=(X+T+Q)%Q,me=Ce[$e];We(me)},Mn=function(T){var X=T.code,Q=s&&g,$e=Ce[0],me=Ce[Ce.length-1];switch(X){case"ArrowLeft":{g&&Te(Q?1:-1);break}case"ArrowRight":{g&&Te(Q?-1:1);break}case"ArrowUp":{T.preventDefault(),g||Te(-1);break}case"ArrowDown":{T.preventDefault(),g||Te(1);break}case"Home":{T.preventDefault(),We($e);break}case"End":{T.preventDefault(),We(me);break}case"Enter":case"Space":{T.preventDefault(),_(we??l,T);break}case"Backspace":case"Delete":{var Le=Ce.indexOf(we),ge=P.find(function(je){return je.key===we}),vt=wt(ge==null?void 0:ge.closable,ge==null?void 0:ge.closeIcon,d,ge==null?void 0:ge.disabled);vt&&(T.preventDefault(),T.stopPropagation(),d.onEdit("remove",{key:we,event:T}),Le===Ce.length-1?Te(-1):Te(1));break}}},Ye={};g?Ye[s?"marginRight":"marginLeft"]=m:Ye.marginTop=m;var Tt=P.map(function(F,T){var X=F.key;return r.createElement(zo,{id:o,prefixCls:S,key:X,tab:F,style:T===0?void 0:Ye,closable:F.closable,editable:d,active:X===l,focus:X===we,renderWrapper:h,removeAriaLabel:f==null?void 0:f.removeAriaLabel,tabCount:Ce.length,currentPosition:T+1,onClick:function($e){_(X,$e)},onKeyDown:Mn,onFocus:function(){Nn||We(X),Rt(X),ct(),W.current&&(s||(W.current.scrollLeft=0),W.current.scrollTop=0)},onBlur:function(){We(void 0)},onMouseDown:function(){Mt(!0)},onMouseUp:function(){Mt(!1)}})}),Lt=function(){return Me(function(){var T,X=new Map,Q=(T=x.current)===null||T===void 0?void 0:T.getBoundingClientRect();return P.forEach(function($e){var me,Le=$e.key,ge=(me=x.current)===null||me===void 0?void 0:me.querySelector('[data-node-key="'.concat(hn(Le),'"]'));if(ge){var vt=Bo(ge,Q),je=H(vt,4),Bn=je[0],Hn=je[1],Dn=je[2],An=je[3];X.set(Le,{width:Bn,height:Hn,left:Dn,top:An})}}),X})};r.useEffect(function(){Lt()},[P.map(function(F){return F.key}).join("_")]);var Je=pn(function(){var F=ze(N),T=ze(R),X=ze(O);B([F[0]-T[0]-X[0],F[1]-T[1]-X[1]]);var Q=ze(p);ue(Q);var $e=ze(v);se($e);var me=ze(x);U([me[0]-Q[0],me[1]-Q[1]]),Lt()}),Tn=P.slice(0,On),Ln=P.slice(_n+1),jt=[].concat(Pe(Tn),Pe(Ln)),zt=Y.get(l),jn=Oo({activeTabOffset:zt,horizontal:g,indicator:b,rtl:s}),zn=jn.style;r.useEffect(function(){Rt()},[l,Ee,Ie,tn(zt),tn(Y),g]),r.useEffect(function(){Je()},[s]);var Bt=!!jt.length,Fe="".concat(S,"-nav-wrap"),ut,ft,Ht,Dt;return g?s?(ft=M>0,ut=M!==Ie):(ut=M<0,ft=M!==Ee):(Ht=q<0,Dt=q!==Ee),r.createElement(mt,{onResize:Je},r.createElement("div",{ref:Zn(t,N),role:"tablist","aria-orientation":g?"horizontal":"vertical",className:j("".concat(S,"-nav"),a),style:n,onKeyDown:function(){ct()}},r.createElement(nn,{ref:R,position:"left",extra:c,prefixCls:S}),r.createElement(mt,{onResize:Je},r.createElement("div",{className:j(Fe,ne(ne(ne(ne({},"".concat(Fe,"-ping-left"),ut),"".concat(Fe,"-ping-right"),ft),"".concat(Fe,"-ping-top"),Ht),"".concat(Fe,"-ping-bottom"),Dt)),ref:W},r.createElement(mt,{onResize:Je},r.createElement("div",{ref:x,className:"".concat(S,"-nav-list"),style:{transform:"translate(".concat(M,"px, ").concat(q,"px)"),transition:Ue?"none":void 0}},Tt,r.createElement($n,{ref:p,prefixCls:S,locale:f,editable:d,style:ae(ae({},Tt.length===0?void 0:Ye),{},{visibility:Bt?"hidden":null})}),r.createElement("div",{className:j("".concat(S,"-ink-bar"),ne({},"".concat(S,"-ink-bar-animated"),i.inkBar)),style:zn}))))),r.createElement(jo,oe({},e,{removeAriaLabel:f==null?void 0:f.removeAriaLabel,ref:v,prefixCls:S,tabs:jt,className:!Bt&&En,tabMoving:!!Ue})),r.createElement(nn,{ref:O,position:"right",extra:c,prefixCls:S})))}),yn=r.forwardRef(function(e,t){var a=e.prefixCls,n=e.className,o=e.style,i=e.id,l=e.active,s=e.tabKey,c=e.children;return r.createElement("div",{id:i&&"".concat(i,"-panel-").concat(s),role:"tabpanel",tabIndex:l?0:-1,"aria-labelledby":i&&"".concat(i,"-tab-").concat(s),"aria-hidden":!l,style:o,className:j(a,l&&"".concat(a,"-active"),n),ref:t},c)}),Ho=["renderTabBar"],Do=["label","key"],Ao=function(t){var a=t.renderTabBar,n=He(t,Ho),o=r.useContext(rt),i=o.tabs;if(a){var l=ae(ae({},n),{},{panes:i.map(function(s){var c=s.label,d=s.key,f=He(s,Do);return r.createElement(yn,oe({tab:c,key:d,tabKey:d},f))})});return a(l,an)}return r.createElement(an,n)},ko=["key","forceRender","style","className","destroyInactiveTabPane"],Go=function(t){var a=t.id,n=t.activeKey,o=t.animated,i=t.tabPosition,l=t.destroyInactiveTabPane,s=r.useContext(rt),c=s.prefixCls,d=s.tabs,f=o.tabPane,u="".concat(c,"-tabpane");return r.createElement("div",{className:j("".concat(c,"-content-holder"))},r.createElement("div",{className:j("".concat(c,"-content"),"".concat(c,"-content-").concat(i),ne({},"".concat(c,"-content-animated"),f))},d.map(function(m){var h=m.key,_=m.forceRender,$=m.style,b=m.className,C=m.destroyInactiveTabPane,S=He(m,ko),P=h===n;return r.createElement(Qn,oe({key:h,visible:P,forceRender:_,removeOnLeave:!!(l||C),leavedClassName:"".concat(u,"-hidden")},o.tabPaneMotion),function(N,R){var O=N.style,W=N.className;return r.createElement(yn,oe({},S,{prefixCls:u,id:a,tabKey:h,animated:f,active:P,style:ae(ae({},$),O),className:j(b,W),ref:R}))})})))};function Wo(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{inkBar:!0,tabPane:!1},t;return e===!1?t={inkBar:!1,tabPane:!1}:e===!0?t={inkBar:!0,tabPane:!1}:t=ae({inkBar:!0},qe(e)==="object"?e:{}),t.tabPaneMotion&&t.tabPane===void 0&&(t.tabPane=!0),!t.tabPaneMotion&&t.tabPane&&(t.tabPane=!1),t}var Fo=["id","prefixCls","className","items","direction","activeKey","defaultActiveKey","editable","animated","tabPosition","tabBarGutter","tabBarStyle","tabBarExtraContent","locale","more","destroyInactiveTabPane","renderTabBar","onChange","onTabClick","onTabScroll","getPopupContainer","popupClassName","indicator"],on=0,Ko=r.forwardRef(function(e,t){var a=e.id,n=e.prefixCls,o=n===void 0?"rc-tabs":n,i=e.className,l=e.items,s=e.direction,c=e.activeKey,d=e.defaultActiveKey,f=e.editable,u=e.animated,m=e.tabPosition,h=m===void 0?"top":m,_=e.tabBarGutter,$=e.tabBarStyle,b=e.tabBarExtraContent,C=e.locale,S=e.more,P=e.destroyInactiveTabPane,N=e.renderTabBar,R=e.onChange,O=e.onTabClick,W=e.onTabScroll,x=e.getPopupContainer,v=e.popupClassName,p=e.indicator,g=He(e,Fo),I=r.useMemo(function(){return(l||[]).filter(function(G){return G&&qe(G)==="object"&&"key"in G})},[l]),E=s==="rtl",M=Wo(u),V=r.useState(!1),z=H(V,2),A=z[0],q=z[1];r.useEffect(function(){q(ea())},[]);var w=kt(function(){var G;return(G=I[0])===null||G===void 0?void 0:G.key},{value:c,defaultValue:d}),ee=H(w,2),te=ee[0],J=ee[1],B=r.useState(function(){return I.findIndex(function(G){return G.key===te})}),k=H(B,2),L=k[0],D=k[1];r.useEffect(function(){var G=I.findIndex(function(de){return de.key===te});if(G===-1){var se;G=Math.max(0,Math.min(L,I.length-1)),J((se=I[G])===null||se===void 0?void 0:se.key)}D(G)},[I.map(function(G){return G.key}).join("_"),te,L]);var U=kt(null,{value:a}),re=H(U,2),Z=re[0],ie=re[1];r.useEffect(function(){a||(ie("rc-tabs-".concat(on)),on+=1)},[]);function ue(G,se){O==null||O(G,se);var de=G!==te;J(G),de&&(R==null||R(G))}var fe={id:Z,activeKey:te,animated:M,tabPosition:h,rtl:E,mobile:A},ce=ae(ae({},fe),{},{editable:f,locale:C,more:S,tabBarGutter:_,onTabClick:ue,onTabScroll:W,extra:b,style:$,panes:null,getPopupContainer:x,popupClassName:v,indicator:p});return r.createElement(rt.Provider,{value:{tabs:I,prefixCls:o}},r.createElement("div",oe({ref:t,id:a,className:j(o,"".concat(o,"-").concat(h),ne(ne(ne({},"".concat(o,"-mobile"),A),"".concat(o,"-editable"),f),"".concat(o,"-rtl"),E),i)},g),r.createElement(Ao,oe({},ce,{renderTabBar:N})),r.createElement(Go,oe({destroyInactiveTabPane:P},fe,{animated:M}))))});const Vo={motionAppear:!1,motionEnter:!0,motionLeave:!0};function qo(e,t={inkBar:!0,tabPane:!1}){let a;return t===!1?a={inkBar:!1,tabPane:!1}:t===!0?a={inkBar:!0,tabPane:!0}:a=Object.assign({inkBar:!0},typeof t=="object"?t:{}),a.tabPane&&(a.tabPaneMotion=Object.assign(Object.assign({},Vo),{motionName:ta(e,"switch")})),a}var Xo=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(a[n[o]]=e[n[o]]);return a};function Uo(e){return e.filter(t=>t)}function Yo(e,t){if(e)return e.map(n=>{var o;const i=(o=n.destroyOnHidden)!==null&&o!==void 0?o:n.destroyInactiveTabPane;return Object.assign(Object.assign({},n),{destroyInactiveTabPane:i})});const a=na(t).map(n=>{if(r.isValidElement(n)){const{key:o,props:i}=n,l=i||{},{tab:s}=l,c=Xo(l,["tab"]);return Object.assign(Object.assign({key:String(o)},c),{label:s})}return null});return Uo(a)}const Jo=e=>{const{componentCls:t,motionDurationSlow:a}=e;return[{[t]:{[`${t}-switch`]:{"&-appear, &-enter":{transition:"none","&-start":{opacity:0},"&-active":{opacity:1,transition:`opacity ${a}`}},"&-leave":{position:"absolute",transition:"none",inset:0,"&-start":{opacity:1},"&-active":{opacity:0,transition:`opacity ${a}`}}}}},[Gt(e,"slide-up"),Gt(e,"slide-down")]]},Zo=e=>{const{componentCls:t,tabsCardPadding:a,cardBg:n,cardGutter:o,colorBorderSecondary:i,itemSelectedColor:l}=e;return{[`${t}-card`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{margin:0,padding:a,background:n,border:`${y(e.lineWidth)} ${e.lineType} ${i}`,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`},[`${t}-tab-active`]:{color:l,background:e.colorBgContainer},[`${t}-tab-focus:has(${t}-tab-btn:focus-visible)`]:cn(e,-3),[`& ${t}-tab${t}-tab-focus ${t}-tab-btn:focus-visible`]:{outline:"none"},[`${t}-ink-bar`]:{visibility:"hidden"}},[`&${t}-top, &${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginLeft:{_skip_check_:!0,value:y(o)}}}},[`&${t}-top`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`${y(e.borderRadiusLG)} ${y(e.borderRadiusLG)} 0 0`},[`${t}-tab-active`]:{borderBottomColor:e.colorBgContainer}}},[`&${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:`0 0 ${y(e.borderRadiusLG)} ${y(e.borderRadiusLG)}`},[`${t}-tab-active`]:{borderTopColor:e.colorBgContainer}}},[`&${t}-left, &${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginTop:y(o)}}},[`&${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${y(e.borderRadiusLG)} 0 0 ${y(e.borderRadiusLG)}`}},[`${t}-tab-active`]:{borderRightColor:{_skip_check_:!0,value:e.colorBgContainer}}}},[`&${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${y(e.borderRadiusLG)} ${y(e.borderRadiusLG)} 0`}},[`${t}-tab-active`]:{borderLeftColor:{_skip_check_:!0,value:e.colorBgContainer}}}}}}},Qo=e=>{const{componentCls:t,itemHoverColor:a,dropdownEdgeChildVerticalPadding:n}=e;return{[`${t}-dropdown`]:Object.assign(Object.assign({},et(e)),{position:"absolute",top:-9999,left:{_skip_check_:!0,value:-9999},zIndex:e.zIndexPopup,display:"block","&-hidden":{display:"none"},[`${t}-dropdown-menu`]:{maxHeight:e.tabsDropdownHeight,margin:0,padding:`${y(n)} 0`,overflowX:"hidden",overflowY:"auto",textAlign:{_skip_check_:!0,value:"left"},listStyleType:"none",backgroundColor:e.colorBgContainer,backgroundClip:"padding-box",borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,"&-item":Object.assign(Object.assign({},xt),{display:"flex",alignItems:"center",minWidth:e.tabsDropdownWidth,margin:0,padding:`${y(e.paddingXXS)} ${y(e.paddingSM)}`,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"> span":{flex:1,whiteSpace:"nowrap"},"&-remove":{flex:"none",marginLeft:{_skip_check_:!0,value:e.marginSM},color:e.colorIcon,fontSize:e.fontSizeSM,background:"transparent",border:0,cursor:"pointer","&:hover":{color:a}},"&:hover":{background:e.controlItemBgHover},"&-disabled":{"&, &:hover":{color:e.colorTextDisabled,background:"transparent",cursor:"not-allowed"}}})}})}},er=e=>{const{componentCls:t,margin:a,colorBorderSecondary:n,horizontalMargin:o,verticalItemPadding:i,verticalItemMargin:l,calc:s}=e;return{[`${t}-top, ${t}-bottom`]:{flexDirection:"column",[`> ${t}-nav, > div > ${t}-nav`]:{margin:o,"&::before":{position:"absolute",right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},borderBottom:`${y(e.lineWidth)} ${e.lineType} ${n}`,content:"''"},[`${t}-ink-bar`]:{height:e.lineWidthBold,"&-animated":{transition:`width ${e.motionDurationSlow}, left ${e.motionDurationSlow},
            right ${e.motionDurationSlow}`}},[`${t}-nav-wrap`]:{"&::before, &::after":{top:0,bottom:0,width:e.controlHeight},"&::before":{left:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowLeft},"&::after":{right:{_skip_check_:!0,value:0},boxShadow:e.boxShadowTabsOverflowRight},[`&${t}-nav-wrap-ping-left::before`]:{opacity:1},[`&${t}-nav-wrap-ping-right::after`]:{opacity:1}}}},[`${t}-top`]:{[`> ${t}-nav,
        > div > ${t}-nav`]:{"&::before":{bottom:0},[`${t}-ink-bar`]:{bottom:0}}},[`${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,marginTop:a,marginBottom:0,"&::before":{top:0},[`${t}-ink-bar`]:{top:0}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0}},[`${t}-left, ${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{flexDirection:"column",minWidth:s(e.controlHeight).mul(1.25).equal(),[`${t}-tab`]:{padding:i,textAlign:"center"},[`${t}-tab + ${t}-tab`]:{margin:l},[`${t}-nav-wrap`]:{flexDirection:"column","&::before, &::after":{right:{_skip_check_:!0,value:0},left:{_skip_check_:!0,value:0},height:e.controlHeight},"&::before":{top:0,boxShadow:e.boxShadowTabsOverflowTop},"&::after":{bottom:0,boxShadow:e.boxShadowTabsOverflowBottom},[`&${t}-nav-wrap-ping-top::before`]:{opacity:1},[`&${t}-nav-wrap-ping-bottom::after`]:{opacity:1}},[`${t}-ink-bar`]:{width:e.lineWidthBold,"&-animated":{transition:`height ${e.motionDurationSlow}, top ${e.motionDurationSlow}`}},[`${t}-nav-list, ${t}-nav-operations`]:{flex:"1 0 auto",flexDirection:"column"}}},[`${t}-left`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-ink-bar`]:{right:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{marginLeft:{_skip_check_:!0,value:y(s(e.lineWidth).mul(-1).equal())},borderLeft:{_skip_check_:!0,value:`${y(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingLeft:{_skip_check_:!0,value:e.paddingLG}}}},[`${t}-right`]:{[`> ${t}-nav, > div > ${t}-nav`]:{order:1,[`${t}-ink-bar`]:{left:{_skip_check_:!0,value:0}}},[`> ${t}-content-holder, > div > ${t}-content-holder`]:{order:0,marginRight:{_skip_check_:!0,value:s(e.lineWidth).mul(-1).equal()},borderRight:{_skip_check_:!0,value:`${y(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},[`> ${t}-content > ${t}-tabpane`]:{paddingRight:{_skip_check_:!0,value:e.paddingLG}}}}}},tr=e=>{const{componentCls:t,cardPaddingSM:a,cardPaddingLG:n,cardHeightSM:o,cardHeightLG:i,horizontalItemPaddingSM:l,horizontalItemPaddingLG:s}=e;return{[t]:{"&-small":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:l,fontSize:e.titleFontSizeSM}}},"&-large":{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:s,fontSize:e.titleFontSizeLG,lineHeight:e.lineHeightLG}}}},[`${t}-card`]:{[`&${t}-small`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:a},[`${t}-nav-add`]:{minWidth:o,minHeight:o}},[`&${t}-bottom`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`0 0 ${y(e.borderRadius)} ${y(e.borderRadius)}`}},[`&${t}-top`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:`${y(e.borderRadius)} ${y(e.borderRadius)} 0 0`}},[`&${t}-right`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`0 ${y(e.borderRadius)} ${y(e.borderRadius)} 0`}}},[`&${t}-left`]:{[`> ${t}-nav ${t}-tab`]:{borderRadius:{_skip_check_:!0,value:`${y(e.borderRadius)} 0 0 ${y(e.borderRadius)}`}}}},[`&${t}-large`]:{[`> ${t}-nav`]:{[`${t}-tab`]:{padding:n},[`${t}-nav-add`]:{minWidth:i,minHeight:i}}}}}},nr=e=>{const{componentCls:t,itemActiveColor:a,itemHoverColor:n,iconCls:o,tabsHorizontalItemMargin:i,horizontalItemPadding:l,itemSelectedColor:s,itemColor:c}=e,d=`${t}-tab`;return{[d]:{position:"relative",WebkitTouchCallout:"none",WebkitTapHighlightColor:"transparent",display:"inline-flex",alignItems:"center",padding:l,fontSize:e.titleFontSize,background:"transparent",border:0,outline:"none",cursor:"pointer",color:c,"&-btn, &-remove":{"&:focus:not(:focus-visible), &:active":{color:a}},"&-btn":{outline:"none",transition:`all ${e.motionDurationSlow}`,[`${d}-icon:not(:last-child)`]:{marginInlineEnd:e.marginSM}},"&-remove":Object.assign({flex:"none",marginRight:{_skip_check_:!0,value:e.calc(e.marginXXS).mul(-1).equal()},marginLeft:{_skip_check_:!0,value:e.marginXS},color:e.colorIcon,fontSize:e.fontSizeSM,background:"transparent",border:"none",outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}`,"&:hover":{color:e.colorTextHeading}},yt(e)),"&:hover":{color:n},[`&${d}-active ${d}-btn`]:{color:s,textShadow:e.tabsActiveTextShadow},[`&${d}-focus ${d}-btn:focus-visible`]:cn(e),[`&${d}-disabled`]:{color:e.colorTextDisabled,cursor:"not-allowed"},[`&${d}-disabled ${d}-btn, &${d}-disabled ${t}-remove`]:{"&:focus, &:active":{color:e.colorTextDisabled}},[`& ${d}-remove ${o}`]:{margin:0},[`${o}:not(:last-child)`]:{marginRight:{_skip_check_:!0,value:e.marginSM}}},[`${d} + ${d}`]:{margin:{_skip_check_:!0,value:i}}}},ar=e=>{const{componentCls:t,tabsHorizontalItemMarginRTL:a,iconCls:n,cardGutter:o,calc:i}=e;return{[`${t}-rtl`]:{direction:"rtl",[`${t}-nav`]:{[`${t}-tab`]:{margin:{_skip_check_:!0,value:a},[`${t}-tab:last-of-type`]:{marginLeft:{_skip_check_:!0,value:0}},[n]:{marginRight:{_skip_check_:!0,value:0},marginLeft:{_skip_check_:!0,value:y(e.marginSM)}},[`${t}-tab-remove`]:{marginRight:{_skip_check_:!0,value:y(e.marginXS)},marginLeft:{_skip_check_:!0,value:y(i(e.marginXXS).mul(-1).equal())},[n]:{margin:0}}}},[`&${t}-left`]:{[`> ${t}-nav`]:{order:1},[`> ${t}-content-holder`]:{order:0}},[`&${t}-right`]:{[`> ${t}-nav`]:{order:0},[`> ${t}-content-holder`]:{order:1}},[`&${t}-card${t}-top, &${t}-card${t}-bottom`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-tab + ${t}-tab`]:{marginRight:{_skip_check_:!0,value:o},marginLeft:{_skip_check_:!0,value:0}}}}},[`${t}-dropdown-rtl`]:{direction:"rtl"},[`${t}-menu-item`]:{[`${t}-dropdown-rtl`]:{textAlign:{_skip_check_:!0,value:"right"}}}}},or=e=>{const{componentCls:t,tabsCardPadding:a,cardHeight:n,cardGutter:o,itemHoverColor:i,itemActiveColor:l,colorBorderSecondary:s}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},et(e)),{display:"flex",[`> ${t}-nav, > div > ${t}-nav`]:{position:"relative",display:"flex",flex:"none",alignItems:"center",[`${t}-nav-wrap`]:{position:"relative",display:"flex",flex:"auto",alignSelf:"stretch",overflow:"hidden",whiteSpace:"nowrap",transform:"translate(0)","&::before, &::after":{position:"absolute",zIndex:1,opacity:0,transition:`opacity ${e.motionDurationSlow}`,content:"''",pointerEvents:"none"}},[`${t}-nav-list`]:{position:"relative",display:"flex",transition:`opacity ${e.motionDurationSlow}`},[`${t}-nav-operations`]:{display:"flex",alignSelf:"stretch"},[`${t}-nav-operations-hidden`]:{position:"absolute",visibility:"hidden",pointerEvents:"none"},[`${t}-nav-more`]:{position:"relative",padding:a,background:"transparent",border:0,color:e.colorText,"&::after":{position:"absolute",right:{_skip_check_:!0,value:0},bottom:0,left:{_skip_check_:!0,value:0},height:e.calc(e.controlHeightLG).div(8).equal(),transform:"translateY(100%)",content:"''"}},[`${t}-nav-add`]:Object.assign({minWidth:n,minHeight:n,marginLeft:{_skip_check_:!0,value:o},background:"transparent",border:`${y(e.lineWidth)} ${e.lineType} ${s}`,borderRadius:`${y(e.borderRadiusLG)} ${y(e.borderRadiusLG)} 0 0`,outline:"none",cursor:"pointer",color:e.colorText,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOut}`,"&:hover":{color:i},"&:active, &:focus:not(:focus-visible)":{color:l}},yt(e,-3))},[`${t}-extra-content`]:{flex:"none"},[`${t}-ink-bar`]:{position:"absolute",background:e.inkBarColor,pointerEvents:"none"}}),nr(e)),{[`${t}-content`]:{position:"relative",width:"100%"},[`${t}-content-holder`]:{flex:"auto",minWidth:0,minHeight:0},[`${t}-tabpane`]:Object.assign(Object.assign({},yt(e)),{"&-hidden":{display:"none"}})}),[`${t}-centered`]:{[`> ${t}-nav, > div > ${t}-nav`]:{[`${t}-nav-wrap`]:{[`&:not([class*='${t}-nav-wrap-ping']) > ${t}-nav-list`]:{margin:"auto"}}}}}},rr=e=>{const{cardHeight:t,cardHeightSM:a,cardHeightLG:n,controlHeight:o,controlHeightLG:i}=e,l=t||i,s=a||o,c=n||i+8;return{zIndexPopup:e.zIndexPopupBase+50,cardBg:e.colorFillAlter,cardHeight:l,cardHeightSM:s,cardHeightLG:c,cardPadding:`${(l-e.fontHeight)/2-e.lineWidth}px ${e.padding}px`,cardPaddingSM:`${(s-e.fontHeight)/2-e.lineWidth}px ${e.paddingXS}px`,cardPaddingLG:`${(c-e.fontHeightLG)/2-e.lineWidth}px ${e.padding}px`,titleFontSize:e.fontSize,titleFontSizeLG:e.fontSizeLG,titleFontSizeSM:e.fontSize,inkBarColor:e.colorPrimary,horizontalMargin:`0 0 ${e.margin}px 0`,horizontalItemGutter:32,horizontalItemMargin:"",horizontalItemMarginRTL:"",horizontalItemPadding:`${e.paddingSM}px 0`,horizontalItemPaddingSM:`${e.paddingXS}px 0`,horizontalItemPaddingLG:`${e.padding}px 0`,verticalItemPadding:`${e.paddingXS}px ${e.paddingLG}px`,verticalItemMargin:`${e.margin}px 0 0 0`,itemColor:e.colorText,itemSelectedColor:e.colorPrimary,itemHoverColor:e.colorPrimaryHover,itemActiveColor:e.colorPrimaryActive,cardGutter:e.marginXXS/2}},ir=Ae("Tabs",e=>{const t=Xe(e,{tabsCardPadding:e.cardPadding,dropdownEdgeChildVerticalPadding:e.paddingXXS,tabsActiveTextShadow:"0 0 0.25px currentcolor",tabsDropdownHeight:200,tabsDropdownWidth:120,tabsHorizontalItemMargin:`0 0 0 ${y(e.horizontalItemGutter)}`,tabsHorizontalItemMarginRTL:`0 0 0 ${y(e.horizontalItemGutter)}`});return[tr(t),ar(t),er(t),Qo(t),Zo(t),or(t),Jo(t)]},rr),lr=()=>null;var sr=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(a[n[o]]=e[n[o]]);return a};const Cn=e=>{var t,a,n,o,i,l,s,c,d,f,u;const{type:m,className:h,rootClassName:_,size:$,onEdit:b,hideAdd:C,centered:S,addIcon:P,removeIcon:N,moreIcon:R,more:O,popupClassName:W,children:x,items:v,animated:p,style:g,indicatorSize:I,indicator:E,destroyInactiveTabPane:M,destroyOnHidden:V}=e,z=sr(e,["type","className","rootClassName","size","onEdit","hideAdd","centered","addIcon","removeIcon","moreIcon","more","popupClassName","children","items","animated","style","indicatorSize","indicator","destroyInactiveTabPane","destroyOnHidden"]),{prefixCls:A}=z,{direction:q,tabs:w,getPrefixCls:ee,getPopupContainer:te}=r.useContext(pe),J=ee("tabs",A),B=St(J),[k,L,D]=ir(J,B);let U;m==="editable-card"&&(U={onEdit:(G,{key:se,event:de})=>{b==null||b(G==="add"?de:se,G)},removeIcon:(t=N??(w==null?void 0:w.removeIcon))!==null&&t!==void 0?t:r.createElement(un,null),addIcon:(P??(w==null?void 0:w.addIcon))||r.createElement(Po,null),showAdd:C!==!0});const re=ee(),Z=dn($),ie=Yo(v,x),ue=qo(J,p),fe=Object.assign(Object.assign({},w==null?void 0:w.style),g),ce={align:(a=E==null?void 0:E.align)!==null&&a!==void 0?a:(n=w==null?void 0:w.indicator)===null||n===void 0?void 0:n.align,size:(s=(i=(o=E==null?void 0:E.size)!==null&&o!==void 0?o:I)!==null&&i!==void 0?i:(l=w==null?void 0:w.indicator)===null||l===void 0?void 0:l.size)!==null&&s!==void 0?s:w==null?void 0:w.indicatorSize};return k(r.createElement(Ko,Object.assign({direction:q,getPopupContainer:te},z,{items:ie,className:j({[`${J}-${Z}`]:Z,[`${J}-card`]:["card","editable-card"].includes(m),[`${J}-editable-card`]:m==="editable-card",[`${J}-centered`]:S},w==null?void 0:w.className,h,_,L,D,B),popupClassName:j(W,L,D,B),style:fe,editable:U,more:Object.assign({icon:(u=(f=(d=(c=w==null?void 0:w.more)===null||c===void 0?void 0:c.icon)!==null&&d!==void 0?d:w==null?void 0:w.moreIcon)!==null&&f!==void 0?f:R)!==null&&u!==void 0?u:r.createElement(aa,null),transitionName:`${re}-slide-up`},O),prefixCls:J,animated:ue,indicator:ce,destroyInactiveTabPane:V??M})))};Cn.TabPane=lr;var cr=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(a[n[o]]=e[n[o]]);return a};const Sn=e=>{var{prefixCls:t,className:a,hoverable:n=!0}=e,o=cr(e,["prefixCls","className","hoverable"]);const{getPrefixCls:i}=r.useContext(pe),l=i("card",t),s=j(`${l}-grid`,a,{[`${l}-grid-hoverable`]:n});return r.createElement("div",Object.assign({},o,{className:s}))},dr=e=>{const{antCls:t,componentCls:a,headerHeight:n,headerPadding:o,tabsMarginBottom:i}=e;return Object.assign(Object.assign({display:"flex",justifyContent:"center",flexDirection:"column",minHeight:n,marginBottom:-1,padding:`0 ${y(o)}`,color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.headerFontSize,background:e.headerBg,borderBottom:`${y(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`,borderRadius:`${y(e.borderRadiusLG)} ${y(e.borderRadiusLG)} 0 0`},nt()),{"&-wrapper":{width:"100%",display:"flex",alignItems:"center"},"&-title":Object.assign(Object.assign({display:"inline-block",flex:1},xt),{[`
          > ${a}-typography,
          > ${a}-typography-edit-content
        `]:{insetInlineStart:0,marginTop:0,marginBottom:0}}),[`${t}-tabs-top`]:{clear:"both",marginBottom:i,color:e.colorText,fontWeight:"normal",fontSize:e.fontSize,"&-bar":{borderBottom:`${y(e.lineWidth)} ${e.lineType} ${e.colorBorderSecondary}`}}})},ur=e=>{const{cardPaddingBase:t,colorBorderSecondary:a,cardShadow:n,lineWidth:o}=e;return{width:"33.33%",padding:t,border:0,borderRadius:0,boxShadow:`
      ${y(o)} 0 0 0 ${a},
      0 ${y(o)} 0 0 ${a},
      ${y(o)} ${y(o)} 0 0 ${a},
      ${y(o)} 0 0 0 ${a} inset,
      0 ${y(o)} 0 0 ${a} inset;
    `,transition:`all ${e.motionDurationMid}`,"&-hoverable:hover":{position:"relative",zIndex:1,boxShadow:n}}},fr=e=>{const{componentCls:t,iconCls:a,actionsLiMargin:n,cardActionsIconSize:o,colorBorderSecondary:i,actionsBg:l}=e;return Object.assign(Object.assign({margin:0,padding:0,listStyle:"none",background:l,borderTop:`${y(e.lineWidth)} ${e.lineType} ${i}`,display:"flex",borderRadius:`0 0 ${y(e.borderRadiusLG)} ${y(e.borderRadiusLG)}`},nt()),{"& > li":{margin:n,color:e.colorTextDescription,textAlign:"center","> span":{position:"relative",display:"block",minWidth:e.calc(e.cardActionsIconSize).mul(2).equal(),fontSize:e.fontSize,lineHeight:e.lineHeight,cursor:"pointer","&:hover":{color:e.colorPrimary,transition:`color ${e.motionDurationMid}`},[`a:not(${t}-btn), > ${a}`]:{display:"inline-block",width:"100%",color:e.colorIcon,lineHeight:y(e.fontHeight),transition:`color ${e.motionDurationMid}`,"&:hover":{color:e.colorPrimary}},[`> ${a}`]:{fontSize:o,lineHeight:y(e.calc(o).mul(e.lineHeight).equal())}},"&:not(:last-child)":{borderInlineEnd:`${y(e.lineWidth)} ${e.lineType} ${i}`}}})},vr=e=>Object.assign(Object.assign({margin:`${y(e.calc(e.marginXXS).mul(-1).equal())} 0`,display:"flex"},nt()),{"&-avatar":{paddingInlineEnd:e.padding},"&-detail":{overflow:"hidden",flex:1,"> div:not(:last-child)":{marginBottom:e.marginXS}},"&-title":Object.assign({color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:e.fontSizeLG},xt),"&-description":{color:e.colorTextDescription}}),mr=e=>{const{componentCls:t,colorFillAlter:a,headerPadding:n,bodyPadding:o}=e;return{[`${t}-head`]:{padding:`0 ${y(n)}`,background:a,"&-title":{fontSize:e.fontSize}},[`${t}-body`]:{padding:`${y(e.padding)} ${y(o)}`}}},gr=e=>{const{componentCls:t}=e;return{overflow:"hidden",[`${t}-body`]:{userSelect:"none"}}},br=e=>{const{componentCls:t,cardShadow:a,cardHeadPadding:n,colorBorderSecondary:o,boxShadowTertiary:i,bodyPadding:l,extraColor:s}=e;return{[t]:Object.assign(Object.assign({},et(e)),{position:"relative",background:e.colorBgContainer,borderRadius:e.borderRadiusLG,[`&:not(${t}-bordered)`]:{boxShadow:i},[`${t}-head`]:dr(e),[`${t}-extra`]:{marginInlineStart:"auto",color:s,fontWeight:"normal",fontSize:e.fontSize},[`${t}-body`]:Object.assign({padding:l,borderRadius:`0 0 ${y(e.borderRadiusLG)} ${y(e.borderRadiusLG)}`},nt()),[`${t}-grid`]:ur(e),[`${t}-cover`]:{"> *":{display:"block",width:"100%",borderRadius:`${y(e.borderRadiusLG)} ${y(e.borderRadiusLG)} 0 0`}},[`${t}-actions`]:fr(e),[`${t}-meta`]:vr(e)}),[`${t}-bordered`]:{border:`${y(e.lineWidth)} ${e.lineType} ${o}`,[`${t}-cover`]:{marginTop:-1,marginInlineStart:-1,marginInlineEnd:-1}},[`${t}-hoverable`]:{cursor:"pointer",transition:`box-shadow ${e.motionDurationMid}, border-color ${e.motionDurationMid}`,"&:hover":{borderColor:"transparent",boxShadow:a}},[`${t}-contain-grid`]:{borderRadius:`${y(e.borderRadiusLG)} ${y(e.borderRadiusLG)} 0 0 `,[`${t}-body`]:{display:"flex",flexWrap:"wrap"},[`&:not(${t}-loading) ${t}-body`]:{marginBlockStart:e.calc(e.lineWidth).mul(-1).equal(),marginInlineStart:e.calc(e.lineWidth).mul(-1).equal(),padding:0}},[`${t}-contain-tabs`]:{[`> div${t}-head`]:{minHeight:0,[`${t}-head-title, ${t}-extra`]:{paddingTop:n}}},[`${t}-type-inner`]:mr(e),[`${t}-loading`]:gr(e),[`${t}-rtl`]:{direction:"rtl"}}},pr=e=>{const{componentCls:t,bodyPaddingSM:a,headerPaddingSM:n,headerHeightSM:o,headerFontSizeSM:i}=e;return{[`${t}-small`]:{[`> ${t}-head`]:{minHeight:o,padding:`0 ${y(n)}`,fontSize:i,[`> ${t}-head-wrapper`]:{[`> ${t}-extra`]:{fontSize:e.fontSize}}},[`> ${t}-body`]:{padding:a}},[`${t}-small${t}-contain-tabs`]:{[`> ${t}-head`]:{[`${t}-head-title, ${t}-extra`]:{paddingTop:0,display:"flex",alignItems:"center"}}}}},hr=e=>{var t,a;return{headerBg:"transparent",headerFontSize:e.fontSizeLG,headerFontSizeSM:e.fontSize,headerHeight:e.fontSizeLG*e.lineHeightLG+e.padding*2,headerHeightSM:e.fontSize*e.lineHeight+e.paddingXS*2,actionsBg:e.colorBgContainer,actionsLiMargin:`${e.paddingSM}px 0`,tabsMarginBottom:-e.padding-e.lineWidth,extraColor:e.colorText,bodyPaddingSM:12,headerPaddingSM:12,bodyPadding:(t=e.bodyPadding)!==null&&t!==void 0?t:e.paddingLG,headerPadding:(a=e.headerPadding)!==null&&a!==void 0?a:e.paddingLG}},$r=Ae("Card",e=>{const t=Xe(e,{cardShadow:e.boxShadowCard,cardHeadPadding:e.padding,cardPaddingBase:e.paddingLG,cardActionsIconSize:e.fontSize});return[br(t),pr(t)]},hr);var rn=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(a[n[o]]=e[n[o]]);return a};const yr=e=>{const{actionClasses:t,actions:a=[],actionStyle:n}=e;return r.createElement("ul",{className:t,style:n},a.map((o,i)=>{const l=`action-${i}`;return r.createElement("li",{style:{width:`${100/a.length}%`},key:l},r.createElement("span",null,o))}))},Cr=r.forwardRef((e,t)=>{const{prefixCls:a,className:n,rootClassName:o,style:i,extra:l,headStyle:s={},bodyStyle:c={},title:d,loading:f,bordered:u,variant:m,size:h,type:_,cover:$,actions:b,tabList:C,children:S,activeTabKey:P,defaultActiveTabKey:N,tabBarExtraContent:R,hoverable:O,tabProps:W={},classNames:x,styles:v}=e,p=rn(e,["prefixCls","className","rootClassName","style","extra","headStyle","bodyStyle","title","loading","bordered","variant","size","type","cover","actions","tabList","children","activeTabKey","defaultActiveTabKey","tabBarExtraContent","hoverable","tabProps","classNames","styles"]),{getPrefixCls:g,direction:I,card:E}=r.useContext(pe),[M]=oa("card",m,u),V=Y=>{var K;(K=e.onTabChange)===null||K===void 0||K.call(e,Y)},z=Y=>{var K;return j((K=E==null?void 0:E.classNames)===null||K===void 0?void 0:K[Y],x==null?void 0:x[Y])},A=Y=>{var K;return Object.assign(Object.assign({},(K=E==null?void 0:E.styles)===null||K===void 0?void 0:K[Y]),v==null?void 0:v[Y])},q=r.useMemo(()=>{let Y=!1;return r.Children.forEach(S,K=>{(K==null?void 0:K.type)===Sn&&(Y=!0)}),Y},[S]),w=g("card",a),[ee,te,J]=$r(w),B=r.createElement(Ge,{loading:!0,active:!0,paragraph:{rows:4},title:!1},S),k=P!==void 0,L=Object.assign(Object.assign({},W),{[k?"activeKey":"defaultActiveKey"]:k?P:N,tabBarExtraContent:R});let D;const U=dn(h),re=!U||U==="default"?"large":U,Z=C?r.createElement(Cn,Object.assign({size:re},L,{className:`${w}-head-tabs`,onChange:V,items:C.map(Y=>{var{tab:K}=Y,he=rn(Y,["tab"]);return Object.assign({label:K},he)})})):null;if(d||l||Z){const Y=j(`${w}-head`,z("header")),K=j(`${w}-head-title`,z("title")),he=j(`${w}-extra`,z("extra")),_e=Object.assign(Object.assign({},s),A("header"));D=r.createElement("div",{className:Y,style:_e},r.createElement("div",{className:`${w}-head-wrapper`},d&&r.createElement("div",{className:K,style:A("title")},d),l&&r.createElement("div",{className:he,style:A("extra")},l)),Z)}const ie=j(`${w}-cover`,z("cover")),ue=$?r.createElement("div",{className:ie,style:A("cover")},$):null,fe=j(`${w}-body`,z("body")),ce=Object.assign(Object.assign({},c),A("body")),G=r.createElement("div",{className:fe,style:ce},f?B:S),se=j(`${w}-actions`,z("actions")),de=b!=null&&b.length?r.createElement(yr,{actionClasses:se,actionStyle:A("actions"),actions:b}):null,xe=tt(p,["onTabChange"]),Oe=j(w,E==null?void 0:E.className,{[`${w}-loading`]:f,[`${w}-bordered`]:M!=="borderless",[`${w}-hoverable`]:O,[`${w}-contain-grid`]:q,[`${w}-contain-tabs`]:C==null?void 0:C.length,[`${w}-${U}`]:U,[`${w}-type-${_}`]:!!_,[`${w}-rtl`]:I==="rtl"},n,o,te,J),Me=Object.assign(Object.assign({},E==null?void 0:E.style),i);return ee(r.createElement("div",Object.assign({ref:t},xe,{className:Oe,style:Me}),D,ue,G,de))});var Sr=function(e,t){var a={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(a[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(a[n[o]]=e[n[o]]);return a};const xr=e=>{const{prefixCls:t,className:a,avatar:n,title:o,description:i}=e,l=Sr(e,["prefixCls","className","avatar","title","description"]),{getPrefixCls:s}=r.useContext(pe),c=s("card",t),d=j(`${c}-meta`,a),f=n?r.createElement("div",{className:`${c}-meta-avatar`},n):null,u=o?r.createElement("div",{className:`${c}-meta-title`},o):null,m=i?r.createElement("div",{className:`${c}-meta-description`},i):null,h=u||m?r.createElement("div",{className:`${c}-meta-detail`},u,m):null;return r.createElement("div",Object.assign({},l,{className:d}),f,h)},xn=Cr;xn.Grid=Sn;xn.Meta=xr;let be=null,Ne=e=>e(),Ke=[],Ve={};function ln(){const{getContainer:e,duration:t,rtl:a,maxCount:n,top:o}=Ve,i=(e==null?void 0:e())||document.body;return{getContainer:()=>i,duration:t,rtl:a,maxCount:n,top:o}}const Er=le.forwardRef((e,t)=>{const{messageConfig:a,sync:n}=e,{getPrefixCls:o}=r.useContext(pe),i=Ve.prefixCls||o("message"),l=r.useContext(Co),[s,c]=bn(Object.assign(Object.assign(Object.assign({},a),{prefixCls:i}),l.message));return le.useImperativeHandle(t,()=>{const d=Object.assign({},s);return Object.keys(d).forEach(f=>{d[f]=(...u)=>(n(),s[f].apply(s,u))}),{instance:d,sync:n}}),c}),wr=le.forwardRef((e,t)=>{const[a,n]=le.useState(ln),o=()=>{n(ln)};le.useEffect(o,[]);const i=la(),l=i.getRootPrefixCls(),s=i.getIconPrefixCls(),c=i.getTheme(),d=le.createElement(Er,{ref:t,sync:o,messageConfig:a});return le.createElement(ia,{prefixCls:l,iconPrefixCls:s,theme:c},i.holderRender?i.holderRender(d):d)});function it(){if(!be){const e=document.createDocumentFragment(),t={fragment:e};be=t,Ne(()=>{ra()(le.createElement(wr,{ref:n=>{const{instance:o,sync:i}=n||{};Promise.resolve().then(()=>{!t.instance&&o&&(t.instance=o,t.sync=i,it())})}}),e)});return}be.instance&&(Ke.forEach(e=>{const{type:t,skipped:a}=e;if(!a)switch(t){case"open":{Ne(()=>{const n=be.instance.open(Object.assign(Object.assign({},Ve),e.config));n==null||n.then(e.resolve),e.setCloseFn(n)});break}case"destroy":Ne(()=>{be==null||be.instance.destroy(e.key)});break;default:Ne(()=>{var n;const o=(n=be.instance)[t].apply(n,Pe(e.args));o==null||o.then(e.resolve),e.setCloseFn(o)})}}),Ke=[])}function Pr(e){Ve=Object.assign(Object.assign({},Ve),e),Ne(()=>{var t;(t=be==null?void 0:be.sync)===null||t===void 0||t.call(be)})}function Or(e){const t=Et(a=>{let n;const o={type:"open",config:e,resolve:a,setCloseFn:i=>{n=i}};return Ke.push(o),()=>{n?Ne(()=>{n()}):o.skipped=!0}});return it(),t}function _r(e,t){const a=Et(n=>{let o;const i={type:e,args:t,resolve:n,setCloseFn:l=>{o=l}};return Ke.push(i),()=>{o?Ne(()=>{o()}):i.skipped=!0}});return it(),a}const Rr=e=>{Ke.push({type:"destroy",key:e}),it()},Ir=["success","info","warning","error","loading"],Nr={open:Or,destroy:Rr,config:Pr,useMessage:Va,_InternalPanelDoNotUseOrYouWillBeFired:Ha},Mr=Nr;Ir.forEach(e=>{Mr[e]=(...t)=>_r(e,t)});export{xn as C,ga as R,Ge as S,un as a,da as b,zr as c,ha as d,Lr as e,yo as g,Ea as p,Mr as s,jr as u};
