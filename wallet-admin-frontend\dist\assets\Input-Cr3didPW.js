import{r as u,D as Se,x as de,ae as be,bS as we,k as Ee,b as m,E as Ne,_ as me,c as S,F as Fe,ai as Oe,t as ve,bT as ge,ag as Re,h as _e,a0 as Ie,bU as Pe,b6 as $e,a1 as Be,i as je,a3 as Ae,a2 as ze,bV as Ke,a4 as De,J as Ve,a5 as Ce,a6 as xe,a7 as Te}from"./index-CW-Whzws.js";var ke=["autoComplete","onChange","onFocus","onBlur","onPressEnter","onKeyDown","onKeyUp","prefixCls","disabled","htmlSize","className","maxLength","suffix","showCount","count","type","classes","classNames","styles","onCompositionStart","onCompositionEnd"],Le=u.forwardRef(function(e,b){var v=e.autoComplete,s=e.onChange,o=e.onFocus,f=e.onBlur,g=e.onPressEnter,d=e.onKeyDown,E=e.onKeyUp,B=e.prefixCls,C=B===void 0?"rc-input":B,N=e.disabled,M=e.htmlSize,X=e.className,Y=e.maxLength,j=e.suffix,W=e.showCount,A=e.count,F=e.type,Z=F===void 0?"text":F,ee=e.classes,h=e.classNames,c=e.styles,z=e.onCompositionStart,K=e.onCompositionEnd,te=Se(e,ke),ne=u.useState(!1),D=de(ne,2),ae=D[0],n=D[1],P=u.useRef(!1),x=u.useRef(!1),r=u.useRef(null),O=u.useRef(null),V=function(t){r.current&&Fe(r.current,t)},oe=be(e.defaultValue,{value:e.value}),H=de(oe,2),T=H[0],p=H[1],k=T==null?"":String(T),se=u.useState(null),J=de(se,2),w=J[0],re=J[1],i=we(A,W),R=i.max||Y,_=i.strategy(k),le=!!R&&_>R;u.useImperativeHandle(b,function(){var a;return{focus:V,blur:function(){var l;(l=r.current)===null||l===void 0||l.blur()},setSelectionRange:function(l,U,$){var I;(I=r.current)===null||I===void 0||I.setSelectionRange(l,U,$)},select:function(){var l;(l=r.current)===null||l===void 0||l.select()},input:r.current,nativeElement:((a=O.current)===null||a===void 0?void 0:a.nativeElement)||r.current}}),u.useEffect(function(){x.current&&(x.current=!1),n(function(a){return a&&N?!1:a})},[N]);var q=function(t,l,U){var $=l;if(!P.current&&i.exceedFormatter&&i.max&&i.strategy(l)>i.max){if($=i.exceedFormatter(l,{max:i.max}),l!==$){var I,fe;re([((I=r.current)===null||I===void 0?void 0:I.selectionStart)||0,((fe=r.current)===null||fe===void 0?void 0:fe.selectionEnd)||0])}}else if(U.source==="compositionEnd")return;p($),r.current&&ge(r.current,t,s,$)};u.useEffect(function(){if(w){var a;(a=r.current)===null||a===void 0||a.setSelectionRange.apply(a,Ee(w))}},[w]);var ue=function(t){q(t,t.target.value,{source:"change"})},ie=function(t){P.current=!1,q(t,t.currentTarget.value,{source:"compositionEnd"}),K==null||K(t)},ce=function(t){g&&t.key==="Enter"&&!x.current&&(x.current=!0,g(t)),d==null||d(t)},G=function(t){t.key==="Enter"&&(x.current=!1),E==null||E(t)},Q=function(t){n(!0),o==null||o(t)},y=function(t){x.current&&(x.current=!1),n(!1),f==null||f(t)},L=function(t){p(""),V(),r.current&&ge(r.current,t,s)},he=le&&"".concat(C,"-out-of-range"),pe=function(){var t=Re(e,["prefixCls","onPressEnter","addonBefore","addonAfter","prefix","suffix","allowClear","defaultValue","showCount","count","classes","htmlSize","styles","classNames","onClear"]);return m.createElement("input",me({autoComplete:v},t,{onChange:ue,onFocus:Q,onBlur:y,onKeyDown:ce,onKeyUp:G,className:S(C,ve({},"".concat(C,"-disabled"),N),h==null?void 0:h.input),style:c==null?void 0:c.input,ref:r,size:M,type:Z,onCompositionStart:function(U){P.current=!0,z==null||z(U)},onCompositionEnd:ie}))},ye=function(){var t=Number(R)>0;if(j||i.show){var l=i.showFormatter?i.showFormatter({value:k,count:_,maxLength:R}):"".concat(_).concat(t?" / ".concat(R):"");return m.createElement(m.Fragment,null,i.show&&m.createElement("span",{className:S("".concat(C,"-show-count-suffix"),ve({},"".concat(C,"-show-count-has-suffix"),!!j),h==null?void 0:h.count),style:Oe({},c==null?void 0:c.count)},l),j)}return null};return m.createElement(Ne,me({},te,{prefixCls:C,className:S(X,he),handleReset:L,value:k,focused:ae,triggerFocus:V,suffix:ye(),disabled:N,classes:ee,classNames:h,styles:c,ref:O}),pe())});function Ue(e,b){const v=u.useRef([]),s=()=>{v.current.push(setTimeout(()=>{var o,f,g,d;!((o=e.current)===null||o===void 0)&&o.input&&((f=e.current)===null||f===void 0?void 0:f.input.getAttribute("type"))==="password"&&(!((g=e.current)===null||g===void 0)&&g.input.hasAttribute("value"))&&((d=e.current)===null||d===void 0||d.input.removeAttribute("value"))}))};return u.useEffect(()=>(b&&s(),()=>v.current.forEach(o=>{o&&clearTimeout(o)})),[]),s}function Me(e){return!!(e.prefix||e.suffix||e.allowClear||e.showCount)}var We=function(e,b){var v={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&b.indexOf(s)<0&&(v[s]=e[s]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,s=Object.getOwnPropertySymbols(e);o<s.length;o++)b.indexOf(s[o])<0&&Object.prototype.propertyIsEnumerable.call(e,s[o])&&(v[s[o]]=e[s[o]]);return v};const Je=u.forwardRef((e,b)=>{const{prefixCls:v,bordered:s=!0,status:o,size:f,disabled:g,onBlur:d,onFocus:E,suffix:B,allowClear:C,addonAfter:N,addonBefore:M,className:X,style:Y,styles:j,rootClassName:W,onChange:A,classNames:F,variant:Z}=e,ee=We(e,["prefixCls","bordered","status","size","disabled","onBlur","onFocus","suffix","allowClear","addonAfter","addonBefore","className","style","styles","rootClassName","onChange","classNames","variant"]),{getPrefixCls:h,direction:c,allowClear:z,autoComplete:K,className:te,style:ne,classNames:D,styles:ae}=_e("input"),n=h("input",v),P=u.useRef(null),x=Ie(n),[r,O,V]=Pe(n,W),[oe]=$e(n,x),{compactSize:H,compactItemClassnames:T}=Be(n,c),p=je(y=>{var L;return(L=f??H)!==null&&L!==void 0?L:y}),k=m.useContext(Ae),se=g??k,{status:J,hasFeedback:w,feedbackIcon:re}=u.useContext(ze),i=Te(J,o),R=Me(e)||!!w;u.useRef(R);const _=Ue(P,!0),le=y=>{_(),d==null||d(y)},q=y=>{_(),E==null||E(y)},ue=y=>{_(),A==null||A(y)},ie=(w||B)&&m.createElement(m.Fragment,null,B,w&&re),ce=Ke(C??z),[G,Q]=De("input",Z,s);return r(oe(m.createElement(Le,Object.assign({ref:Ve(b,P),prefixCls:n,autoComplete:K},ee,{disabled:se,onBlur:le,onFocus:q,style:Object.assign(Object.assign({},ne),Y),styles:Object.assign(Object.assign({},ae),j),suffix:ie,allowClear:ce,className:S(X,W,V,x,T,te),onChange:ue,addonBefore:M&&m.createElement(xe,{form:!0,space:!0},M),addonAfter:N&&m.createElement(xe,{form:!0,space:!0},N),classNames:Object.assign(Object.assign(Object.assign({},F),D),{input:S({[`${n}-sm`]:p==="small",[`${n}-lg`]:p==="large",[`${n}-rtl`]:c==="rtl"},F==null?void 0:F.input,D.input,O),variant:S({[`${n}-${G}`]:Q},Ce(n,i)),affixWrapper:S({[`${n}-affix-wrapper-sm`]:p==="small",[`${n}-affix-wrapper-lg`]:p==="large",[`${n}-affix-wrapper-rtl`]:c==="rtl"},O),wrapper:S({[`${n}-group-rtl`]:c==="rtl"},O),groupWrapper:S({[`${n}-group-wrapper-sm`]:p==="small",[`${n}-group-wrapper-lg`]:p==="large",[`${n}-group-wrapper-rtl`]:c==="rtl",[`${n}-group-wrapper-${G}`]:Q},Ce(`${n}-group-wrapper`,i,w),O)})}))))});export{Je as I,Ue as u};
