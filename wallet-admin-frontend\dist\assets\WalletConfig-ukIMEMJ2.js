import{g as qe,m as nt,f as Fe,e as X,h as Gt,i as _t,r as i,c as le,I as Ve,_ as ge,q as Xt,s as Kt,t as se,v as $n,x as Me,y as jn,z as Cn,w as Je,D as rt,E as In,F as kn,G as Ae,H as dt,J as En,K as Nn,L as xt,M as On,N as Tn,O as Ut,P as Mn,Q as Pn,U as Rn,V as Dn,W as zn,X as An,Y as Fn,Z as Bn,$ as Vn,C as He,a0 as Ln,a1 as Wn,a2 as qn,a3 as Qt,a4 as Hn,a5 as kt,a6 as Et,a7 as Gn,a8 as Xn,a9 as Kn,aa as Un,ab as Nt,B as ce,ac as Qn,ad as Yn,ae as vt,af as Zn,ag as Yt,ah as Jn,ai as Ot,aj as mt,ak as Ye,al as er,am as tr,an as nr,ao as Tt,ap as rr,aq as ar,ar as lr,j as n,T as L,b as Ce,as as we,at as Zt,S as ir,au as sr,A as Ie,av as or}from"./index-CW-Whzws.js";import{R as cr,S as pe,T as ke,u as dr}from"./index-pOBwrJ9T.js";import{R as gt,u as Ke,a as ur}from"./ReloadOutlined-DZubnAgL.js";import{F as j}from"./index-CeljsNjF.js";import{R as ye,C as E}from"./row-fflaQe-U.js";import{R as je,a as pr,F as Jt}from"./Table-DINlcmbY.js";import{I as ne,R as mr}from"./index-2rLJYotV.js";import{a as gr,A as Pe,M as Be}from"./index-CBm39ssz.js";import{u as en}from"./useWalletRemarks-BeWTBa_i.js";import{R as tn,a as hr,b as fr,s as Q,c as _r,C as xr}from"./index-BVbup1Oj.js";import"./Input-Cr3didPW.js";const vr=e=>{const{componentCls:t}=e;return{[t]:{"&-horizontal":{[`&${t}`]:{"&-sm":{marginBlock:e.marginXS},"&-md":{marginBlock:e.margin}}}}}},yr=e=>{const{componentCls:t,sizePaddingEdgeHorizontal:r,colorSplit:a,lineWidth:l,textPaddingInline:d,orientationMargin:m,verticalMarginInline:c}=e;return{[t]:Object.assign(Object.assign({},Fe(e)),{borderBlockStart:`${X(l)} solid ${a}`,"&-vertical":{position:"relative",top:"-0.06em",display:"inline-block",height:"0.9em",marginInline:c,marginBlock:0,verticalAlign:"middle",borderTop:0,borderInlineStart:`${X(l)} solid ${a}`},"&-horizontal":{display:"flex",clear:"both",width:"100%",minWidth:"100%",margin:`${X(e.marginLG)} 0`},[`&-horizontal${t}-with-text`]:{display:"flex",alignItems:"center",margin:`${X(e.dividerHorizontalWithTextGutterMargin)} 0`,color:e.colorTextHeading,fontWeight:500,fontSize:e.fontSizeLG,whiteSpace:"nowrap",textAlign:"center",borderBlockStart:`0 ${a}`,"&::before, &::after":{position:"relative",width:"50%",borderBlockStart:`${X(l)} solid transparent`,borderBlockStartColor:"inherit",borderBlockEnd:0,transform:"translateY(50%)",content:"''"}},[`&-horizontal${t}-with-text-start`]:{"&::before":{width:`calc(${m} * 100%)`},"&::after":{width:`calc(100% - ${m} * 100%)`}},[`&-horizontal${t}-with-text-end`]:{"&::before":{width:`calc(100% - ${m} * 100%)`},"&::after":{width:`calc(${m} * 100%)`}},[`${t}-inner-text`]:{display:"inline-block",paddingBlock:0,paddingInline:d},"&-dashed":{background:"none",borderColor:a,borderStyle:"dashed",borderWidth:`${X(l)} 0 0`},[`&-horizontal${t}-with-text${t}-dashed`]:{"&::before, &::after":{borderStyle:"dashed none none"}},[`&-vertical${t}-dashed`]:{borderInlineStartWidth:l,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},"&-dotted":{background:"none",borderColor:a,borderStyle:"dotted",borderWidth:`${X(l)} 0 0`},[`&-horizontal${t}-with-text${t}-dotted`]:{"&::before, &::after":{borderStyle:"dotted none none"}},[`&-vertical${t}-dotted`]:{borderInlineStartWidth:l,borderInlineEnd:0,borderBlockStart:0,borderBlockEnd:0},[`&-plain${t}-with-text`]:{color:e.colorText,fontWeight:"normal",fontSize:e.fontSize},[`&-horizontal${t}-with-text-start${t}-no-default-orientation-margin-start`]:{"&::before":{width:0},"&::after":{width:"100%"},[`${t}-inner-text`]:{paddingInlineStart:r}},[`&-horizontal${t}-with-text-end${t}-no-default-orientation-margin-end`]:{"&::before":{width:"100%"},"&::after":{width:0},[`${t}-inner-text`]:{paddingInlineEnd:r}}})}},br=e=>({textPaddingInline:"1em",orientationMargin:.05,verticalMarginInline:e.marginXS}),Sr=qe("Divider",e=>{const t=nt(e,{dividerHorizontalWithTextGutterMargin:e.margin,sizePaddingEdgeHorizontal:0});return[yr(t),vr(t)]},br,{unitless:{orientationMargin:!0}});var wr=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,a=Object.getOwnPropertySymbols(e);l<a.length;l++)t.indexOf(a[l])<0&&Object.prototype.propertyIsEnumerable.call(e,a[l])&&(r[a[l]]=e[a[l]]);return r};const $r={small:"sm",middle:"md"},Te=e=>{const{getPrefixCls:t,direction:r,className:a,style:l}=Gt("divider"),{prefixCls:d,type:m="horizontal",orientation:c="center",orientationMargin:s,className:u,rootClassName:o,children:h,dashed:p,variant:f="solid",plain:S,style:g,size:v}=e,_=wr(e,["prefixCls","type","orientation","orientationMargin","className","rootClassName","children","dashed","variant","plain","style","size"]),w=t("divider",d),[O,$,T]=Sr(w),N=_t(v),P=$r[N],I=!!h,R=i.useMemo(()=>c==="left"?r==="rtl"?"end":"start":c==="right"?r==="rtl"?"start":"end":c,[r,c]),k=R==="start"&&s!=null,Y=R==="end"&&s!=null,K=le(w,a,$,T,`${w}-${m}`,{[`${w}-with-text`]:I,[`${w}-with-text-${R}`]:I,[`${w}-dashed`]:!!p,[`${w}-${f}`]:f!=="solid",[`${w}-plain`]:!!S,[`${w}-rtl`]:r==="rtl",[`${w}-no-default-orientation-margin-start`]:k,[`${w}-no-default-orientation-margin-end`]:Y,[`${w}-${P}`]:!!P},u,o),W=i.useMemo(()=>typeof s=="number"?s:/^\d+$/.test(s)?Number(s):s,[s]),V={marginInlineStart:k?W:void 0,marginInlineEnd:Y?W:void 0};return O(i.createElement("div",Object.assign({className:K,style:Object.assign(Object.assign({},l),g)},_,{role:"separator"}),h&&m!=="vertical"&&i.createElement("span",{className:`${w}-inner-text`,style:V},h)))};var jr={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"}}]},name:"up",theme:"outlined"},Cr=function(t,r){return i.createElement(Ve,ge({},t,{ref:r,icon:jr}))},Ir=i.forwardRef(Cr);function ht(){return typeof BigInt=="function"}function nn(e){return!e&&e!==0&&!Number.isNaN(e)||!String(e).trim()}function De(e){var t=e.trim(),r=t.startsWith("-");r&&(t=t.slice(1)),t=t.replace(/(\.\d*[^0])0*$/,"$1").replace(/\.0*$/,"").replace(/^0+/,""),t.startsWith(".")&&(t="0".concat(t));var a=t||"0",l=a.split("."),d=l[0]||"0",m=l[1]||"0";d==="0"&&m==="0"&&(r=!1);var c=r?"-":"";return{negative:r,negativeStr:c,trimStr:a,integerStr:d,decimalStr:m,fullStr:"".concat(c).concat(a)}}function yt(e){var t=String(e);return!Number.isNaN(Number(t))&&t.includes("e")}function Re(e){var t=String(e);if(yt(e)){var r=Number(t.slice(t.indexOf("e-")+2)),a=t.match(/\.(\d+)/);return a!=null&&a[1]&&(r+=a[1].length),r}return t.includes(".")&&bt(t)?t.length-t.indexOf(".")-1:0}function at(e){var t=String(e);if(yt(e)){if(e>Number.MAX_SAFE_INTEGER)return String(ht()?BigInt(e).toString():Number.MAX_SAFE_INTEGER);if(e<Number.MIN_SAFE_INTEGER)return String(ht()?BigInt(e).toString():Number.MIN_SAFE_INTEGER);t=e.toFixed(Re(t))}return De(t).fullStr}function bt(e){return typeof e=="number"?!Number.isNaN(e):e?/^\s*-?\d+(\.\d+)?\s*$/.test(e)||/^\s*-?\d+\.\s*$/.test(e)||/^\s*-?\.\d+\s*$/.test(e):!1}var kr=function(){function e(t){if(Kt(this,e),se(this,"origin",""),se(this,"negative",void 0),se(this,"integer",void 0),se(this,"decimal",void 0),se(this,"decimalLen",void 0),se(this,"empty",void 0),se(this,"nan",void 0),nn(t)){this.empty=!0;return}if(this.origin=String(t),t==="-"||Number.isNaN(t)){this.nan=!0;return}var r=t;if(yt(r)&&(r=Number(r)),r=typeof r=="string"?r:at(r),bt(r)){var a=De(r);this.negative=a.negative;var l=a.trimStr.split(".");this.integer=BigInt(l[0]);var d=l[1]||"0";this.decimal=BigInt(d),this.decimalLen=d.length}else this.nan=!0}return Xt(e,[{key:"getMark",value:function(){return this.negative?"-":""}},{key:"getIntegerStr",value:function(){return this.integer.toString()}},{key:"getDecimalStr",value:function(){return this.decimal.toString().padStart(this.decimalLen,"0")}},{key:"alignDecimal",value:function(r){var a="".concat(this.getMark()).concat(this.getIntegerStr()).concat(this.getDecimalStr().padEnd(r,"0"));return BigInt(a)}},{key:"negate",value:function(){var r=new e(this.toString());return r.negative=!r.negative,r}},{key:"cal",value:function(r,a,l){var d=Math.max(this.getDecimalStr().length,r.getDecimalStr().length),m=this.alignDecimal(d),c=r.alignDecimal(d),s=a(m,c).toString(),u=l(d),o=De(s),h=o.negativeStr,p=o.trimStr,f="".concat(h).concat(p.padStart(u+1,"0"));return new e("".concat(f.slice(0,-u),".").concat(f.slice(-u)))}},{key:"add",value:function(r){if(this.isInvalidate())return new e(r);var a=new e(r);return a.isInvalidate()?this:this.cal(a,function(l,d){return l+d},function(l){return l})}},{key:"multi",value:function(r){var a=new e(r);return this.isInvalidate()||a.isInvalidate()?new e(NaN):this.cal(a,function(l,d){return l*d},function(l){return l*2})}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return this.nan}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(r){return this.toString()===(r==null?void 0:r.toString())}},{key:"lessEquals",value:function(r){return this.add(r.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.isNaN()?NaN:Number(this.toString())}},{key:"toString",value:function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return r?this.isInvalidate()?"":De("".concat(this.getMark()).concat(this.getIntegerStr(),".").concat(this.getDecimalStr())).fullStr:this.origin}}]),e}(),Er=function(){function e(t){if(Kt(this,e),se(this,"origin",""),se(this,"number",void 0),se(this,"empty",void 0),nn(t)){this.empty=!0;return}this.origin=String(t),this.number=Number(t)}return Xt(e,[{key:"negate",value:function(){return new e(-this.toNumber())}},{key:"add",value:function(r){if(this.isInvalidate())return new e(r);var a=Number(r);if(Number.isNaN(a))return this;var l=this.number+a;if(l>Number.MAX_SAFE_INTEGER)return new e(Number.MAX_SAFE_INTEGER);if(l<Number.MIN_SAFE_INTEGER)return new e(Number.MIN_SAFE_INTEGER);var d=Math.max(Re(this.number),Re(a));return new e(l.toFixed(d))}},{key:"multi",value:function(r){var a=Number(r);if(this.isInvalidate()||Number.isNaN(a))return new e(NaN);var l=this.number*a;if(l>Number.MAX_SAFE_INTEGER)return new e(Number.MAX_SAFE_INTEGER);if(l<Number.MIN_SAFE_INTEGER)return new e(Number.MIN_SAFE_INTEGER);var d=Math.max(Re(this.number),Re(a));return new e(l.toFixed(d))}},{key:"isEmpty",value:function(){return this.empty}},{key:"isNaN",value:function(){return Number.isNaN(this.number)}},{key:"isInvalidate",value:function(){return this.isEmpty()||this.isNaN()}},{key:"equals",value:function(r){return this.toNumber()===(r==null?void 0:r.toNumber())}},{key:"lessEquals",value:function(r){return this.add(r.negate().toString()).toNumber()<=0}},{key:"toNumber",value:function(){return this.number}},{key:"toString",value:function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;return r?this.isInvalidate()?"":at(this.number):this.origin}}]),e}();function ve(e){return ht()?new kr(e):new Er(e)}function Ze(e,t,r){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:!1;if(e==="")return"";var l=De(e),d=l.negativeStr,m=l.integerStr,c=l.decimalStr,s="".concat(t).concat(c),u="".concat(d).concat(m);if(r>=0){var o=Number(c[r]);if(o>=5&&!a){var h=ve(e).add("".concat(d,"0.").concat("0".repeat(r)).concat(10-o));return Ze(h.toString(),t,r,a)}return r===0?u:"".concat(u).concat(t).concat(c.padEnd(r,"0").slice(0,r))}return s===".0"?u:"".concat(u).concat(s)}function Nr(e,t){return typeof Proxy<"u"&&e?new Proxy(e,{get:function(a,l){if(t[l])return t[l];var d=a[l];return typeof d=="function"?d.bind(a):d}}):e}function Or(e,t){var r=i.useRef(null);function a(){try{var d=e.selectionStart,m=e.selectionEnd,c=e.value,s=c.substring(0,d),u=c.substring(m);r.current={start:d,end:m,value:c,beforeTxt:s,afterTxt:u}}catch{}}function l(){if(e&&r.current&&t)try{var d=e.value,m=r.current,c=m.beforeTxt,s=m.afterTxt,u=m.start,o=d.length;if(d.startsWith(c))o=c.length;else if(d.endsWith(s))o=d.length-r.current.afterTxt.length;else{var h=c[u-1],p=d.indexOf(h,u-1);p!==-1&&(o=p+1)}e.setSelectionRange(o,o)}catch(f){$n(!1,"Something warning of cursor restore. Please fire issue about this: ".concat(f.message))}}return[a,l]}var Tr=function(){var t=i.useState(!1),r=Me(t,2),a=r[0],l=r[1];return jn(function(){l(Cn())},[]),a},Mr=200,Pr=600;function Rr(e){var t=e.prefixCls,r=e.upNode,a=e.downNode,l=e.upDisabled,d=e.downDisabled,m=e.onStep,c=i.useRef(),s=i.useRef([]),u=i.useRef();u.current=m;var o=function(){clearTimeout(c.current)},h=function(O,$){O.preventDefault(),o(),u.current($);function T(){u.current($),c.current=setTimeout(T,Mr)}c.current=setTimeout(T,Pr)};i.useEffect(function(){return function(){o(),s.current.forEach(function(w){return Je.cancel(w)})}},[]);var p=Tr();if(p)return null;var f="".concat(t,"-handler"),S=le(f,"".concat(f,"-up"),se({},"".concat(f,"-up-disabled"),l)),g=le(f,"".concat(f,"-down"),se({},"".concat(f,"-down-disabled"),d)),v=function(){return s.current.push(Je(o))},_={unselectable:"on",role:"button",onMouseUp:v,onMouseLeave:v};return i.createElement("div",{className:"".concat(f,"-wrap")},i.createElement("span",ge({},_,{onMouseDown:function(O){h(O,!0)},"aria-label":"Increase Value","aria-disabled":l,className:S}),r||i.createElement("span",{unselectable:"on",className:"".concat(t,"-handler-up-inner")})),i.createElement("span",ge({},_,{onMouseDown:function(O){h(O,!1)},"aria-label":"Decrease Value","aria-disabled":d,className:g}),a||i.createElement("span",{unselectable:"on",className:"".concat(t,"-handler-down-inner")})))}function Mt(e){var t=typeof e=="number"?at(e):De(e).fullStr,r=t.includes(".");return r?De(t.replace(/(\d)\.(\d)/g,"$1$2.")).fullStr:e+"0"}const Dr=function(){var e=i.useRef(0),t=function(){Je.cancel(e.current)};return i.useEffect(function(){return t},[]),function(r){t(),e.current=Je(function(){r()})}};var zr=["prefixCls","className","style","min","max","step","defaultValue","value","disabled","readOnly","upHandler","downHandler","keyboard","changeOnWheel","controls","classNames","stringMode","parser","formatter","precision","decimalSeparator","onChange","onInput","onPressEnter","onStep","changeOnBlur","domRef"],Ar=["disabled","style","prefixCls","value","prefix","suffix","addonBefore","addonAfter","className","classNames"],Pt=function(t,r){return t||r.isEmpty()?r.toString():r.toNumber()},Rt=function(t){var r=ve(t);return r.isInvalidate()?null:r},Fr=i.forwardRef(function(e,t){var r=e.prefixCls,a=e.className,l=e.style,d=e.min,m=e.max,c=e.step,s=c===void 0?1:c,u=e.defaultValue,o=e.value,h=e.disabled,p=e.readOnly,f=e.upHandler,S=e.downHandler,g=e.keyboard,v=e.changeOnWheel,_=v===void 0?!1:v,w=e.controls,O=w===void 0?!0:w;e.classNames;var $=e.stringMode,T=e.parser,N=e.formatter,P=e.precision,I=e.decimalSeparator,R=e.onChange,k=e.onInput,Y=e.onPressEnter,K=e.onStep,W=e.changeOnBlur,V=W===void 0?!0:W,re=e.domRef,ie=rt(e,zr),G="".concat(r,"-input"),b=i.useRef(null),y=i.useState(!1),C=Me(y,2),z=C[0],ae=C[1],q=i.useRef(!1),te=i.useRef(!1),ue=i.useRef(!1),he=i.useState(function(){return ve(o??u)}),fe=Me(he,2),H=fe[0],_e=fe[1];function Ge(D){o===void 0&&_e(D)}var Ne=i.useCallback(function(D,M){if(!M)return P>=0?P:Math.max(Re(D),Re(s))},[P,s]),x=i.useCallback(function(D){var M=String(D);if(T)return T(M);var U=M;return I&&(U=U.replace(I,".")),U.replace(/[^\w.-]+/g,"")},[T,I]),A=i.useRef(""),F=i.useCallback(function(D,M){if(N)return N(D,{userTyping:M,input:String(A.current)});var U=typeof D=="number"?at(D):D;if(!M){var B=Ne(U,M);if(bt(U)&&(I||B>=0)){var Se=I||".";U=Ze(U,Se,B)}}return U},[N,Ne,I]),Z=i.useState(function(){var D=u??o;return H.isInvalidate()&&["string","number"].includes(Ae(D))?Number.isNaN(D)?"":D:F(H.toString(),!1)}),ee=Me(Z,2),oe=ee[0],be=ee[1];A.current=oe;function me(D,M){be(F(D.isInvalidate()?D.toString(!1):D.toString(!M),M))}var de=i.useMemo(function(){return Rt(m)},[m,P]),xe=i.useMemo(function(){return Rt(d)},[d,P]),Oe=i.useMemo(function(){return!de||!H||H.isInvalidate()?!1:de.lessEquals(H)},[de,H]),ze=i.useMemo(function(){return!xe||!H||H.isInvalidate()?!1:H.lessEquals(xe)},[xe,H]),mn=Or(b.current,z),$t=Me(mn,2),gn=$t[0],hn=$t[1],jt=function(M){return de&&!M.lessEquals(de)?de:xe&&!xe.lessEquals(M)?xe:null},st=function(M){return!jt(M)},Xe=function(M,U){var B=M,Se=st(B)||B.isEmpty();if(!B.isEmpty()&&!U&&(B=jt(B)||B,Se=!0),!p&&!h&&Se){var Le=B.toString(),ct=Ne(Le,U);return ct>=0&&(B=ve(Ze(Le,".",ct)),st(B)||(B=ve(Ze(Le,".",ct,!0)))),B.equals(H)||(Ge(B),R==null||R(B.isEmpty()?null:Pt($,B)),o===void 0&&me(B,U)),B}return H},fn=Dr(),Ct=function D(M){if(gn(),A.current=M,be(M),!te.current){var U=x(M),B=ve(U);B.isNaN()||Xe(B,!0)}k==null||k(M),fn(function(){var Se=M;T||(Se=M.replace(/。/g,".")),Se!==M&&D(Se)})},_n=function(){te.current=!0},xn=function(){te.current=!1,Ct(b.current.value)},vn=function(M){Ct(M.target.value)},ot=function(M){var U;if(!(M&&Oe||!M&&ze)){q.current=!1;var B=ve(ue.current?Mt(s):s);M||(B=B.negate());var Se=(H||ve(0)).add(B.toString()),Le=Xe(Se,!1);K==null||K(Pt($,Le),{offset:ue.current?Mt(s):s,type:M?"up":"down"}),(U=b.current)===null||U===void 0||U.focus()}},It=function(M){var U=ve(x(oe)),B;U.isNaN()?B=Xe(H,M):B=Xe(U,M),o!==void 0?me(H,!1):B.isNaN()||me(B,!1)},yn=function(){q.current=!0},bn=function(M){var U=M.key,B=M.shiftKey;q.current=!0,ue.current=B,U==="Enter"&&(te.current||(q.current=!1),It(!1),Y==null||Y(M)),g!==!1&&!te.current&&["Up","ArrowUp","Down","ArrowDown"].includes(U)&&(ot(U==="Up"||U==="ArrowUp"),M.preventDefault())},Sn=function(){q.current=!1,ue.current=!1};i.useEffect(function(){if(_&&z){var D=function(B){ot(B.deltaY<0),B.preventDefault()},M=b.current;if(M)return M.addEventListener("wheel",D,{passive:!1}),function(){return M.removeEventListener("wheel",D)}}});var wn=function(){V&&It(!1),ae(!1),q.current=!1};return dt(function(){H.isInvalidate()||me(H,!1)},[P,N]),dt(function(){var D=ve(o);_e(D);var M=ve(x(oe));(!D.equals(M)||!q.current||N)&&me(D,q.current)},[o]),dt(function(){N&&hn()},[oe]),i.createElement("div",{ref:re,className:le(r,a,se(se(se(se(se({},"".concat(r,"-focused"),z),"".concat(r,"-disabled"),h),"".concat(r,"-readonly"),p),"".concat(r,"-not-a-number"),H.isNaN()),"".concat(r,"-out-of-range"),!H.isInvalidate()&&!st(H))),style:l,onFocus:function(){ae(!0)},onBlur:wn,onKeyDown:bn,onKeyUp:Sn,onCompositionStart:_n,onCompositionEnd:xn,onBeforeInput:yn},O&&i.createElement(Rr,{prefixCls:r,upNode:f,downNode:S,upDisabled:Oe,downDisabled:ze,onStep:ot}),i.createElement("div",{className:"".concat(G,"-wrap")},i.createElement("input",ge({autoComplete:"off",role:"spinbutton","aria-valuemin":d,"aria-valuemax":m,"aria-valuenow":H.isInvalidate()?null:H.toString(),step:s},ie,{ref:En(b,t),className:G,value:oe,onChange:vn,disabled:h,readOnly:p}))))}),Br=i.forwardRef(function(e,t){var r=e.disabled,a=e.style,l=e.prefixCls,d=l===void 0?"rc-input-number":l,m=e.value,c=e.prefix,s=e.suffix,u=e.addonBefore,o=e.addonAfter,h=e.className,p=e.classNames,f=rt(e,Ar),S=i.useRef(null),g=i.useRef(null),v=i.useRef(null),_=function(O){v.current&&kn(v.current,O)};return i.useImperativeHandle(t,function(){return Nr(v.current,{focus:_,nativeElement:S.current.nativeElement||g.current})}),i.createElement(In,{className:h,triggerFocus:_,prefixCls:d,value:m,disabled:r,style:a,prefix:c,suffix:s,addonAfter:o,addonBefore:u,classNames:p,components:{affixWrapper:"div",groupWrapper:"div",wrapper:"div",groupAddon:"div"},ref:S},i.createElement(Fr,ge({prefixCls:d,disabled:r,ref:v,domRef:g,className:p==null?void 0:p.input},f)))});const Vr=e=>{var t;const r=(t=e.handleVisible)!==null&&t!==void 0?t:"auto",a=e.controlHeightSM-e.lineWidth*2;return Object.assign(Object.assign({},Nn(e)),{controlWidth:90,handleWidth:a,handleFontSize:e.fontSize/2,handleVisible:r,handleActiveBg:e.colorFillAlter,handleBg:e.colorBgContainer,filledHandleBg:new xt(e.colorFillSecondary).onBackground(e.colorBgContainer).toHexString(),handleHoverColor:e.colorPrimary,handleBorderColor:e.colorBorder,handleOpacity:r===!0?1:0,handleVisibleWidth:r===!0?a:0})},Dt=({componentCls:e,borderRadiusSM:t,borderRadiusLG:r},a)=>{const l=a==="lg"?r:t;return{[`&-${a}`]:{[`${e}-handler-wrap`]:{borderStartEndRadius:l,borderEndEndRadius:l},[`${e}-handler-up`]:{borderStartEndRadius:l},[`${e}-handler-down`]:{borderEndEndRadius:l}}}},Lr=e=>{const{componentCls:t,lineWidth:r,lineType:a,borderRadius:l,inputFontSizeSM:d,inputFontSizeLG:m,controlHeightLG:c,controlHeightSM:s,colorError:u,paddingInlineSM:o,paddingBlockSM:h,paddingBlockLG:p,paddingInlineLG:f,colorIcon:S,motionDurationMid:g,handleHoverColor:v,handleOpacity:_,paddingInline:w,paddingBlock:O,handleBg:$,handleActiveBg:T,colorTextDisabled:N,borderRadiusSM:P,borderRadiusLG:I,controlWidth:R,handleBorderColor:k,filledHandleBg:Y,lineHeightLG:K,calc:W}=e;return[{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Fe(e)),Ut(e)),{display:"inline-block",width:R,margin:0,padding:0,borderRadius:l}),Mn(e,{[`${t}-handler-wrap`]:{background:$,[`${t}-handler-down`]:{borderBlockStart:`${X(r)} ${a} ${k}`}}})),Pn(e,{[`${t}-handler-wrap`]:{background:Y,[`${t}-handler-down`]:{borderBlockStart:`${X(r)} ${a} ${k}`}},"&:focus-within":{[`${t}-handler-wrap`]:{background:$}}})),Rn(e,{[`${t}-handler-wrap`]:{background:$,[`${t}-handler-down`]:{borderBlockStart:`${X(r)} ${a} ${k}`}}})),Dn(e)),{"&-rtl":{direction:"rtl",[`${t}-input`]:{direction:"rtl"}},"&-lg":{padding:0,fontSize:m,lineHeight:K,borderRadius:I,[`input${t}-input`]:{height:W(c).sub(W(r).mul(2)).equal(),padding:`${X(p)} ${X(f)}`}},"&-sm":{padding:0,fontSize:d,borderRadius:P,[`input${t}-input`]:{height:W(s).sub(W(r).mul(2)).equal(),padding:`${X(h)} ${X(o)}`}},"&-out-of-range":{[`${t}-input-wrap`]:{input:{color:u}}},"&-group":Object.assign(Object.assign(Object.assign({},Fe(e)),An(e)),{"&-wrapper":Object.assign(Object.assign(Object.assign({display:"inline-block",textAlign:"start",verticalAlign:"top",[`${t}-affix-wrapper`]:{width:"100%"},"&-lg":{[`${t}-group-addon`]:{borderRadius:I,fontSize:e.fontSizeLG}},"&-sm":{[`${t}-group-addon`]:{borderRadius:P}}},Fn(e)),Bn(e)),{[`&:not(${t}-compact-first-item):not(${t}-compact-last-item)${t}-compact-item`]:{[`${t}, ${t}-group-addon`]:{borderRadius:0}},[`&:not(${t}-compact-last-item)${t}-compact-first-item`]:{[`${t}, ${t}-group-addon`]:{borderStartEndRadius:0,borderEndEndRadius:0}},[`&:not(${t}-compact-first-item)${t}-compact-last-item`]:{[`${t}, ${t}-group-addon`]:{borderStartStartRadius:0,borderEndStartRadius:0}}})}),[`&-disabled ${t}-input`]:{cursor:"not-allowed"},[t]:{"&-input":Object.assign(Object.assign(Object.assign(Object.assign({},Fe(e)),{width:"100%",padding:`${X(O)} ${X(w)}`,textAlign:"start",backgroundColor:"transparent",border:0,borderRadius:l,outline:0,transition:`all ${g} linear`,appearance:"textfield",fontSize:"inherit"}),zn(e.colorTextPlaceholder)),{'&[type="number"]::-webkit-inner-spin-button, &[type="number"]::-webkit-outer-spin-button':{margin:0,appearance:"none"}})},[`&:hover ${t}-handler-wrap, &-focused ${t}-handler-wrap`]:{width:e.handleWidth,opacity:1}})},{[t]:Object.assign(Object.assign(Object.assign({[`${t}-handler-wrap`]:{position:"absolute",insetBlockStart:0,insetInlineEnd:0,width:e.handleVisibleWidth,opacity:_,height:"100%",borderStartStartRadius:0,borderStartEndRadius:l,borderEndEndRadius:l,borderEndStartRadius:0,display:"flex",flexDirection:"column",alignItems:"stretch",transition:`all ${g}`,overflow:"hidden",[`${t}-handler`]:{display:"flex",alignItems:"center",justifyContent:"center",flex:"auto",height:"40%",[`
              ${t}-handler-up-inner,
              ${t}-handler-down-inner
            `]:{marginInlineEnd:0,fontSize:e.handleFontSize}}},[`${t}-handler`]:{height:"50%",overflow:"hidden",color:S,fontWeight:"bold",lineHeight:0,textAlign:"center",cursor:"pointer",borderInlineStart:`${X(r)} ${a} ${k}`,transition:`all ${g} linear`,"&:active":{background:T},"&:hover":{height:"60%",[`
              ${t}-handler-up-inner,
              ${t}-handler-down-inner
            `]:{color:v}},"&-up-inner, &-down-inner":Object.assign(Object.assign({},Vn()),{color:S,transition:`all ${g} linear`,userSelect:"none"})},[`${t}-handler-up`]:{borderStartEndRadius:l},[`${t}-handler-down`]:{borderEndEndRadius:l}},Dt(e,"lg")),Dt(e,"sm")),{"&-disabled, &-readonly":{[`${t}-handler-wrap`]:{display:"none"},[`${t}-input`]:{color:"inherit"}},[`
          ${t}-handler-up-disabled,
          ${t}-handler-down-disabled
        `]:{cursor:"not-allowed"},[`
          ${t}-handler-up-disabled:hover &-handler-up-inner,
          ${t}-handler-down-disabled:hover &-handler-down-inner
        `]:{color:N}})}]},Wr=e=>{const{componentCls:t,paddingBlock:r,paddingInline:a,inputAffixPadding:l,controlWidth:d,borderRadiusLG:m,borderRadiusSM:c,paddingInlineLG:s,paddingInlineSM:u,paddingBlockLG:o,paddingBlockSM:h,motionDurationMid:p}=e;return{[`${t}-affix-wrapper`]:Object.assign(Object.assign({[`input${t}-input`]:{padding:`${X(r)} 0`}},Ut(e)),{position:"relative",display:"inline-flex",alignItems:"center",width:d,padding:0,paddingInlineStart:a,"&-lg":{borderRadius:m,paddingInlineStart:s,[`input${t}-input`]:{padding:`${X(o)} 0`}},"&-sm":{borderRadius:c,paddingInlineStart:u,[`input${t}-input`]:{padding:`${X(h)} 0`}},[`&:not(${t}-disabled):hover`]:{zIndex:1},"&-focused, &:focus":{zIndex:1},[`&-disabled > ${t}-disabled`]:{background:"transparent"},[`> div${t}`]:{width:"100%",border:"none",outline:"none",[`&${t}-focused`]:{boxShadow:"none !important"}},"&::before":{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'},[`${t}-handler-wrap`]:{zIndex:2},[t]:{position:"static",color:"inherit","&-prefix, &-suffix":{display:"flex",flex:"none",alignItems:"center",pointerEvents:"none"},"&-prefix":{marginInlineEnd:l},"&-suffix":{insetBlockStart:0,insetInlineEnd:0,height:"100%",marginInlineEnd:a,marginInlineStart:l,transition:`margin ${p}`}},[`&:hover ${t}-handler-wrap, &-focused ${t}-handler-wrap`]:{width:e.handleWidth,opacity:1},[`&:not(${t}-affix-wrapper-without-controls):hover ${t}-suffix`]:{marginInlineEnd:e.calc(e.handleWidth).add(a).equal()}})}},qr=qe("InputNumber",e=>{const t=nt(e,On(e));return[Lr(t),Wr(t),Tn(t)]},Vr,{unitless:{handleOpacity:!0}});var Hr=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,a=Object.getOwnPropertySymbols(e);l<a.length;l++)t.indexOf(a[l])<0&&Object.prototype.propertyIsEnumerable.call(e,a[l])&&(r[a[l]]=e[a[l]]);return r};const rn=i.forwardRef((e,t)=>{const{getPrefixCls:r,direction:a}=i.useContext(He),l=i.useRef(null);i.useImperativeHandle(t,()=>l.current);const{className:d,rootClassName:m,size:c,disabled:s,prefixCls:u,addonBefore:o,addonAfter:h,prefix:p,suffix:f,bordered:S,readOnly:g,status:v,controls:_,variant:w}=e,O=Hr(e,["className","rootClassName","size","disabled","prefixCls","addonBefore","addonAfter","prefix","suffix","bordered","readOnly","status","controls","variant"]),$=r("input-number",u),T=Ln($),[N,P,I]=qr($,T),{compactSize:R,compactItemClassnames:k}=Wn($,a);let Y=i.createElement(Ir,{className:`${$}-handler-up-inner`}),K=i.createElement(cr,{className:`${$}-handler-down-inner`});const W=typeof _=="boolean"?_:void 0;typeof _=="object"&&(Y=typeof _.upIcon>"u"?Y:i.createElement("span",{className:`${$}-handler-up-inner`},_.upIcon),K=typeof _.downIcon>"u"?K:i.createElement("span",{className:`${$}-handler-down-inner`},_.downIcon));const{hasFeedback:V,status:re,isFormItemInput:ie,feedbackIcon:G}=i.useContext(qn),b=Gn(re,v),y=_t(H=>{var _e;return(_e=c??R)!==null&&_e!==void 0?_e:H}),C=i.useContext(Qt),z=s??C,[ae,q]=Hn("inputNumber",w,S),te=V&&i.createElement(i.Fragment,null,G),ue=le({[`${$}-lg`]:y==="large",[`${$}-sm`]:y==="small",[`${$}-rtl`]:a==="rtl",[`${$}-in-form-item`]:ie},P),he=`${$}-group`,fe=i.createElement(Br,Object.assign({ref:l,disabled:z,className:le(I,T,d,m,k),upHandler:Y,downHandler:K,prefixCls:$,readOnly:g,controls:W,prefix:p,suffix:te||f,addonBefore:o&&i.createElement(Et,{form:!0,space:!0},o),addonAfter:h&&i.createElement(Et,{form:!0,space:!0},h),classNames:{input:ue,variant:le({[`${$}-${ae}`]:q},kt($,b,V)),affixWrapper:le({[`${$}-affix-wrapper-sm`]:y==="small",[`${$}-affix-wrapper-lg`]:y==="large",[`${$}-affix-wrapper-rtl`]:a==="rtl",[`${$}-affix-wrapper-without-controls`]:_===!1||z},P),wrapper:le({[`${he}-rtl`]:a==="rtl"},P),groupWrapper:le({[`${$}-group-wrapper-sm`]:y==="small",[`${$}-group-wrapper-lg`]:y==="large",[`${$}-group-wrapper-rtl`]:a==="rtl",[`${$}-group-wrapper-${ae}`]:q},kt(`${$}-group-wrapper`,b,V),P)}},O));return N(fe)}),J=rn,Gr=e=>i.createElement(Xn,{theme:{components:{InputNumber:{handleVisible:!0}}}},i.createElement(rn,Object.assign({},e)));J._InternalPanelDoNotUseOrYouWillBeFired=Gr;const Xr=e=>{const{componentCls:t,iconCls:r,antCls:a,zIndexPopup:l,colorText:d,colorWarning:m,marginXXS:c,marginXS:s,fontSize:u,fontWeightStrong:o,colorTextHeading:h}=e;return{[t]:{zIndex:l,[`&${a}-popover`]:{fontSize:u},[`${t}-message`]:{marginBottom:s,display:"flex",flexWrap:"nowrap",alignItems:"start",[`> ${t}-message-icon ${r}`]:{color:m,fontSize:u,lineHeight:1,marginInlineEnd:s},[`${t}-title`]:{fontWeight:o,color:h,"&:only-child":{fontWeight:"normal"}},[`${t}-description`]:{marginTop:c,color:d}},[`${t}-buttons`]:{textAlign:"end",whiteSpace:"nowrap",button:{marginInlineStart:s}}}}},Kr=e=>{const{zIndexPopupBase:t}=e;return{zIndexPopup:t+60}},an=qe("Popconfirm",e=>Xr(e),Kr,{resetStyle:!1});var Ur=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,a=Object.getOwnPropertySymbols(e);l<a.length;l++)t.indexOf(a[l])<0&&Object.prototype.propertyIsEnumerable.call(e,a[l])&&(r[a[l]]=e[a[l]]);return r};const ln=e=>{const{prefixCls:t,okButtonProps:r,cancelButtonProps:a,title:l,description:d,cancelText:m,okText:c,okType:s="primary",icon:u=i.createElement(tn,null),showCancel:o=!0,close:h,onConfirm:p,onCancel:f,onPopupClick:S}=e,{getPrefixCls:g}=i.useContext(He),[v]=Kn("Popconfirm",Un.Popconfirm),_=Nt(l),w=Nt(d);return i.createElement("div",{className:`${t}-inner-content`,onClick:S},i.createElement("div",{className:`${t}-message`},u&&i.createElement("span",{className:`${t}-message-icon`},u),i.createElement("div",{className:`${t}-message-text`},_&&i.createElement("div",{className:`${t}-title`},_),w&&i.createElement("div",{className:`${t}-description`},w))),i.createElement("div",{className:`${t}-buttons`},o&&i.createElement(ce,Object.assign({onClick:f,size:"small"},a),m||(v==null?void 0:v.cancelText)),i.createElement(gr,{buttonProps:Object.assign(Object.assign({size:"small"},Qn(s)),r),actionFn:p,close:h,prefixCls:g("btn"),quitOnNullishReturnValue:!0,emitEvent:!0},c||(v==null?void 0:v.okText))))},Qr=e=>{const{prefixCls:t,placement:r,className:a,style:l}=e,d=Ur(e,["prefixCls","placement","className","style"]),{getPrefixCls:m}=i.useContext(He),c=m("popconfirm",t),[s]=an(c);return s(i.createElement(Yn,{placement:r,className:le(c,a),style:l,content:i.createElement(ln,Object.assign({prefixCls:c},d))}))};var Yr=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,a=Object.getOwnPropertySymbols(e);l<a.length;l++)t.indexOf(a[l])<0&&Object.prototype.propertyIsEnumerable.call(e,a[l])&&(r[a[l]]=e[a[l]]);return r};const Zr=i.forwardRef((e,t)=>{var r,a;const{prefixCls:l,placement:d="top",trigger:m="click",okType:c="primary",icon:s=i.createElement(tn,null),children:u,overlayClassName:o,onOpenChange:h,onVisibleChange:p,overlayStyle:f,styles:S,classNames:g}=e,v=Yr(e,["prefixCls","placement","trigger","okType","icon","children","overlayClassName","onOpenChange","onVisibleChange","overlayStyle","styles","classNames"]),{getPrefixCls:_,className:w,style:O,classNames:$,styles:T}=Gt("popconfirm"),[N,P]=vt(!1,{value:(r=e.open)!==null&&r!==void 0?r:e.visible,defaultValue:(a=e.defaultOpen)!==null&&a!==void 0?a:e.defaultVisible}),I=(G,b)=>{P(G,!0),p==null||p(G),h==null||h(G,b)},R=G=>{I(!1,G)},k=G=>{var b;return(b=e.onConfirm)===null||b===void 0?void 0:b.call(void 0,G)},Y=G=>{var b;I(!1,G),(b=e.onCancel)===null||b===void 0||b.call(void 0,G)},K=(G,b)=>{const{disabled:y=!1}=e;y||I(G,b)},W=_("popconfirm",l),V=le(W,w,o,$.root,g==null?void 0:g.root),re=le($.body,g==null?void 0:g.body),[ie]=an(W);return ie(i.createElement(Zn,Object.assign({},Yt(v,["title"]),{trigger:m,placement:d,onOpenChange:K,open:N,ref:t,classNames:{root:V,body:re},styles:{root:Object.assign(Object.assign(Object.assign(Object.assign({},T.root),O),f),S==null?void 0:S.root),body:Object.assign(Object.assign({},T.body),S==null?void 0:S.body)},content:i.createElement(ln,Object.assign({okType:c,icon:s},e,{prefixCls:W,close:R,onConfirm:k,onCancel:Y})),"data-popover-inject":!0}),u))}),St=Zr;St._InternalPanelDoNotUseOrYouWillBeFired=Qr;var Jr={percent:0,prefixCls:"rc-progress",strokeColor:"#2db7f5",strokeLinecap:"round",strokeWidth:1,trailColor:"#D9D9D9",trailWidth:1,gapPosition:"bottom"},ea=function(){var t=i.useRef([]),r=i.useRef(null);return i.useEffect(function(){var a=Date.now(),l=!1;t.current.forEach(function(d){if(d){l=!0;var m=d.style;m.transitionDuration=".3s, .3s, .3s, .06s",r.current&&a-r.current<100&&(m.transitionDuration="0s, 0s")}}),l&&(r.current=Date.now())}),t.current},zt=0,ta=Jn();function na(){var e;return ta?(e=zt,zt+=1):e="TEST_OR_SSR",e}const ra=function(e){var t=i.useState(),r=Me(t,2),a=r[0],l=r[1];return i.useEffect(function(){l("rc_progress_".concat(na()))},[]),e||a};var At=function(t){var r=t.bg,a=t.children;return i.createElement("div",{style:{width:"100%",height:"100%",background:r}},a)};function Ft(e,t){return Object.keys(e).map(function(r){var a=parseFloat(r),l="".concat(Math.floor(a*t),"%");return"".concat(e[r]," ").concat(l)})}var aa=i.forwardRef(function(e,t){var r=e.prefixCls,a=e.color,l=e.gradientId,d=e.radius,m=e.style,c=e.ptg,s=e.strokeLinecap,u=e.strokeWidth,o=e.size,h=e.gapDegree,p=a&&Ae(a)==="object",f=p?"#FFF":void 0,S=o/2,g=i.createElement("circle",{className:"".concat(r,"-circle-path"),r:d,cx:S,cy:S,stroke:f,strokeLinecap:s,strokeWidth:u,opacity:c===0?0:1,style:m,ref:t});if(!p)return g;var v="".concat(l,"-conic"),_=h?"".concat(180+h/2,"deg"):"0deg",w=Ft(a,(360-h)/360),O=Ft(a,1),$="conic-gradient(from ".concat(_,", ").concat(w.join(", "),")"),T="linear-gradient(to ".concat(h?"bottom":"top",", ").concat(O.join(", "),")");return i.createElement(i.Fragment,null,i.createElement("mask",{id:v},g),i.createElement("foreignObject",{x:0,y:0,width:o,height:o,mask:"url(#".concat(v,")")},i.createElement(At,{bg:T},i.createElement(At,{bg:$}))))}),We=100,ut=function(t,r,a,l,d,m,c,s,u,o){var h=arguments.length>10&&arguments[10]!==void 0?arguments[10]:0,p=a/100*360*((360-m)/360),f=m===0?0:{bottom:0,top:180,left:90,right:-90}[c],S=(100-l)/100*r;u==="round"&&l!==100&&(S+=o/2,S>=r&&(S=r-.01));var g=We/2;return{stroke:typeof s=="string"?s:void 0,strokeDasharray:"".concat(r,"px ").concat(t),strokeDashoffset:S+h,transform:"rotate(".concat(d+p+f,"deg)"),transformOrigin:"".concat(g,"px ").concat(g,"px"),transition:"stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s, opacity .3s ease 0s",fillOpacity:0}},la=["id","prefixCls","steps","strokeWidth","trailWidth","gapDegree","gapPosition","trailColor","strokeLinecap","style","className","strokeColor","percent"];function Bt(e){var t=e??[];return Array.isArray(t)?t:[t]}var ia=function(t){var r=Ot(Ot({},Jr),t),a=r.id,l=r.prefixCls,d=r.steps,m=r.strokeWidth,c=r.trailWidth,s=r.gapDegree,u=s===void 0?0:s,o=r.gapPosition,h=r.trailColor,p=r.strokeLinecap,f=r.style,S=r.className,g=r.strokeColor,v=r.percent,_=rt(r,la),w=We/2,O=ra(a),$="".concat(O,"-gradient"),T=w-m/2,N=Math.PI*2*T,P=u>0?90+u/2:-90,I=N*((360-u)/360),R=Ae(d)==="object"?d:{count:d,gap:2},k=R.count,Y=R.gap,K=Bt(v),W=Bt(g),V=W.find(function(z){return z&&Ae(z)==="object"}),re=V&&Ae(V)==="object",ie=re?"butt":p,G=ut(N,I,0,100,P,u,o,h,ie,m),b=ea(),y=function(){var ae=0;return K.map(function(q,te){var ue=W[te]||W[W.length-1],he=ut(N,I,ae,q,P,u,o,ue,ie,m);return ae+=q,i.createElement(aa,{key:te,color:ue,ptg:q,radius:T,prefixCls:l,gradientId:$,style:he,strokeLinecap:ie,strokeWidth:m,gapDegree:u,ref:function(H){b[te]=H},size:We})}).reverse()},C=function(){var ae=Math.round(k*(K[0]/100)),q=100/k,te=0;return new Array(k).fill(null).map(function(ue,he){var fe=he<=ae-1?W[0]:h,H=fe&&Ae(fe)==="object"?"url(#".concat($,")"):void 0,_e=ut(N,I,te,q,P,u,o,fe,"butt",m,Y);return te+=(I-_e.strokeDashoffset+Y)*100/I,i.createElement("circle",{key:he,className:"".concat(l,"-circle-path"),r:T,cx:w,cy:w,stroke:H,strokeWidth:m,opacity:1,style:_e,ref:function(Ne){b[he]=Ne}})})};return i.createElement("svg",ge({className:le("".concat(l,"-circle"),S),viewBox:"0 0 ".concat(We," ").concat(We),style:f,id:a,role:"presentation"},_),!k&&i.createElement("circle",{className:"".concat(l,"-circle-trail"),r:T,cx:w,cy:w,stroke:h,strokeLinecap:ie,strokeWidth:c||m,style:G}),k?C():y())};function Ee(e){return!e||e<0?0:e>100?100:e}function et({success:e,successPercent:t}){let r=t;return e&&"progress"in e&&(r=e.progress),e&&"percent"in e&&(r=e.percent),r}const sa=({percent:e,success:t,successPercent:r})=>{const a=Ee(et({success:t,successPercent:r}));return[a,Ee(Ee(e)-a)]},oa=({success:e={},strokeColor:t})=>{const{strokeColor:r}=e;return[r||mt.green,t||null]},lt=(e,t,r)=>{var a,l,d,m;let c=-1,s=-1;if(t==="step"){const u=r.steps,o=r.strokeWidth;typeof e=="string"||typeof e>"u"?(c=e==="small"?2:14,s=o??8):typeof e=="number"?[c,s]=[e,e]:[c=14,s=8]=Array.isArray(e)?e:[e.width,e.height],c*=u}else if(t==="line"){const u=r==null?void 0:r.strokeWidth;typeof e=="string"||typeof e>"u"?s=u||(e==="small"?6:8):typeof e=="number"?[c,s]=[e,e]:[c=-1,s=8]=Array.isArray(e)?e:[e.width,e.height]}else(t==="circle"||t==="dashboard")&&(typeof e=="string"||typeof e>"u"?[c,s]=e==="small"?[60,60]:[120,120]:typeof e=="number"?[c,s]=[e,e]:Array.isArray(e)&&(c=(l=(a=e[0])!==null&&a!==void 0?a:e[1])!==null&&l!==void 0?l:120,s=(m=(d=e[0])!==null&&d!==void 0?d:e[1])!==null&&m!==void 0?m:120));return[c,s]},ca=3,da=e=>ca/e*100,ua=e=>{const{prefixCls:t,trailColor:r=null,strokeLinecap:a="round",gapPosition:l,gapDegree:d,width:m=120,type:c,children:s,success:u,size:o=m,steps:h}=e,[p,f]=lt(o,"circle");let{strokeWidth:S}=e;S===void 0&&(S=Math.max(da(p),6));const g={width:p,height:f,fontSize:p*.15+6},v=i.useMemo(()=>{if(d||d===0)return d;if(c==="dashboard")return 75},[d,c]),_=sa(e),w=l||c==="dashboard"&&"bottom"||void 0,O=Object.prototype.toString.call(e.strokeColor)==="[object Object]",$=oa({success:u,strokeColor:e.strokeColor}),T=le(`${t}-inner`,{[`${t}-circle-gradient`]:O}),N=i.createElement(ia,{steps:h,percent:h?_[1]:_,strokeWidth:S,trailWidth:S,strokeColor:h?$[1]:$,strokeLinecap:a,trailColor:r,prefixCls:t,gapDegree:v,gapPosition:w}),P=p<=20,I=i.createElement("div",{className:T,style:g},N,!P&&s);return P?i.createElement(Ye,{title:s},I):I},tt="--progress-line-stroke-color",sn="--progress-percent",Vt=e=>{const t=e?"100%":"-100%";return new er(`antProgress${e?"RTL":"LTR"}Active`,{"0%":{transform:`translateX(${t}) scaleX(0)`,opacity:.1},"20%":{transform:`translateX(${t}) scaleX(0)`,opacity:.5},to:{transform:"translateX(0) scaleX(1)",opacity:0}})},pa=e=>{const{componentCls:t,iconCls:r}=e;return{[t]:Object.assign(Object.assign({},Fe(e)),{display:"inline-block","&-rtl":{direction:"rtl"},"&-line":{position:"relative",width:"100%",fontSize:e.fontSize},[`${t}-outer`]:{display:"inline-flex",alignItems:"center",width:"100%"},[`${t}-inner`]:{position:"relative",display:"inline-block",width:"100%",flex:1,overflow:"hidden",verticalAlign:"middle",backgroundColor:e.remainingColor,borderRadius:e.lineBorderRadius},[`${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.defaultColor}},[`${t}-success-bg, ${t}-bg`]:{position:"relative",background:e.defaultColor,borderRadius:e.lineBorderRadius,transition:`all ${e.motionDurationSlow} ${e.motionEaseInOutCirc}`},[`${t}-layout-bottom`]:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",[`${t}-text`]:{width:"max-content",marginInlineStart:0,marginTop:e.marginXXS}},[`${t}-bg`]:{overflow:"hidden","&::after":{content:'""',background:{_multi_value_:!0,value:["inherit",`var(${tt})`]},height:"100%",width:`calc(1 / var(${sn}) * 100%)`,display:"block"},[`&${t}-bg-inner`]:{minWidth:"max-content","&::after":{content:"none"},[`${t}-text-inner`]:{color:e.colorWhite,[`&${t}-text-bright`]:{color:"rgba(0, 0, 0, 0.45)"}}}},[`${t}-success-bg`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,backgroundColor:e.colorSuccess},[`${t}-text`]:{display:"inline-block",marginInlineStart:e.marginXS,color:e.colorText,lineHeight:1,width:"2em",whiteSpace:"nowrap",textAlign:"start",verticalAlign:"middle",wordBreak:"normal",[r]:{fontSize:e.fontSize},[`&${t}-text-outer`]:{width:"max-content"},[`&${t}-text-outer${t}-text-start`]:{width:"max-content",marginInlineStart:0,marginInlineEnd:e.marginXS}},[`${t}-text-inner`]:{display:"flex",justifyContent:"center",alignItems:"center",width:"100%",height:"100%",marginInlineStart:0,padding:`0 ${X(e.paddingXXS)}`,[`&${t}-text-start`]:{justifyContent:"start"},[`&${t}-text-end`]:{justifyContent:"end"}},[`&${t}-status-active`]:{[`${t}-bg::before`]:{position:"absolute",inset:0,backgroundColor:e.colorBgContainer,borderRadius:e.lineBorderRadius,opacity:0,animationName:Vt(),animationDuration:e.progressActiveMotionDuration,animationTimingFunction:e.motionEaseOutQuint,animationIterationCount:"infinite",content:'""'}},[`&${t}-rtl${t}-status-active`]:{[`${t}-bg::before`]:{animationName:Vt(!0)}},[`&${t}-status-exception`]:{[`${t}-bg`]:{backgroundColor:e.colorError},[`${t}-text`]:{color:e.colorError}},[`&${t}-status-exception ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorError}},[`&${t}-status-success`]:{[`${t}-bg`]:{backgroundColor:e.colorSuccess},[`${t}-text`]:{color:e.colorSuccess}},[`&${t}-status-success ${t}-inner:not(${t}-circle-gradient)`]:{[`${t}-circle-path`]:{stroke:e.colorSuccess}}})}},ma=e=>{const{componentCls:t,iconCls:r}=e;return{[t]:{[`${t}-circle-trail`]:{stroke:e.remainingColor},[`&${t}-circle ${t}-inner`]:{position:"relative",lineHeight:1,backgroundColor:"transparent"},[`&${t}-circle ${t}-text`]:{position:"absolute",insetBlockStart:"50%",insetInlineStart:0,width:"100%",margin:0,padding:0,color:e.circleTextColor,fontSize:e.circleTextFontSize,lineHeight:1,whiteSpace:"normal",textAlign:"center",transform:"translateY(-50%)",[r]:{fontSize:e.circleIconFontSize}},[`${t}-circle&-status-exception`]:{[`${t}-text`]:{color:e.colorError}},[`${t}-circle&-status-success`]:{[`${t}-text`]:{color:e.colorSuccess}}},[`${t}-inline-circle`]:{lineHeight:1,[`${t}-inner`]:{verticalAlign:"bottom"}}}},ga=e=>{const{componentCls:t}=e;return{[t]:{[`${t}-steps`]:{display:"inline-block","&-outer":{display:"flex",flexDirection:"row",alignItems:"center"},"&-item":{flexShrink:0,minWidth:e.progressStepMinWidth,marginInlineEnd:e.progressStepMarginInlineEnd,backgroundColor:e.remainingColor,transition:`all ${e.motionDurationSlow}`,"&-active":{backgroundColor:e.defaultColor}}}}}},ha=e=>{const{componentCls:t,iconCls:r}=e;return{[t]:{[`${t}-small&-line, ${t}-small&-line ${t}-text ${r}`]:{fontSize:e.fontSizeSM}}}},fa=e=>({circleTextColor:e.colorText,defaultColor:e.colorInfo,remainingColor:e.colorFillSecondary,lineBorderRadius:100,circleTextFontSize:"1em",circleIconFontSize:`${e.fontSize/e.fontSizeSM}em`}),_a=qe("Progress",e=>{const t=e.calc(e.marginXXS).div(2).equal(),r=nt(e,{progressStepMarginInlineEnd:t,progressStepMinWidth:t,progressActiveMotionDuration:"2.4s"});return[pa(r),ma(r),ga(r),ha(r)]},fa);var xa=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,a=Object.getOwnPropertySymbols(e);l<a.length;l++)t.indexOf(a[l])<0&&Object.prototype.propertyIsEnumerable.call(e,a[l])&&(r[a[l]]=e[a[l]]);return r};const va=e=>{let t=[];return Object.keys(e).forEach(r=>{const a=parseFloat(r.replace(/%/g,""));Number.isNaN(a)||t.push({key:a,value:e[r]})}),t=t.sort((r,a)=>r.key-a.key),t.map(({key:r,value:a})=>`${a} ${r}%`).join(", ")},ya=(e,t)=>{const{from:r=mt.blue,to:a=mt.blue,direction:l=t==="rtl"?"to left":"to right"}=e,d=xa(e,["from","to","direction"]);if(Object.keys(d).length!==0){const c=va(d),s=`linear-gradient(${l}, ${c})`;return{background:s,[tt]:s}}const m=`linear-gradient(${l}, ${r}, ${a})`;return{background:m,[tt]:m}},ba=e=>{const{prefixCls:t,direction:r,percent:a,size:l,strokeWidth:d,strokeColor:m,strokeLinecap:c="round",children:s,trailColor:u=null,percentPosition:o,success:h}=e,{align:p,type:f}=o,S=m&&typeof m!="string"?ya(m,r):{[tt]:m,background:m},g=c==="square"||c==="butt"?0:void 0,v=l??[-1,d||(l==="small"?6:8)],[_,w]=lt(v,"line",{strokeWidth:d}),O={backgroundColor:u||void 0,borderRadius:g},$=Object.assign(Object.assign({width:`${Ee(a)}%`,height:w,borderRadius:g},S),{[sn]:Ee(a)/100}),T=et(e),N={width:`${Ee(T)}%`,height:w,borderRadius:g,backgroundColor:h==null?void 0:h.strokeColor},P={width:_<0?"100%":_},I=i.createElement("div",{className:`${t}-inner`,style:O},i.createElement("div",{className:le(`${t}-bg`,`${t}-bg-${f}`),style:$},f==="inner"&&s),T!==void 0&&i.createElement("div",{className:`${t}-success-bg`,style:N})),R=f==="outer"&&p==="start",k=f==="outer"&&p==="end";return f==="outer"&&p==="center"?i.createElement("div",{className:`${t}-layout-bottom`},I,s):i.createElement("div",{className:`${t}-outer`,style:P},R&&s,I,k&&s)},Sa=e=>{const{size:t,steps:r,rounding:a=Math.round,percent:l=0,strokeWidth:d=8,strokeColor:m,trailColor:c=null,prefixCls:s,children:u}=e,o=a(r*(l/100)),p=t??[t==="small"?2:14,d],[f,S]=lt(p,"step",{steps:r,strokeWidth:d}),g=f/r,v=Array.from({length:r});for(let _=0;_<r;_++){const w=Array.isArray(m)?m[_]:m;v[_]=i.createElement("div",{key:_,className:le(`${s}-steps-item`,{[`${s}-steps-item-active`]:_<=o-1}),style:{backgroundColor:_<=o-1?w:c,width:g,height:S}})}return i.createElement("div",{className:`${s}-steps-outer`},v,u)};var wa=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,a=Object.getOwnPropertySymbols(e);l<a.length;l++)t.indexOf(a[l])<0&&Object.prototype.propertyIsEnumerable.call(e,a[l])&&(r[a[l]]=e[a[l]]);return r};const $a=["normal","exception","active","success"],ja=i.forwardRef((e,t)=>{const{prefixCls:r,className:a,rootClassName:l,steps:d,strokeColor:m,percent:c=0,size:s="default",showInfo:u=!0,type:o="line",status:h,format:p,style:f,percentPosition:S={}}=e,g=wa(e,["prefixCls","className","rootClassName","steps","strokeColor","percent","size","showInfo","type","status","format","style","percentPosition"]),{align:v="end",type:_="outer"}=S,w=Array.isArray(m)?m[0]:m,O=typeof m=="string"||Array.isArray(m)?m:void 0,$=i.useMemo(()=>{if(w){const y=typeof w=="string"?w:Object.values(w)[0];return new xt(y).isLight()}return!1},[m]),T=i.useMemo(()=>{var y,C;const z=et(e);return parseInt(z!==void 0?(y=z??0)===null||y===void 0?void 0:y.toString():(C=c??0)===null||C===void 0?void 0:C.toString(),10)},[c,e.success,e.successPercent]),N=i.useMemo(()=>!$a.includes(h)&&T>=100?"success":h||"normal",[h,T]),{getPrefixCls:P,direction:I,progress:R}=i.useContext(He),k=P("progress",r),[Y,K,W]=_a(k),V=o==="line",re=V&&!d,ie=i.useMemo(()=>{if(!u)return null;const y=et(e);let C;const z=p||(q=>`${q}%`),ae=V&&$&&_==="inner";return _==="inner"||p||N!=="exception"&&N!=="success"?C=z(Ee(c),Ee(y)):N==="exception"?C=V?i.createElement(tr,null):i.createElement(hr,null):N==="success"&&(C=V?i.createElement(fr,null):i.createElement(nr,null)),i.createElement("span",{className:le(`${k}-text`,{[`${k}-text-bright`]:ae,[`${k}-text-${v}`]:re,[`${k}-text-${_}`]:re}),title:typeof C=="string"?C:void 0},C)},[u,c,T,N,o,k,p]);let G;o==="line"?G=d?i.createElement(Sa,Object.assign({},e,{strokeColor:O,prefixCls:k,steps:typeof d=="object"?d.count:d}),ie):i.createElement(ba,Object.assign({},e,{strokeColor:w,prefixCls:k,direction:I,percentPosition:{align:v,type:_}}),ie):(o==="circle"||o==="dashboard")&&(G=i.createElement(ua,Object.assign({},e,{strokeColor:w,prefixCls:k,progressStatus:N}),ie));const b=le(k,`${k}-status-${N}`,{[`${k}-${o==="dashboard"&&"circle"||o}`]:o!=="line",[`${k}-inline-circle`]:o==="circle"&&lt(s,"circle")[0]<=20,[`${k}-line`]:re,[`${k}-line-align-${v}`]:re,[`${k}-line-position-${_}`]:re,[`${k}-steps`]:d,[`${k}-show-info`]:u,[`${k}-${s}`]:typeof s=="string",[`${k}-rtl`]:I==="rtl"},R==null?void 0:R.className,a,l,K,W);return Y(i.createElement("div",Object.assign({ref:t,style:Object.assign(Object.assign({},R==null?void 0:R.style),f),className:b,role:"progressbar","aria-valuenow":T,"aria-valuemin":0,"aria-valuemax":100},Yt(g,["trailColor","strokeWidth","width","gapDegree","gapPosition","strokeLinecap","success","successPercent"])),G))});var Ca={icon:{tag:"svg",attrs:{"fill-rule":"evenodd",viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 912H144c-17.7 0-32-14.3-32-32V144c0-17.7 14.3-32 32-32h360c4.4 0 8 3.6 8 8v56c0 4.4-3.6 8-8 8H184v656h656V520c0-4.4 3.6-8 8-8h56c4.4 0 8 3.6 8 8v360c0 17.7-14.3 32-32 32zM653.3 424.6l52.2 52.2a8.01 8.01 0 01-4.7 13.6l-179.4 21c-5.1.6-9.5-3.7-8.9-8.9l21-179.4c.8-6.6 8.9-9.4 13.6-4.7l52.4 52.4 256.2-256.2c3.1-3.1 8.2-3.1 11.3 0l42.4 42.4c3.1 3.1 3.1 8.2 0 11.3L653.3 424.6z"}}]},name:"import",theme:"outlined"},Ia=function(t,r){return i.createElement(Ve,ge({},t,{ref:r,icon:Ca}))},ka=i.forwardRef(Ia),Ea={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm-88-532h-48c-4.4 0-8 3.6-8 8v304c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V360c0-4.4-3.6-8-8-8zm224 0h-48c-4.4 0-8 3.6-8 8v304c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V360c0-4.4-3.6-8-8-8z"}}]},name:"pause-circle",theme:"outlined"},Na=function(t,r){return i.createElement(Ve,ge({},t,{ref:r,icon:Ea}))},Oa=i.forwardRef(Na),Ta={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M719.4 499.1l-296.1-215A15.9 15.9 0 00398 297v430c0 13.1 14.8 20.5 25.3 12.9l296.1-215a15.9 15.9 0 000-25.8zm-257.6 134V390.9L628.5 512 461.8 633.1z"}}]},name:"play-circle",theme:"outlined"},Ma=function(t,r){return i.createElement(Ve,ge({},t,{ref:r,icon:Ta}))},on=i.forwardRef(Ma),Pa={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M893.3 293.3L730.7 130.7c-7.5-7.5-16.7-13-26.7-16V112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V338.5c0-17-6.7-33.2-18.7-45.2zM384 184h256v104H384V184zm456 656H184V184h136v136c0 17.7 14.3 32 32 32h320c17.7 0 32-14.3 32-32V205.8l136 136V840zM512 442c-79.5 0-144 64.5-144 144s64.5 144 144 144 144-64.5 144-144-64.5-144-144-144zm0 224c-44.2 0-80-35.8-80-80s35.8-80 80-80 80 35.8 80 80-35.8 80-80 80z"}}]},name:"save",theme:"outlined"},Ra=function(t,r){return i.createElement(Ve,ge({},t,{ref:r,icon:Pa}))},Da=i.forwardRef(Ra),za={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"}}]},name:"setting",theme:"outlined"},Aa=function(t,r){return i.createElement(Ve,ge({},t,{ref:r,icon:za}))},Fa=i.forwardRef(Aa),Ba=["prefixCls","className","checked","defaultChecked","disabled","loadingIcon","checkedChildren","unCheckedChildren","onClick","onChange","onKeyDown"],cn=i.forwardRef(function(e,t){var r,a=e.prefixCls,l=a===void 0?"rc-switch":a,d=e.className,m=e.checked,c=e.defaultChecked,s=e.disabled,u=e.loadingIcon,o=e.checkedChildren,h=e.unCheckedChildren,p=e.onClick,f=e.onChange,S=e.onKeyDown,g=rt(e,Ba),v=vt(!1,{value:m,defaultValue:c}),_=Me(v,2),w=_[0],O=_[1];function $(I,R){var k=w;return s||(k=I,O(k),f==null||f(k,R)),k}function T(I){I.which===Tt.LEFT?$(!1,I):I.which===Tt.RIGHT&&$(!0,I),S==null||S(I)}function N(I){var R=$(!w,I);p==null||p(R,I)}var P=le(l,d,(r={},se(r,"".concat(l,"-checked"),w),se(r,"".concat(l,"-disabled"),s),r));return i.createElement("button",ge({},g,{type:"button",role:"switch","aria-checked":w,disabled:s,className:P,ref:t,onKeyDown:T,onClick:N}),u,i.createElement("span",{className:"".concat(l,"-inner")},i.createElement("span",{className:"".concat(l,"-inner-checked")},o),i.createElement("span",{className:"".concat(l,"-inner-unchecked")},h)))});cn.displayName="Switch";const Va=e=>{const{componentCls:t,trackHeightSM:r,trackPadding:a,trackMinWidthSM:l,innerMinMarginSM:d,innerMaxMarginSM:m,handleSizeSM:c,calc:s}=e,u=`${t}-inner`,o=X(s(c).add(s(a).mul(2)).equal()),h=X(s(m).mul(2).equal());return{[t]:{[`&${t}-small`]:{minWidth:l,height:r,lineHeight:X(r),[`${t}-inner`]:{paddingInlineStart:m,paddingInlineEnd:d,[`${u}-checked, ${u}-unchecked`]:{minHeight:r},[`${u}-checked`]:{marginInlineStart:`calc(-100% + ${o} - ${h})`,marginInlineEnd:`calc(100% - ${o} + ${h})`},[`${u}-unchecked`]:{marginTop:s(r).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`${t}-handle`]:{width:c,height:c},[`${t}-loading-icon`]:{top:s(s(c).sub(e.switchLoadingIconSize)).div(2).equal(),fontSize:e.switchLoadingIconSize},[`&${t}-checked`]:{[`${t}-inner`]:{paddingInlineStart:d,paddingInlineEnd:m,[`${u}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${u}-unchecked`]:{marginInlineStart:`calc(100% - ${o} + ${h})`,marginInlineEnd:`calc(-100% + ${o} - ${h})`}},[`${t}-handle`]:{insetInlineStart:`calc(100% - ${X(s(c).add(a).equal())})`}},[`&:not(${t}-disabled):active`]:{[`&:not(${t}-checked) ${u}`]:{[`${u}-unchecked`]:{marginInlineStart:s(e.marginXXS).div(2).equal(),marginInlineEnd:s(e.marginXXS).mul(-1).div(2).equal()}},[`&${t}-checked ${u}`]:{[`${u}-checked`]:{marginInlineStart:s(e.marginXXS).mul(-1).div(2).equal(),marginInlineEnd:s(e.marginXXS).div(2).equal()}}}}}}},La=e=>{const{componentCls:t,handleSize:r,calc:a}=e;return{[t]:{[`${t}-loading-icon${e.iconCls}`]:{position:"relative",top:a(a(r).sub(e.fontSize)).div(2).equal(),color:e.switchLoadingIconColor,verticalAlign:"top"},[`&${t}-checked ${t}-loading-icon`]:{color:e.switchColor}}}},Wa=e=>{const{componentCls:t,trackPadding:r,handleBg:a,handleShadow:l,handleSize:d,calc:m}=e,c=`${t}-handle`;return{[t]:{[c]:{position:"absolute",top:r,insetInlineStart:r,width:d,height:d,transition:`all ${e.switchDuration} ease-in-out`,"&::before":{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,backgroundColor:a,borderRadius:m(d).div(2).equal(),boxShadow:l,transition:`all ${e.switchDuration} ease-in-out`,content:'""'}},[`&${t}-checked ${c}`]:{insetInlineStart:`calc(100% - ${X(m(d).add(r).equal())})`},[`&:not(${t}-disabled):active`]:{[`${c}::before`]:{insetInlineEnd:e.switchHandleActiveInset,insetInlineStart:0},[`&${t}-checked ${c}::before`]:{insetInlineEnd:0,insetInlineStart:e.switchHandleActiveInset}}}}},qa=e=>{const{componentCls:t,trackHeight:r,trackPadding:a,innerMinMargin:l,innerMaxMargin:d,handleSize:m,calc:c}=e,s=`${t}-inner`,u=X(c(m).add(c(a).mul(2)).equal()),o=X(c(d).mul(2).equal());return{[t]:{[s]:{display:"block",overflow:"hidden",borderRadius:100,height:"100%",paddingInlineStart:d,paddingInlineEnd:l,transition:`padding-inline-start ${e.switchDuration} ease-in-out, padding-inline-end ${e.switchDuration} ease-in-out`,[`${s}-checked, ${s}-unchecked`]:{display:"block",color:e.colorTextLightSolid,fontSize:e.fontSizeSM,transition:`margin-inline-start ${e.switchDuration} ease-in-out, margin-inline-end ${e.switchDuration} ease-in-out`,pointerEvents:"none",minHeight:r},[`${s}-checked`]:{marginInlineStart:`calc(-100% + ${u} - ${o})`,marginInlineEnd:`calc(100% - ${u} + ${o})`},[`${s}-unchecked`]:{marginTop:c(r).mul(-1).equal(),marginInlineStart:0,marginInlineEnd:0}},[`&${t}-checked ${s}`]:{paddingInlineStart:l,paddingInlineEnd:d,[`${s}-checked`]:{marginInlineStart:0,marginInlineEnd:0},[`${s}-unchecked`]:{marginInlineStart:`calc(100% - ${u} + ${o})`,marginInlineEnd:`calc(-100% + ${u} - ${o})`}},[`&:not(${t}-disabled):active`]:{[`&:not(${t}-checked) ${s}`]:{[`${s}-unchecked`]:{marginInlineStart:c(a).mul(2).equal(),marginInlineEnd:c(a).mul(-1).mul(2).equal()}},[`&${t}-checked ${s}`]:{[`${s}-checked`]:{marginInlineStart:c(a).mul(-1).mul(2).equal(),marginInlineEnd:c(a).mul(2).equal()}}}}}},Ha=e=>{const{componentCls:t,trackHeight:r,trackMinWidth:a}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign({},Fe(e)),{position:"relative",display:"inline-block",boxSizing:"border-box",minWidth:a,height:r,lineHeight:X(r),verticalAlign:"middle",background:e.colorTextQuaternary,border:"0",borderRadius:100,cursor:"pointer",transition:`all ${e.motionDurationMid}`,userSelect:"none",[`&:hover:not(${t}-disabled)`]:{background:e.colorTextTertiary}}),rr(e)),{[`&${t}-checked`]:{background:e.switchColor,[`&:hover:not(${t}-disabled)`]:{background:e.colorPrimaryHover}},[`&${t}-loading, &${t}-disabled`]:{cursor:"not-allowed",opacity:e.switchDisabledOpacity,"*":{boxShadow:"none",cursor:"not-allowed"}},[`&${t}-rtl`]:{direction:"rtl"}})}},Ga=e=>{const{fontSize:t,lineHeight:r,controlHeight:a,colorWhite:l}=e,d=t*r,m=a/2,c=2,s=d-c*2,u=m-c*2;return{trackHeight:d,trackHeightSM:m,trackMinWidth:s*2+c*4,trackMinWidthSM:u*2+c*2,trackPadding:c,handleBg:l,handleSize:s,handleSizeSM:u,handleShadow:`0 2px 4px 0 ${new xt("#00230b").setA(.2).toRgbString()}`,innerMinMargin:s/2,innerMaxMargin:s+c+c*2,innerMinMarginSM:u/2,innerMaxMarginSM:u+c+c*2}},Xa=qe("Switch",e=>{const t=nt(e,{switchDuration:e.motionDurationMid,switchColor:e.colorPrimary,switchDisabledOpacity:e.opacityLoading,switchLoadingIconSize:e.calc(e.fontSizeIcon).mul(.75).equal(),switchLoadingIconColor:`rgba(0, 0, 0, ${e.opacityLoading})`,switchHandleActiveInset:"-30%"});return[Ha(t),qa(t),Wa(t),La(t),Va(t)]},Ga);var Ka=function(e,t){var r={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(r[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,a=Object.getOwnPropertySymbols(e);l<a.length;l++)t.indexOf(a[l])<0&&Object.prototype.propertyIsEnumerable.call(e,a[l])&&(r[a[l]]=e[a[l]]);return r};const Ua=i.forwardRef((e,t)=>{const{prefixCls:r,size:a,disabled:l,loading:d,className:m,rootClassName:c,style:s,checked:u,value:o,defaultChecked:h,defaultValue:p,onChange:f}=e,S=Ka(e,["prefixCls","size","disabled","loading","className","rootClassName","style","checked","value","defaultChecked","defaultValue","onChange"]),[g,v]=vt(!1,{value:u??o,defaultValue:h??p}),{getPrefixCls:_,direction:w,switch:O}=i.useContext(He),$=i.useContext(Qt),T=(l??$)||d,N=_("switch",r),P=i.createElement("div",{className:`${N}-handle`},d&&i.createElement(lr,{className:`${N}-loading-icon`})),[I,R,k]=Xa(N),Y=_t(a),K=le(O==null?void 0:O.className,{[`${N}-small`]:Y==="small",[`${N}-loading`]:d,[`${N}-rtl`]:w==="rtl"},m,c,R,k),W=Object.assign(Object.assign({},O==null?void 0:O.style),s),V=(...re)=>{v(re[0]),f==null||f.apply(void 0,re)};return I(i.createElement(ar,{component:"Switch"},i.createElement(cn,Object.assign({},S,{checked:g,onChange:V,prefixCls:N,className:K,style:W,disabled:T,ref:t,loadingIcon:P}))))}),it=Ua;it.__ANT_SWITCH=!0;const Lt=(e,t)=>t===0?0:e/t,Wt=(e,t)=>e*t,Qa=({form:e})=>{const t=j.useWatch("follow_mode",e);return n.jsxs(n.Fragment,{children:[n.jsx(L.Title,{level:5,style:{margin:"0 0 8px 0",color:"#722ed1"},children:"💰 跟单模式配置"}),n.jsxs(ye,{gutter:[8,4],children:[n.jsx(E,{span:6,children:n.jsx(j.Item,{name:"follow_mode",label:"跟单模式",rules:[{required:!0,message:"请选择跟单模式"}],children:n.jsxs(je.Group,{children:[n.jsx(je,{value:"Percentage",children:"按百分比跟单"}),n.jsx(je,{value:"FixedAmount",children:"固定金额跟单"})]})})}),t==="Percentage"&&n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"follow_percentage",label:"跟单百分比 (%)",rules:[{required:t==="Percentage",message:"请输入跟单百分比"}],children:n.jsx(ne,{style:{width:"100%"},placeholder:"5.0",type:"number"})})}),t==="FixedAmount"&&n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"fixed_follow_amount_sol",label:"固定跟单金额 (SOL)",rules:[{required:t==="FixedAmount",message:"请输入固定跟单金额"}],children:n.jsx(ne,{style:{width:"100%"},placeholder:"0.001",type:"number"})})})]})]})},{Option:Ue}=pe,Ya=({form:e})=>{const t=j.useWatch("take_profit_strategy",e);return n.jsxs(n.Fragment,{children:[n.jsx(L.Title,{level:5,style:{margin:"0 0 8px 0",color:"#13c2c2"},children:"📈 止盈策略配置"}),n.jsxs(ye,{gutter:[8,4],children:[n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"take_profit_strategy",label:"止盈策略类型",rules:[{required:!0,message:"请选择止盈策略"}],children:n.jsxs(pe,{placeholder:"选择策略",children:[n.jsx(Ue,{value:"standard",children:"标准分步止盈"}),n.jsx(Ue,{value:"trailing",children:"动态追踪止盈"}),n.jsx(Ue,{value:"exponential",children:"指数递增止盈"}),n.jsx(Ue,{value:"volatility",children:"布林带策略"})]})})}),t==="standard"&&n.jsxs(n.Fragment,{children:[n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"take_profit_start_pct",label:"起始止盈阈值 (%)",children:n.jsx(J,{style:{width:"100%"},placeholder:"20",stringMode:!0,controls:!1})})}),n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"take_profit_step_pct",label:"止盈步长 (%)",children:n.jsx(J,{style:{width:"100%"},placeholder:"10",stringMode:!0,controls:!1})})}),n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"take_profit_sell_portion_pct",label:"每次卖出比例 (%)",children:n.jsx(J,{style:{width:"100%"},placeholder:"25",stringMode:!0,controls:!1})})})]}),t==="trailing"&&n.jsx(n.Fragment,{children:n.jsx(E,{span:6,children:n.jsx(j.Item,{name:"trailing_stop_profit_percentage",label:"追踪止盈触发阈值 (%)",children:n.jsx(J,{style:{width:"100%"},placeholder:"20",stringMode:!0,controls:!1})})})}),t==="exponential"&&n.jsxs(n.Fragment,{children:[n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"exponential_sell_trigger_step_pct",label:"触发步长 (%)",children:n.jsx(J,{style:{width:"100%"},placeholder:"10",stringMode:!0,controls:!1})})}),n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"exponential_sell_base_portion_pct",label:"基础卖出比例 (%)",children:n.jsx(J,{style:{width:"100%"},placeholder:"20",stringMode:!0,controls:!1})})}),n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"exponential_sell_power",label:"指数幂",children:n.jsx(J,{style:{width:"100%"},placeholder:"2.0",step:.01,stringMode:!0,controls:!1})})})]}),t==="volatility"&&n.jsxs(n.Fragment,{children:[n.jsx(E,{span:4,children:n.jsx(j.Item,{label:"布林带窗口大小",name:"volatility_bb_window_size",rules:[{required:!0,message:"请输入布林带窗口大小"}],children:n.jsx(J,{style:{width:"100%"},min:100,max:1e4,placeholder:"1000"})})}),n.jsx(E,{span:4,children:n.jsx(j.Item,{label:"布林带标准差倍数",name:"volatility_bb_stddev",rules:[{required:!0,message:"请输入布林带标准差倍数"}],children:n.jsx(J,{style:{width:"100%"},min:.5,max:5,step:.1,placeholder:"1.8"})})}),n.jsx(E,{span:4,children:n.jsx(j.Item,{label:"ATR样本数",name:"volatility_atr_samples",rules:[{required:!0,message:"请输入ATR样本数"}],children:n.jsx(J,{style:{width:"100%"},min:10,max:1e3,placeholder:"100"})})}),n.jsx(E,{span:4,children:n.jsx(j.Item,{label:"ATR突增阈值倍数",name:"volatility_atr_multiplier",rules:[{required:!0,message:"请输入ATR突增阈值倍数"}],children:n.jsx(J,{style:{width:"100%"},min:.5,max:5,step:.1,placeholder:"1.3"})})}),n.jsx(E,{span:4,children:n.jsx(j.Item,{label:"波动率策略卖出比例",name:"volatility_sell_percent",rules:[{required:!0,message:"请输入波动率策略卖出比例"}],children:n.jsx(J,{style:{width:"100%"},min:1,max:100,formatter:r=>`${r}%`,parser:r=>r==null?void 0:r.replace("%",""),placeholder:"40"})})}),n.jsx(E,{span:4,children:n.jsx(j.Item,{label:"波动率策略冷却时间",name:"volatility_cooldown_ms",rules:[{required:!0,message:"请输入波动率策略冷却时间"}],children:n.jsx(J,{style:{width:"100%"},min:0,max:1e4,placeholder:"500",addonAfter:"ms"})})}),n.jsx(E,{span:4,children:n.jsx(j.Item,{label:"最小卖出保护比例",name:"min_partial_sell_pct",tooltip:"当剩余仓位占初始仓位比例低于该值时直接清仓",children:n.jsx(J,{style:{width:"100%"},min:0,max:100,formatter:r=>`${r}%`,parser:r=>r==null?void 0:r.replace("%",""),placeholder:"30"})})})]})]})]})},Za=({form:e})=>{const t=j.useWatch(["auto_suspend_config","enabled"],e),r=j.useWatch(["auto_suspend_config","window_size"],e),a=j.useWatch(["auto_suspend_config","loss_count"],e),l=j.useWatch(["auto_suspend_config","loss_threshold"],e);return n.jsxs(n.Fragment,{children:[n.jsx(L.Title,{level:5,style:{margin:"0 0 8px 0",color:"#eb2f96"},children:"⏸️ 自动暂停配置"}),n.jsxs(ye,{gutter:[8,4],children:[n.jsx(E,{span:4,children:n.jsx(j.Item,{name:["auto_suspend_config","enabled"],label:"启用自动暂停",valuePropName:"checked",children:n.jsx(it,{checkedChildren:"启用",unCheckedChildren:"禁用"})})}),t&&n.jsxs(n.Fragment,{children:[n.jsx(E,{span:3,children:n.jsx(j.Item,{name:["auto_suspend_config","window_size"],label:"监控窗口 (小时)",rules:[{required:t,message:"请输入监控窗口"}],initialValue:1,children:n.jsx(ne,{style:{width:"100%"},placeholder:"1",type:"number"})})}),n.jsx(E,{span:3,children:n.jsx(j.Item,{name:["auto_suspend_config","loss_count"],label:"连续亏损次数",rules:[{required:t,message:"请输入亏损次数阈值"}],initialValue:1,children:n.jsx(ne,{style:{width:"100%"},placeholder:"1",type:"number"})})}),n.jsx(E,{span:3,children:n.jsx(j.Item,{name:["auto_suspend_config","loss_threshold"],label:"亏损阈值 (%)",rules:[{required:t,message:"请输入亏损阈值"}],initialValue:-5,children:n.jsx(ne,{style:{width:"100%"},placeholder:"-5.0",type:"number"})})}),n.jsx(E,{span:11,children:n.jsx(Pe,{message:"触发条件",description:`在 ${r||1} 小时内连续 ${a||1} 次亏损超过 ${Math.abs(l||5)}% 时，系统将自动暂停该钱包的跟单功能，避免进一步损失`,type:"info",showIcon:!0})})]})]})]})},{Option:qt}=pe,pt=({value:e,onChange:t,placeholder:r,style:a})=>{const l=f=>f?f>=1e3&&f%1e3===0?{value:Math.round(f/1e3),unit:"s"}:{value:Math.round(f),unit:"ms"}:{value:void 0,unit:"ms"},d=l(e),[m,c]=Ce.useState(d.value),[s,u]=Ce.useState(d.unit);Ce.useEffect(()=>{const f=l(e);c(f.value),u(f.unit)},[e]);const o=(f,S)=>f==null?null:S==="s"?f*1e3:f,h=f=>{c(f||void 0);const S=o(f||void 0,s);t==null||t(S)},p=f=>{u(f);const S=o(m,f);t==null||t(S)};return n.jsxs(ne.Group,{compact:!0,style:a,children:[n.jsx(J,{value:m,onChange:h,placeholder:r,style:{width:"70%"},min:0,precision:s==="s"?3:0,step:s==="s"?.1:100,formatter:f=>{if(f==null)return"";const S=Number(f);return S%1===0,S.toString()}}),n.jsxs(pe,{value:s,onChange:p,style:{width:"30%"},children:[n.jsx(qt,{value:"ms",children:"毫秒"}),n.jsx(qt,{value:"s",children:"秒"})]})]})},Ht=({visible:e,mode:t,editingWallet:r,solPrice:a,form:l,onSubmit:d,onCancel:m,loading:c})=>{const s=t==="add"?"添加钱包配置":"编辑钱包配置";Ce.useEffect(()=>{if(t==="edit"&&r&&e){console.log(`💰 Modal: 使用SOL价格 $${a.toFixed(4)} 进行价格转换`);const o={...r,min_price_usd:r.min_price_multiplier?(r.min_price_multiplier*a).toFixed(6):void 0,max_price_usd:r.max_price_multiplier?(r.max_price_multiplier*a).toFixed(3):void 0,min_follow_amount_sol:r.sol_amount_min||void 0,max_follow_amount_sol:r.sol_amount_max||void 0,take_profit_start_pct:r.take_profit_start_pct,take_profit_step_pct:r.take_profit_step_pct,take_profit_sell_portion_pct:r.take_profit_sell_portion_pct,trailing_stop_profit_percentage:r.trailing_stop_profit_percentage,exponential_sell_trigger_step_pct:r.exponential_sell_trigger_step_pct,exponential_sell_base_portion_pct:r.exponential_sell_base_portion_pct,exponential_sell_power:r.exponential_sell_power,volatility_bb_window_size:r.volatility_bb_window_size,volatility_bb_stddev:r.volatility_bb_stddev,volatility_atr_samples:r.volatility_atr_samples,volatility_atr_multiplier:r.volatility_atr_multiplier,volatility_sell_percent:r.volatility_sell_percent,volatility_cooldown_ms:r.volatility_cooldown_ms,min_partial_sell_pct:r.min_partial_sell_pct,sell_slippage_percentage:r.sell_slippage_percentage,sell_priority_fee:r.sell_priority_fee,sell_tip_percentage:r.sell_tip_percentage,sell_compute_unit_limit:r.sell_compute_unit_limit,sell_retry_max_attempts:r.sell_retry_max_attempts,sell_retry_slippage_increment:r.sell_retry_slippage_increment,follow_mode:r.follow_mode||"Percentage",slippage_percentage:r.slippage_percentage??5,priority_fee:r.priority_fee??15e4,compute_unit_limit:r.compute_unit_limit??8e4,accelerator_tip_percentage:r.accelerator_tip_percentage??1,take_profit_strategy:r.take_profit_strategy||"standard",auto_suspend_config:r.auto_suspend_config||{enabled:!0,window_size:1,loss_count:1,loss_threshold:-5},protocol:r.protocol||"pump"};l.setFieldsValue(o)}},[t,r,e,l,a]);const u=async o=>{const h=g=>{if(g==null||g==="")return null;const v=parseFloat(g);return isNaN(v)?null:v};console.log("🔍 提交前原始数据:",o);const p={...o,min_price_multiplier:o.min_price_usd?Lt(h(o.min_price_usd)||0,a):null,max_price_multiplier:o.max_price_usd?Lt(h(o.max_price_usd)||0,a):null,sol_amount_min:h(o.min_follow_amount_sol),sol_amount_max:h(o.max_follow_amount_sol),slippage_percentage:h(o.slippage_percentage),accelerator_tip_percentage:h(o.accelerator_tip_percentage),follow_percentage:h(o.follow_percentage),fixed_follow_amount_sol:h(o.fixed_follow_amount_sol),sell_slippage_percentage:h(o.sell_slippage_percentage),sell_priority_fee:h(o.sell_priority_fee),sell_tip_percentage:h(o.sell_tip_percentage)};o.auto_suspend_config&&(p.auto_suspend_config={enabled:o.auto_suspend_config.enabled,window_size:h(o.auto_suspend_config.window_size),loss_count:h(o.auto_suspend_config.loss_count),loss_threshold:h(o.auto_suspend_config.loss_threshold)}),["take_profit_start_pct","take_profit_step_pct","take_profit_sell_portion_pct","trailing_stop_profit_percentage","exponential_sell_trigger_step_pct","exponential_sell_base_portion_pct","exponential_sell_power","volatility_bb_window_size","volatility_bb_stddev","volatility_atr_samples","volatility_atr_multiplier","volatility_sell_percent","volatility_cooldown_ms","min_partial_sell_pct"].forEach(g=>{o[g]!==void 0&&(p[g]=h(o[g]))}),["hard_stop_loss_pct","callback_stop_pct","entry_confirmation_ms","dynamic_hold_max_ms","dynamic_hold_trigger_pct","dynamic_hold_extend_ms"].forEach(g=>{o[g]!==void 0&&(p[g]=h(o[g]))}),o.follow_mode==="Percentage"?p.fixed_follow_amount_sol=null:o.follow_mode==="FixedAmount"&&(p.follow_percentage=null),delete p.min_price_usd,delete p.max_price_usd,delete p.min_follow_amount_sol,delete p.max_follow_amount_sol,o.take_profit_strategy==="standard"?(p.trailing_stop_profit_percentage=null,p.exponential_sell_trigger_step_pct=null,p.exponential_sell_base_portion_pct=null,p.exponential_sell_power=null,p.volatility_bb_window_size=null,p.volatility_bb_stddev=null,p.volatility_atr_samples=null,p.volatility_atr_multiplier=null,p.volatility_sell_percent=null,p.volatility_cooldown_ms=null,p.min_partial_sell_pct=null):o.take_profit_strategy==="trailing"?(p.take_profit_start_pct=null,p.take_profit_step_pct=null,p.take_profit_sell_portion_pct=null,p.exponential_sell_trigger_step_pct=null,p.exponential_sell_base_portion_pct=null,p.exponential_sell_power=null,p.volatility_bb_window_size=null,p.volatility_bb_stddev=null,p.volatility_atr_samples=null,p.volatility_atr_multiplier=null,p.volatility_sell_percent=null,p.volatility_cooldown_ms=null,p.min_partial_sell_pct=null):o.take_profit_strategy==="exponential"?(p.take_profit_start_pct=null,p.take_profit_step_pct=null,p.take_profit_sell_portion_pct=null,p.trailing_stop_profit_percentage=null,p.volatility_bb_window_size=null,p.volatility_bb_stddev=null,p.volatility_atr_samples=null,p.volatility_atr_multiplier=null,p.volatility_sell_percent=null,p.volatility_cooldown_ms=null,p.min_partial_sell_pct=null):o.take_profit_strategy==="volatility"&&(p.take_profit_start_pct=null,p.take_profit_step_pct=null,p.take_profit_sell_portion_pct=null,p.trailing_stop_profit_percentage=null,p.exponential_sell_trigger_step_pct=null,p.exponential_sell_base_portion_pct=null,p.exponential_sell_power=null),p.stop_loss_percentage=null,p.take_profit_percentage_legacy=null,p.dynamic_hold_check_window_secs=null,console.log("🚀 提交到后端的数据:",p),console.log("🔍 remark字段值:",p.remark),d(p)};return n.jsx(Be,{title:s,open:e,onCancel:m,onOk:()=>l.submit(),confirmLoading:c,width:1400,style:{top:10},destroyOnHidden:!0,children:n.jsx("div",{style:{maxHeight:"80vh",overflowY:"auto"},children:n.jsxs(j,{form:l,layout:"vertical",onFinish:u,size:"small",initialValues:{is_active:!0,follow_mode:"Percentage",slippage_percentage:5,priority_fee:15e4,compute_unit_limit:8e4,accelerator_tip_percentage:1,auto_suspend_config:{enabled:!0,window_size:1,loss_count:1,loss_threshold:-5},sell_slippage_percentage:5,sell_priority_fee:15e4,sell_tip_percentage:1,sell_retry_max_attempts:5,sell_retry_slippage_increment:2},children:[n.jsx(L.Title,{level:5,style:{margin:"0 0 8px 0",color:"#1890ff"},children:"📋 基础配置"}),n.jsxs(ye,{gutter:[8,4],children:[t==="add"&&n.jsx(E,{span:6,children:n.jsx(j.Item,{name:"wallet_address",label:"钱包地址",rules:[{required:!0,message:"请输入钱包地址"},{pattern:/^[1-9A-HJ-NP-Za-km-z]{32,44}$/,message:"请输入有效的钱包地址"}],children:n.jsx(ne,{placeholder:"输入钱包地址"})})}),n.jsx(E,{span:t==="add"?6:8,children:n.jsx(j.Item,{name:"remark",label:"钱包备注",children:n.jsx(ne,{placeholder:"例如：主力钱包、测试钱包等"})})}),n.jsx(E,{span:3,children:n.jsx(j.Item,{name:"is_active",label:"启用状态",valuePropName:"checked",children:n.jsx(it,{checkedChildren:"启用",unCheckedChildren:"停用"})})}),t==="add"&&n.jsx(E,{span:6,children:n.jsx(j.Item,{name:"protocol",label:"交易协议",tooltip:"选择要跟单的交易协议",initialValue:"pump",children:n.jsxs(je.Group,{children:[n.jsx(je.Button,{value:"pump",children:"Pump"}),n.jsx(je.Button,{value:"bonk",children:"Bonk"})]})})})]}),n.jsx(Te,{style:{margin:"8px 0"}}),n.jsx(L.Title,{level:5,style:{margin:"0 0 8px 0",color:"#52c41a"},children:"🔍 交易筛选条件"}),n.jsxs(ye,{gutter:[8,4],children:[n.jsx(E,{span:3,children:n.jsx(j.Item,{name:"min_price_usd",label:"最低价格筛选 (USD)",children:n.jsx(ne,{style:{width:"100%"},placeholder:"0.000001",type:"number"})})}),n.jsx(E,{span:3,children:n.jsx(j.Item,{name:"max_price_usd",label:"最高价格筛选 (USD)",children:n.jsx(ne,{style:{width:"100%"},placeholder:"1.0",type:"number"})})}),n.jsx(E,{span:3,children:n.jsx(j.Item,{name:"min_follow_amount_sol",label:"最小跟单金额 (SOL)",children:n.jsx(ne,{style:{width:"100%"},placeholder:"0.001",type:"number"})})}),n.jsx(E,{span:3,children:n.jsx(j.Item,{name:"max_follow_amount_sol",label:"最大跟单金额 (SOL)",children:n.jsx(ne,{style:{width:"100%"},placeholder:"1.0",type:"number"})})})]}),n.jsx(Te,{style:{margin:"8px 0"}}),n.jsx(Qa,{form:l}),n.jsx(Te,{style:{margin:"8px 0"}}),n.jsx(L.Title,{level:5,style:{margin:"0 0 8px 0",color:"#fa8c16"},children:"⚙️ 交易执行参数"}),n.jsx(L.Text,{strong:!0,style:{color:"#52c41a",fontSize:"14px"},children:"📈 买入参数"}),n.jsxs(ye,{gutter:[8,4],style:{marginTop:4},children:[n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"priority_fee",label:"优先费用 (lamports)",rules:[{required:!0,message:"请输入买入优先费用"}],children:n.jsx(J,{style:{width:"100%"},placeholder:"150000"})})}),n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"compute_unit_limit",label:"计算单元限制",rules:[{required:!0,message:"请输入买入计算单元限制"}],children:n.jsx(J,{style:{width:"100%"},placeholder:"80000"})})}),n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"accelerator_tip_percentage",label:"tip (%)",children:n.jsx(ne,{style:{width:"100%"},placeholder:"1.0",type:"number"})})}),n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"slippage_percentage",label:"滑点容忍度 (%)",rules:[{required:!0,message:"请输入买入滑点容忍度"}],children:n.jsx(ne,{style:{width:"100%"},placeholder:"5.0",type:"number"})})})]}),n.jsx(L.Text,{strong:!0,style:{color:"#ff4d4f",fontSize:"14px",marginTop:"12px",display:"block"},children:"📉 卖出参数"}),n.jsxs(ye,{gutter:[8,4],style:{marginTop:4},children:[n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"sell_priority_fee",label:"优先费用 (lamports)",children:n.jsx(J,{style:{width:"100%"},placeholder:"150000"})})}),n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"sell_compute_unit_limit",label:"计算单元限制",children:n.jsx(J,{style:{width:"100%"},placeholder:"80000"})})}),n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"sell_tip_percentage",label:"tip (%)",children:n.jsx(ne,{style:{width:"100%"},placeholder:"1.0",type:"number"})})}),n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"sell_slippage_percentage",label:"滑点容忍度 (%)",children:n.jsx(ne,{style:{width:"100%"},placeholder:"5.0",type:"number"})})})]}),n.jsx(L.Text,{strong:!0,style:{color:"#ff7a45",fontSize:"13px",marginTop:"8px",display:"block"},children:"🔄 重试配置"}),n.jsxs(ye,{gutter:[8,4],style:{marginTop:4},children:[n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"sell_retry_max_attempts",label:"最大重试次数",tooltip:"卖出失败时的最大重试次数，0表示不重试",children:n.jsx(J,{style:{width:"100%"},placeholder:"5",min:0,max:20,precision:0})})}),n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"sell_retry_slippage_increment",label:"滑点递增 (%)",tooltip:"每次重试增加的滑点百分比，无最大限制",children:n.jsx(J,{style:{width:"100%"},placeholder:"2.0",min:.1,max:50,step:.1,precision:1})})})]}),n.jsx(Te,{style:{margin:"8px 0"}}),n.jsx(L.Title,{level:5,style:{margin:"0 0 8px 0",color:"#f5222d"},children:"🛡️ 风险管理"}),n.jsxs(ye,{gutter:[8,4],children:[n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"hard_stop_loss_pct",label:"硬止损 (%)",children:n.jsx(J,{style:{width:"100%"},placeholder:"20",stringMode:!0,controls:!1})})}),n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"callback_stop_pct",label:"回调止损 (%)",children:n.jsx(J,{style:{width:"100%"},placeholder:"20",stringMode:!0,controls:!1})})}),n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"entry_confirmation_ms",label:"初始持仓时间",children:n.jsx(pt,{style:{width:"100%"},placeholder:"10000"})})}),n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"dynamic_hold_max_ms",label:"最大持仓时间",children:n.jsx(pt,{style:{width:"100%"},placeholder:"20000"})})}),n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"dynamic_hold_trigger_pct",label:"延长触发阈值 (%)",children:n.jsx(J,{style:{width:"100%"},placeholder:"0.5",step:.01,stringMode:!0,controls:!1})})}),n.jsx(E,{span:4,children:n.jsx(j.Item,{name:"dynamic_hold_extend_ms",label:"延长时间",children:n.jsx(pt,{style:{width:"100%"},placeholder:"5000"})})})]}),n.jsx(Te,{style:{margin:"8px 0"}}),n.jsx(Ya,{form:l}),n.jsx(Te,{style:{margin:"8px 0"}}),n.jsx(Za,{form:l})]})})})},dn=Ce.memo(({data:e=[],loading:t=!1,solPrice:r,onEdit:a,onDelete:l,onBatchDelete:d,onBatchStatusToggle:m,onSaveAsTemplate:c,onStatusToggle:s,updateLoading:u=!1,deleteLoading:o=!1})=>{const[h,p]=i.useState(null),[f,S]=i.useState(""),[g,v]=i.useState([]),[_,w]=i.useState(""),[O,$]=i.useState("all"),[T,N]=i.useState("all"),{setWalletRemark:P,getWalletRemarkOrNull:I,remarks:R,updateVersion:k}=en();if(!Array.isArray(e))return console.warn("WalletConfigTable: data is not an array:",e),n.jsx("div",{children:"数据格式错误"});const Y=i.useMemo(()=>{const b={};return e.forEach(y=>{const C=y.min_price_multiplier!==null&&y.min_price_multiplier!==void 0,z=y.max_price_multiplier!==null&&y.max_price_multiplier!==void 0;b[y.wallet_address]={minPrice:C?(y.min_price_multiplier*r).toFixed(6):void 0,maxPrice:z?(y.max_price_multiplier*r).toFixed(3):void 0}}),b},[e,r]),K=i.useMemo(()=>e.filter(b=>{const y=_.toLowerCase(),C=b.protocol||"pump",z=I(b.wallet_address,C)||"",ae=!_||b.wallet_address.toLowerCase().includes(y)||z.toLowerCase().includes(y),q=O==="all"||O==="active"&&b.is_active||O==="inactive"&&!b.is_active,te=T==="all"||T==="percentage"&&b.follow_mode==="Percentage"||T==="fixed"&&b.follow_mode==="FixedAmount";return ae&&q&&te}),[e,_,O,T,I,R,k]),W=i.useCallback(()=>{if(g.length===0||o){Q.warning("请选择要删除的钱包");return}d(g),v([])},[g,o,d]),V=i.useCallback(b=>{if(g.length===0||u){Q.warning("请选择要操作的钱包");return}m(g,b),v([])},[g,u,m]),re=i.useCallback(()=>{w(""),$("all"),N("all")},[]),ie=i.useMemo(()=>[{title:"钱包地址",dataIndex:"wallet_address",key:"wallet_address",width:180,render:b=>n.jsx(Ye,{title:`点击复制完整地址: ${b}`,children:n.jsx(L.Text,{code:!0,copyable:{text:b,tooltips:["复制完整地址","已复制完整地址"]},children:`${b.slice(0,8)}...${b.slice(-8)}`})})},{title:"备注",dataIndex:"wallet_address",key:"remark",width:120,render:(b,y)=>{const C=y.protocol||"pump",z=I(b,C)||y.remark,ae=h===b;if(console.log(`🔍 渲染备注 ${b}@${C}:`,{configRemark:y.remark,systemRemark:I(b,C),final:z}),ae){const q=async()=>{const te=f.trim();if(te)try{await P(b,te,C),Q.success("备注保存成功"),setTimeout(()=>{},0)}catch(ue){Q.error("备注保存失败"),console.error("保存备注失败:",ue)}p(null),S("")};return n.jsx(ne,{size:"small",value:f,autoFocus:!0,onChange:te=>S(te.target.value),onBlur:q,onPressEnter:q,placeholder:"输入备注",style:{width:"100%"}})}return n.jsx(Ye,{title:"点击编辑备注",children:n.jsx(L.Text,{style:{color:z?"#ff4d4f":"#999999",cursor:"pointer",padding:"2px 4px",borderRadius:"2px",transition:"background-color 0.2s",fontWeight:z?"bold":"normal"},onClick:()=>{p(b),S(z||"")},onMouseEnter:q=>{q.currentTarget.style.backgroundColor="#2a2a2a"},onMouseLeave:q=>{q.currentTarget.style.backgroundColor="transparent"},children:z||"点击添加备注"})})}},{title:"状态",dataIndex:"is_active",key:"is_active",width:100,render:(b,y)=>n.jsx(it,{checked:b,onChange:C=>s(y,C),loading:u,checkedChildren:"启用",unCheckedChildren:"停用"})},{title:"跟单模式",key:"follow_mode",width:150,render:(b,y)=>{const C={Percentage:{text:"百分比",color:"blue"},FixedAmount:{text:"固定金额",color:"green"}},z=y.follow_mode||"Percentage",ae=C[z]||{text:"百分比",color:"blue"};return n.jsxs(we,{direction:"vertical",size:"small",children:[n.jsx(ke,{color:ae.color,children:ae.text}),z==="Percentage"&&y.follow_percentage&&n.jsxs(L.Text,{type:"secondary",children:[y.follow_percentage,"%"]}),z==="FixedAmount"&&y.fixed_follow_amount_sol&&n.jsxs(L.Text,{type:"secondary",children:[y.fixed_follow_amount_sol," SOL"]})]})}},{title:"SOL 范围",key:"sol_range",width:150,render:(b,y)=>n.jsxs(we,{direction:"vertical",size:"small",children:[n.jsxs(L.Text,{type:"secondary",children:["最小: ",y.sol_amount_min||"N/A"," SOL"]}),n.jsxs(L.Text,{type:"secondary",children:["最大: ",y.sol_amount_max||"N/A"," SOL"]})]})},{title:"价格筛选",key:"price_filter",width:150,render:(b,y)=>{const C=Y[y.wallet_address];return!(C!=null&&C.minPrice)&&!(C!=null&&C.maxPrice)?n.jsx(ke,{color:"default",children:"无限制"}):n.jsxs(we,{direction:"vertical",size:"small",children:[(C==null?void 0:C.minPrice)&&n.jsxs(L.Text,{type:"secondary",children:["最低: $",C.minPrice]}),(C==null?void 0:C.maxPrice)&&n.jsxs(L.Text,{type:"secondary",children:["最高: $",C.maxPrice]})]})}},{title:"止盈策略",dataIndex:"take_profit_strategy",key:"take_profit_strategy",width:120,render:b=>{const C={standard:{text:"标准分步",color:"blue"},trailing:{text:"追踪止盈",color:"green"},exponential:{text:"指数加码",color:"orange"},volatility:{text:"波动性",color:"purple"}}[b]||{text:"未设置",color:"default"};return n.jsx(ke,{color:C.color,children:C.text})}},{title:"最小卖出保护",dataIndex:"min_partial_sell_pct",key:"min_partial_sell_pct",width:120,render:b=>b?n.jsxs(ke,{color:"warning",children:[b,"%"]}):n.jsx(ke,{color:"default",children:"关闭"})},{title:"自动暂停",key:"auto_suspend",width:120,render:(b,y)=>{if(!y.auto_suspend_config||!y.auto_suspend_config.enabled)return n.jsx(ke,{color:"default",children:"关闭"});const C=y.auto_suspend_config;return n.jsx(Ye,{title:`${C.window_size||1}h内${C.loss_count||1}次亏损>${C.loss_threshold||-5}%时暂停`,children:n.jsx(ke,{color:"warning",children:"启用"})})}},{title:"滑点",dataIndex:"slippage_percentage",key:"slippage_percentage",width:80,render:b=>`${b}%`},{title:"交易参数",key:"trading_params",width:180,render:(b,y)=>{var C,z;return n.jsxs(we,{direction:"vertical",size:"small",children:[n.jsxs(L.Text,{type:"secondary",children:["优先费: ",((C=y.priority_fee)==null?void 0:C.toLocaleString())||"N/A"]}),n.jsxs(L.Text,{type:"secondary",children:["计算单元: ",((z=y.compute_unit_limit)==null?void 0:z.toLocaleString())||"N/A"]}),y.accelerator_tip_percentage&&n.jsxs(L.Text,{type:"secondary",children:["加速器: ",y.accelerator_tip_percentage,"%"]})]})}},{title:"风险管理",key:"risk_management",width:150,render:(b,y)=>n.jsxs(we,{direction:"vertical",size:"small",children:[y.hard_stop_loss_pct&&n.jsxs(L.Text,{type:"secondary",children:["硬止损: ",y.hard_stop_loss_pct,"%"]}),y.callback_stop_pct&&n.jsxs(L.Text,{type:"secondary",children:["回调止损: ",y.callback_stop_pct,"%"]}),!y.hard_stop_loss_pct&&!y.callback_stop_pct&&n.jsx(L.Text,{type:"secondary",children:"未设置"})]})},{title:"操作",key:"actions",width:160,render:(b,y)=>n.jsxs(we,{children:[n.jsx(ce,{type:"text",icon:n.jsx(Zt,{}),onClick:()=>a(y),size:"small",title:"编辑配置"}),c&&n.jsx(ce,{type:"text",icon:n.jsx(Da,{}),onClick:()=>c(y),size:"small",title:"保存为模板"}),n.jsx(St,{title:"确认删除",description:"确定要删除这个钱包配置吗？",onConfirm:()=>l(y.wallet_address,y.protocol||void 0),okText:"确认",cancelText:"取消",children:n.jsx(ce,{type:"text",icon:n.jsx(gt,{}),danger:!0,size:"small",loading:o,title:"删除配置"})})]})}],[a,l,c,s,u,o,I,h,f,P,Y]),G=i.useMemo(()=>({selectedRowKeys:g,onChange:b=>{v(b)},onSelect:(b,y,C)=>{console.log("选择行:",b,y,C)},onSelectAll:(b,y,C)=>{console.log("全选:",b,y,C)}}),[g]);return n.jsxs("div",{children:[n.jsxs(ye,{gutter:[16,16],style:{marginBottom:16},children:[n.jsx(E,{span:8,children:n.jsx(ne,{placeholder:"搜索钱包地址或备注",prefix:n.jsx(_r,{}),value:_,onChange:b=>w(b.target.value),allowClear:!0})}),n.jsx(E,{span:4,children:n.jsxs(pe,{style:{width:"100%"},placeholder:"状态筛选",value:O,onChange:$,children:[n.jsx(pe.Option,{value:"all",children:"全部状态"}),n.jsx(pe.Option,{value:"active",children:"已启用"}),n.jsx(pe.Option,{value:"inactive",children:"已停用"})]})}),n.jsx(E,{span:4,children:n.jsxs(pe,{style:{width:"100%"},placeholder:"模式筛选",value:T,onChange:N,children:[n.jsx(pe.Option,{value:"all",children:"全部模式"}),n.jsx(pe.Option,{value:"percentage",children:"百分比"}),n.jsx(pe.Option,{value:"fixed",children:"固定金额"})]})}),n.jsx(E,{span:8,children:n.jsxs(we,{children:[n.jsx(ce,{icon:n.jsx(pr,{}),onClick:re,children:"清空筛选"}),g.length>0&&n.jsxs(n.Fragment,{children:[n.jsxs(ce,{type:"primary",icon:n.jsx(on,{}),onClick:()=>V(!0),loading:u,disabled:u||g.length===0,children:["批量启用 (",g.length,")"]}),n.jsxs(ce,{icon:n.jsx(Oa,{}),onClick:()=>V(!1),loading:u,disabled:u||g.length===0,children:["批量停用 (",g.length,")"]}),n.jsxs(ce,{danger:!0,icon:n.jsx(gt,{}),onClick:W,loading:o,disabled:o||g.length===0,children:["批量删除 (",g.length,")"]})]})]})})]}),K.length!==e.length&&n.jsx(Pe,{message:`筛选结果: ${K.length} / ${e.length} 个钱包配置`,type:"info",showIcon:!0,style:{marginBottom:16}}),n.jsx(Jt,{columns:ie,dataSource:K,loading:t,rowKey:"wallet_address",rowSelection:G,pagination:{pageSize:10,showSizeChanger:!0,showQuickJumper:!0,showTotal:(b,y)=>`第 ${y[0]}-${y[1]} 条，共 ${b} 个钱包配置`},scroll:{x:1200,y:600}})]})});dn.displayName="WalletConfigTable";const un="wallet_templates";let $e=[];const ft=new Set,Ja=()=>{ft.forEach(e=>e())},pn=()=>{try{const e=localStorage.getItem(un);return e?JSON.parse(e):[]}catch(e){return console.error("加载模板失败:",e),[]}},Qe=e=>{try{localStorage.setItem(un,JSON.stringify(e)),$e=e,Ja()}catch(t){throw console.error("保存模板失败:",t),new Error("保存模板失败")}};$e.length===0&&($e=pn());const wt=()=>{const[e,t]=i.useState($e);i.useEffect(()=>{const s=()=>{t([...$e])};return ft.add(s),t([...$e]),()=>{ft.delete(s)}},[]);const r=(s,u,o)=>{const h={id:`template_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,name:s,description:o,config:u,created_at:new Date().toISOString()},p=[...$e,h];return Qe(p),h};return{templates:e,createTemplate:r,updateTemplate:(s,u)=>{const o=$e.map(h=>h.id===s?{...h,...u,updated_at:new Date().toISOString()}:h);Qe(o)},deleteTemplate:s=>{const u=$e.filter(o=>o.id!==s);Qe(u)},getTemplate:s=>$e.find(u=>u.id===s),createTemplateFromConfig:(s,u,o)=>{const{wallet_address:h,remark:p,...f}=u;return r(s,f,o)},refreshTemplates:()=>{const s=pn();Qe(s)}}},{TextArea:el}=ne,tl=({visible:e,mode:t,template:r,onCancel:a,onSave:l})=>{const[d]=j.useForm();Ce.useEffect(()=>{e&&r&&t==="edit"?d.setFieldsValue({name:r.name,description:r.description||""}):e&&t==="create"&&d.resetFields()},[e,r,t,d]);const m=async()=>{try{const c=await d.validateFields();l(c.name,c.description),d.resetFields()}catch(c){console.error("表单验证失败:",c)}};return n.jsx(Be,{title:t==="create"?"创建模板":"编辑模板",open:e,onCancel:a,onOk:m,width:500,children:n.jsxs(j,{form:d,layout:"vertical",children:[n.jsx(j.Item,{name:"name",label:"模板名称",rules:[{required:!0,message:"请输入模板名称"}],children:n.jsx(ne,{placeholder:"例如：高频交易模板"})}),n.jsx(j.Item,{name:"description",label:"模板描述",children:n.jsx(el,{rows:3,placeholder:"描述该模板的使用场景和特点..."})})]})})},nl=({visible:e,onCancel:t,onSelectTemplate:r})=>{const{templates:a,deleteTemplate:l,updateTemplate:d}=wt(),[m,c]=i.useState(!1),[s,u]=i.useState(null),o=g=>{u(g),c(!0)},h=g=>{try{l(g),Q.success("模板删除成功")}catch{Q.error("删除失败")}},p=(g,v)=>{s&&(d(s.id,{name:g,description:v}),Q.success("模板更新成功"),c(!1),u(null))},f=g=>{r(g),t()},S=[{title:"模板名称",dataIndex:"name",key:"name",width:150},{title:"描述",dataIndex:"description",key:"description",width:200,render:g=>g||"无描述"},{title:"创建时间",dataIndex:"created_at",key:"created_at",width:150,render:g=>new Date(g).toLocaleString()},{title:"跟单模式",key:"follow_mode",width:100,render:(g,v)=>{const _=v.config.follow_mode||"Percentage";return n.jsx(ke,{color:_==="Percentage"?"blue":"green",children:_==="Percentage"?"百分比":"固定金额"})}},{title:"操作",key:"actions",width:150,render:(g,v)=>n.jsxs(we,{children:[n.jsx(ce,{type:"text",icon:n.jsx(mr,{}),onClick:()=>f(v),size:"small",title:"使用此模板"}),n.jsx(ce,{type:"text",icon:n.jsx(Zt,{}),onClick:()=>o(v),size:"small",title:"编辑模板"}),n.jsx(St,{title:"确认删除",description:"确定要删除这个模板吗？",onConfirm:()=>h(v.id),okText:"确认",cancelText:"取消",children:n.jsx(ce,{type:"text",icon:n.jsx(gt,{}),danger:!0,size:"small",title:"删除模板"})})]})}];return n.jsxs(n.Fragment,{children:[n.jsx(Be,{title:"选择配置模板",open:e,onCancel:t,footer:null,width:800,children:n.jsx(Jt,{columns:S,dataSource:a,rowKey:"id",pagination:!1,size:"small",locale:{emptyText:"暂无模板，请先保存一个配置作为模板"}})}),n.jsx(tl,{visible:m,mode:"edit",template:s||void 0,onCancel:()=>{c(!1),u(null)},onSave:p})]})},{TextArea:rl}=ne,{Option:al}=pe;class ll extends Ce.Component{constructor(t){super(t),this.state={hasError:!1}}static getDerivedStateFromError(t){return{hasError:!0,error:t}}componentDidCatch(t,r){console.error("BatchImportModal Error:",t,r)}render(){var t;return this.state.hasError?n.jsx(Pe,{message:"批量导入组件出错",description:`错误信息: ${((t=this.state.error)==null?void 0:t.message)||"未知错误"}`,type:"error",showIcon:!0}):this.props.children}}const il=({visible:e,onCancel:t,onImport:r,loading:a})=>{const{templates:l}=wt(),[d,m]=i.useState(""),[c,s]=i.useState(""),[u,o]=i.useState(null),h=i.useMemo(()=>{if(!c.trim())return{wallets:[],errors:[]};try{const g=c.trim().split(`
`).filter(w=>w.trim()),v=[],_=[];return g.forEach((w,O)=>{const T=w.trim().split(",").map(I=>I.trim()),N=T[0],P=T[1]||"";if(!N||N.length<32||N.length>44){_.push(`第${O+1}行：钱包地址格式无效`);return}if(v.some(I=>I.wallet_address===N)){_.push(`第${O+1}行：钱包地址重复`);return}v.push({wallet_address:N,remark:P||void 0})}),{wallets:v,errors:_}}catch(g){return console.error("Parse wallet list error:",g),{wallets:[],errors:["解析钱包地址列表时出错"]}}},[c]),p=i.useCallback(()=>{try{m(""),s(""),o(null)}catch(g){console.error("Reset error:",g)}},[]),f=async()=>{try{if(!d){Q.error("请选择一个配置模板");return}if(!c.trim()){Q.error("请输入钱包地址列表");return}const g=l.find(O=>O.id===d);if(!g){Q.error("所选模板不存在");return}const{wallets:v,errors:_}=h;if(v.length===0){Q.error("没有有效的钱包地址");return}if(_.length>0){Q.error(`存在${_.length}个错误，请修正后重试`);return}o({total:v.length,current:0,success:0,failed:0,errors:[]}),await r(v,g,O=>{o(O)}),p(),Q.success("批量导入完成")}catch(g){console.error("批量导入失败:",g),Q.error("批量导入失败")}},S=l==null?void 0:l.find(g=>g.id===d);return l?n.jsx(Be,{title:"批量导入钱包地址",open:e,onCancel:()=>{p(),t()},destroyOnClose:!0,width:800,footer:[n.jsx(ce,{onClick:p,children:"重置"},"reset"),n.jsx(ce,{onClick:t,children:"取消"},"cancel"),n.jsx(ce,{type:"primary",icon:n.jsx(on,{}),onClick:f,loading:a,disabled:!d||!c.trim(),children:"开始导入"},"import")],children:n.jsxs(we,{direction:"vertical",size:"middle",style:{width:"100%"},children:[n.jsxs("div",{children:[n.jsx(L.Text,{strong:!0,children:"1. 选择配置模板"}),l.length===0?n.jsx(Pe,{style:{marginTop:8},message:"暂无可用模板",description:"请先保存一个钱包配置作为模板，然后再进行批量导入。",type:"warning",showIcon:!0}):n.jsx(pe,{style:{width:"100%",marginTop:8},placeholder:"请选择一个已保存的配置模板",value:d,onChange:m,showSearch:!0,optionFilterProp:"children",children:l.map(g=>n.jsxs(al,{value:g.id,children:[g.name," - ",g.description||"无描述"]},g.id))}),S&&n.jsx(Pe,{style:{marginTop:8},message:`所选模板：${S.name}`,description:`跟单模式：${S.config.follow_mode==="Percentage"?"百分比":"固定金额"}`,type:"info",showIcon:!0})]}),n.jsx(Te,{style:{margin:"12px 0"}}),n.jsxs("div",{children:[n.jsx(L.Text,{strong:!0,children:"2. 输入钱包地址列表"}),n.jsxs(L.Paragraph,{style:{margin:"8px 0",fontSize:"12px",color:"#666"},children:["支持两种格式：",n.jsx("br",{}),"• 每行一个钱包地址",n.jsx("br",{}),"• 钱包地址,备注（用英文逗号分隔）"]}),n.jsx(rl,{rows:8,placeholder:`请输入钱包地址，每行一个
例如：
A1B2C3D4...
A1B2C3D4...,主力钱包
A1B2C3D4...,测试钱包`,value:c,onChange:g=>s(g.target.value)}),c&&n.jsxs("div",{style:{marginTop:8},children:[n.jsxs(L.Text,{type:"secondary",children:["已识别 ",h.wallets.length," 个有效钱包地址"]}),h.errors.length>0&&n.jsx(Pe,{style:{marginTop:8},message:`发现 ${h.errors.length} 个错误`,description:n.jsxs("ul",{style:{margin:0,paddingLeft:20},children:[h.errors.slice(0,5).map((g,v)=>n.jsx("li",{children:g},v)),h.errors.length>5&&n.jsx("li",{children:"..."})]}),type:"error",showIcon:!0})]})]}),u&&n.jsxs("div",{children:[n.jsx(L.Text,{strong:!0,children:"导入进度"}),n.jsx(ja,{percent:Math.round(u.current/u.total*100),status:u.current===u.total?"success":"active",format:()=>`${u.current}/${u.total}`}),n.jsxs(ye,{gutter:16,style:{marginTop:8},children:[n.jsx(E,{span:8,children:n.jsxs(L.Text,{type:"success",children:["成功: ",u.success]})}),n.jsx(E,{span:8,children:n.jsxs(L.Text,{type:"danger",children:["失败: ",u.failed]})}),n.jsx(E,{span:8,children:n.jsxs(L.Text,{children:["总计: ",u.total]})})]}),u.errors.length>0&&n.jsx(Pe,{style:{marginTop:8},message:"部分导入失败",description:n.jsxs("ul",{style:{margin:0,paddingLeft:20},children:[u.errors.slice(0,3).map((g,v)=>n.jsx("li",{children:g},v)),u.errors.length>3&&n.jsx("li",{children:"..."})]}),type:"warning",showIcon:!0})]})]})}):n.jsx(Be,{title:"批量导入钱包地址",open:e,onCancel:t,footer:null,width:800,children:n.jsxs("div",{style:{textAlign:"center",padding:"50px 0"},children:[n.jsx(ir,{size:"large"}),n.jsx("div",{style:{marginTop:16},children:"加载模板数据中..."})]})})},sl=e=>n.jsx(ll,{children:n.jsx(il,{...e})}),{Title:ol}=L,cl=Ce.memo(()=>{const[e,t]=i.useState(!1),[r,a]=i.useState(!1),[l,d]=i.useState(!1),[m,c]=i.useState(!1),[s,u]=i.useState(!1),[o,h]=i.useState(null),[p,f]=i.useState(null),[S]=j.useForm(),[g]=j.useForm(),[v]=j.useForm(),_=sr(),[w,O]=i.useState("all"),$=Ie.getCachedSolPrice(),{setWalletRemark:T,loadRemarks:N,updateServerRemarks:P}=en(),{createTemplateFromConfig:I}=wt(),{data:R,isLoading:k,refetch:Y}=dr({queryKey:["walletConfigs"],queryFn:Ie.getWalletConfigurations}),K=i.useMemo(()=>R?Object.values(R).map(x=>({...x,key:x.wallet_address})):[],[R]),W=i.useMemo(()=>w==="all"?K:K.filter(x=>x.protocol===w),[K,w]);Ce.useEffect(()=>{R&&P(R)},[R,P]);const V=Ke({mutationFn:x=>x.protocol==="bonk"?(console.log("🔵 使用bonk专用API提交配置"),Ie.updateBonkWalletConfiguration(x)):(console.log("🟢 使用标准API提交配置"),Ie.updateWalletConfiguration(x)),onSuccess:()=>{const x=!o;Q.success(x?"钱包配置添加成功":"钱包配置更新成功"),_.invalidateQueries({queryKey:["walletConfigs"]}),t(!1),a(!1),h(null),S.resetFields(),g.resetFields()},onError:x=>{Q.error(`操作失败: ${x.message}`)}}),re=Ke({mutationFn:x=>Ie.deleteWalletConfiguration(x.walletAddress,x.protocol),onSuccess:()=>{Q.success("钱包配置删除成功"),_.invalidateQueries({queryKey:["walletConfigs"]})},onError:x=>{Q.error(`删除失败: ${x.message}`)}}),ie=Ke({mutationFn:async x=>{const F=[];for(let ee=0;ee<x.length;ee+=5){const be=x.slice(ee,ee+5).map(de=>Ie.deleteWalletConfiguration(de.walletAddress,de.protocol)),me=await Promise.allSettled(be);F.push(...me)}const Z=F.filter(ee=>ee.status==="rejected");if(Z.length>0)throw new Error(`${Z.length}个钱包配置删除失败`);return F},onMutate:async x=>{await _.cancelQueries({queryKey:["walletConfigs"]});const A=_.getQueryData(["walletConfigs"]);if(A){const F={...A};x.forEach(Z=>{delete F[Z.walletAddress]}),_.setQueryData(["walletConfigs"],F)}return{previousData:A}},onSuccess:x=>{const A=x.filter(Z=>Z.status==="fulfilled").length,F=x.filter(Z=>Z.status==="rejected").length;F===0?Q.success(`成功删除 ${A} 个钱包配置`):Q.warning(`删除完成：成功 ${A} 个，失败 ${F} 个`),setTimeout(()=>{_.invalidateQueries({queryKey:["walletConfigs"]})},300)},onError:(x,A,F)=>{F!=null&&F.previousData&&_.setQueryData(["walletConfigs"],F.previousData),Q.error(`批量删除失败: ${x.message}`)}}),G=Ke({mutationFn:async({walletAddresses:x,isActive:A})=>{const F=R?Object.values(R):[],Z=5,ee=[];for(let oe=0;oe<x.length;oe+=Z){const me=x.slice(oe,oe+Z).map(xe=>{const Oe=F.find(ze=>ze.wallet_address===xe);if(Oe){const ze={...Oe,is_active:A};return Ie.updateWalletConfiguration(ze)}return Promise.reject(new Error(`找不到钱包配置: ${xe}`))}),de=await Promise.allSettled(me);ee.push(...de)}return ee},onMutate:async({walletAddresses:x,isActive:A})=>{await _.cancelQueries({queryKey:["walletConfigs"]});const F=_.getQueryData(["walletConfigs"]);if(F){const Z={...F};x.forEach(ee=>{Z[ee]&&(Z[ee]={...Z[ee],is_active:A})}),_.setQueryData(["walletConfigs"],Z)}return{previousData:F}},onSuccess:(x,{isActive:A})=>{const F=x.filter(oe=>oe.status==="fulfilled").length,Z=x.filter(oe=>oe.status==="rejected").length,ee=A?"启用":"停用";Z===0?Q.success(`成功${ee} ${F} 个钱包配置`):Q.warning(`${ee}完成：成功 ${F} 个，失败 ${Z} 个`),setTimeout(()=>{_.invalidateQueries({queryKey:["walletConfigs"]})},300)},onError:(x,A,F)=>{F!=null&&F.previousData&&_.setQueryData(["walletConfigs"],F.previousData),Q.error(`批量状态切换失败: ${x.message}`)}}),b=i.useCallback(async(x,A)=>{const F={...x,is_active:A};V.mutate(F)},[V]),y=i.useCallback(x=>{h(x);const A={...x,min_price_usd:x.min_price_multiplier?Wt(x.min_price_multiplier,$):void 0,max_price_usd:x.max_price_multiplier?Wt(x.max_price_multiplier,$):void 0};S.setFieldsValue(A),t(!0)},[S,$]),C=i.useCallback((x,A)=>{re.mutate({walletAddress:x,protocol:A})},[re]),z=i.useCallback(x=>{ie.mutate(x.map(A=>({walletAddress:A})))},[ie]),ae=i.useCallback((x,A)=>{G.mutate({walletAddresses:x,isActive:A})},[G]),q=i.useCallback(async(x,A,F)=>{const Z=[];let ee=0,oe=0;F({total:x.length,current:0,success:0,failed:0,errors:[]});for(let be=0;be<x.length;be++){const me=x[be];try{const de={...A.config,wallet_address:me.wallet_address,remark:me.remark};await Ie.updateWalletConfiguration(de),ee++}catch(de){const xe=de instanceof Error?de.message:"未知错误",Oe=`${me.wallet_address}: ${xe}`;Z.push(Oe),oe++}F({total:x.length,current:be+1,success:ee,failed:oe,errors:Z})}_.invalidateQueries({queryKey:["walletConfigs"]}),setTimeout(()=>{N()},100),Z.length===0?Q.success(`成功导入 ${ee} 个钱包配置`):Q.warning(`导入完成，成功: ${ee}，失败: ${Z.length}`)},[_,T,N]),te=i.useCallback(x=>{f(x),u(!0)},[]),ue=i.useCallback(async()=>{if(p)try{const x=await v.validateFields();I(x.templateName,p,x.templateDescription),Q.success("模板保存成功"),u(!1),f(null),v.resetFields()}catch(x){console.error("保存模板失败:",x),Q.error("模板保存失败")}},[p,v,I]),he=i.useCallback(()=>{u(!1),f(null),v.resetFields()},[v]),fe=i.useCallback(x=>{const A={...x.config,wallet_address:"",remark:""};g.setFieldsValue(A),a(!0)},[g]),H=i.useCallback(async x=>{if(!o)return;const A={...o,...x};V.mutate(A)},[o,V]),_e=i.useCallback(async x=>{const A={wallet_address:x.wallet_address,is_active:x.is_active??!0,follow_mode:x.follow_mode||"Percentage",slippage_percentage:x.slippage_percentage,priority_fee:x.priority_fee,compute_unit_limit:x.compute_unit_limit,protocol:x.protocol||"pump",...x};V.mutate(A)},[V]),Ge=i.useCallback(()=>{t(!1),h(null),S.resetFields()},[S]),Ne=i.useCallback(()=>{a(!1),g.resetFields()},[g]);return n.jsxs("div",{children:[n.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:16},children:[n.jsxs(ol,{level:2,children:[n.jsx(or,{})," 钱包配置管理"]}),n.jsxs(we,{children:[n.jsx(ce,{type:"primary",onClick:()=>{a(!0)},children:"添加钱包"}),n.jsx(ce,{icon:n.jsx(ka,{}),onClick:()=>c(!0),children:"批量导入"}),n.jsx(ce,{icon:n.jsx(Fa,{}),onClick:()=>d(!0),children:"模板管理"}),n.jsx(ce,{icon:n.jsx(ur,{}),onClick:()=>Y(),loading:k,children:"刷新"})]})]}),n.jsxs("div",{style:{marginBottom:16},children:[n.jsxs(je.Group,{defaultValue:"all",buttonStyle:"solid",onChange:x=>O(x.target.value),value:w,children:[n.jsx(je.Button,{value:"all",children:"全部协议"}),n.jsx(je.Button,{value:"pump",children:"Pump协议"}),n.jsx(je.Button,{value:"bonk",children:"Bonk协议"})]}),n.jsxs("span",{style:{marginLeft:8,color:"#888"},children:["当前显示: ",w==="all"?"全部钱包":w==="pump"?"Pump协议钱包":"Bonk协议钱包","(",W.length," 个)"]})]}),n.jsx(xr,{children:n.jsx(dn,{data:W,loading:k,solPrice:$,onEdit:y,onDelete:C,onBatchDelete:z,onBatchStatusToggle:ae,onSaveAsTemplate:te,onStatusToggle:b,updateLoading:V.isPending||G.isPending,deleteLoading:re.isPending||ie.isPending})}),e&&n.jsx(Ht,{visible:e,mode:"edit",editingWallet:o,solPrice:$,form:S,onSubmit:H,onCancel:Ge,loading:V.isPending}),r&&n.jsx(Ht,{visible:r,mode:"add",solPrice:$,form:g,onSubmit:_e,onCancel:Ne,loading:V.isPending}),s&&n.jsxs(Be,{title:"保存为模板",open:s,onOk:ue,onCancel:he,width:500,children:[n.jsx("div",{style:{marginBottom:16},children:n.jsx("p",{children:"将当前配置保存为模板，以便批量应用到其他钱包。"})}),n.jsxs(j,{form:v,layout:"vertical",children:[n.jsx(j.Item,{name:"templateName",label:"模板名称",rules:[{required:!0,message:"请输入模板名称"}],children:n.jsx(ne,{placeholder:"例如：高频交易模板"})}),n.jsx(j.Item,{name:"templateDescription",label:"模板描述",children:n.jsx(ne.TextArea,{rows:3,placeholder:"描述该模板的使用场景和特点..."})})]})]}),l&&n.jsx(nl,{visible:l,onCancel:()=>d(!1),onSelectTemplate:fe}),m&&n.jsx(sl,{visible:m,onCancel:()=>c(!1),onImport:q,loading:V.isPending})]})});cl.displayName="WalletConfigPage";export{cl as default};
