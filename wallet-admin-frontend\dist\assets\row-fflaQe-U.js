import{r as f,C as A,c as G,n as v,l as I}from"./index-CW-Whzws.js";import{u as _,e as J}from"./index-BVbup1Oj.js";const k=f.createContext({});var M=function(e,a){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&a.indexOf(t)<0&&(n[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)a.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(n[t[r]]=e[t[r]]);return n};function R(e){return typeof e=="number"?`${e} ${e} auto`:/^\d+(\.\d+)?(px|em|rem|%)$/.test(e)?`0 0 ${e}`:e}const z=["xs","sm","md","lg","xl","xxl"],H=f.forwardRef((e,a)=>{const{getPrefixCls:n,direction:t}=f.useContext(A),{gutter:r,wrap:c}=f.useContext(k),{prefixCls:u,span:i,order:g,offset:$,push:O,pull:C,className:E,children:x,flex:b,style:j}=e,p=M(e,["prefixCls","span","order","offset","push","pull","className","children","flex","style"]),o=n("col",u),[N,P,m]=_(o),w={};let d={};z.forEach(l=>{let s={};const S=e[l];typeof S=="number"?s.span=S:typeof S=="object"&&(s=S||{}),delete p[l],d=Object.assign(Object.assign({},d),{[`${o}-${l}-${s.span}`]:s.span!==void 0,[`${o}-${l}-order-${s.order}`]:s.order||s.order===0,[`${o}-${l}-offset-${s.offset}`]:s.offset||s.offset===0,[`${o}-${l}-push-${s.push}`]:s.push||s.push===0,[`${o}-${l}-pull-${s.pull}`]:s.pull||s.pull===0,[`${o}-rtl`]:t==="rtl"}),s.flex&&(d[`${o}-${l}-flex`]=!0,w[`--${o}-${l}-flex`]=R(s.flex))});const h=G(o,{[`${o}-${i}`]:i!==void 0,[`${o}-order-${g}`]:g,[`${o}-offset-${$}`]:$,[`${o}-push-${O}`]:O,[`${o}-pull-${C}`]:C},E,d,P,m),y={};if(r&&r[0]>0){const l=r[0]/2;y.paddingLeft=l,y.paddingRight=l}return b&&(y.flex=R(b),c===!1&&!y.minWidth&&(y.minWidth=0)),N(f.createElement("div",Object.assign({},p,{style:Object.assign(Object.assign(Object.assign({},y),j),w),className:h,ref:a}),x))});function B(e,a){const n=[void 0,void 0],t=Array.isArray(e)?e:[e,void 0],r=a||{xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0};return t.forEach((c,u)=>{if(typeof c=="object"&&c!==null)for(let i=0;i<v.length;i++){const g=v[i];if(r[g]&&c[g]!==void 0){n[u]=c[g];break}}else n[u]=c}),n}var L=function(e,a){var n={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&a.indexOf(t)<0&&(n[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,t=Object.getOwnPropertySymbols(e);r<t.length;r++)a.indexOf(t[r])<0&&Object.prototype.propertyIsEnumerable.call(e,t[r])&&(n[t[r]]=e[t[r]]);return n};function V(e,a){const[n,t]=f.useState(typeof e=="string"?e:""),r=()=>{if(typeof e=="string"&&t(e),typeof e=="object")for(let c=0;c<v.length;c++){const u=v[c];if(!a||!a[u])continue;const i=e[u];if(i!==void 0){t(i);return}}};return f.useEffect(()=>{r()},[JSON.stringify(e),a]),n}const q=f.forwardRef((e,a)=>{const{prefixCls:n,justify:t,align:r,className:c,style:u,children:i,gutter:g=0,wrap:$}=e,O=L(e,["prefixCls","justify","align","className","style","children","gutter","wrap"]),{getPrefixCls:C,direction:E}=f.useContext(A),x=I(!0,null),b=V(r,x),j=V(t,x),p=C("row",n),[o,N,P]=J(p),m=B(g,x),w=G(p,{[`${p}-no-wrap`]:$===!1,[`${p}-${j}`]:j,[`${p}-${b}`]:b,[`${p}-rtl`]:E==="rtl"},c,N,P),d={},h=m[0]!=null&&m[0]>0?m[0]/-2:void 0;h&&(d.marginLeft=h,d.marginRight=h);const[y,l]=m;d.rowGap=l;const s=f.useMemo(()=>({gutter:[y,l],wrap:$}),[y,l,$]);return o(f.createElement(k.Provider,{value:s},f.createElement("div",Object.assign({},O,{className:w,style:Object.assign(Object.assign({},d),u),ref:a}),i)))});export{H as C,q as R};
