import{r as i,I as u,_ as p,p as A,j as e,S as B,as as d,T as v,B as k,ak as b,A as M}from"./index-CW-Whzws.js";import{u as D}from"./useWalletRemarks-BeWTBa_i.js";import{u as F,T as f}from"./index-pOBwrJ9T.js";import{C as E,s as j}from"./index-BVbup1Oj.js";import{F as P,a as U}from"./Table-DINlcmbY.js";import{R as N,C as V}from"./row-fflaQe-U.js";import"./Input-Cr3didPW.js";var K={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M862 465.3h-81c-4.6 0-9 2-12.1 5.5L550 723.1V160c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v563.1L255.1 470.8c-3-3.5-7.4-5.5-12.1-5.5h-81c-6.8 0-10.5 8.1-6 13.2L487.9 861a31.96 31.96 0 0048.3 0L868 478.5c4.5-5.2.8-13.2-6-13.2z"}}]},name:"arrow-down",theme:"outlined"},q=function(r,n){return i.createElement(u,p({},r,{ref:n,icon:K}))},Q=i.forwardRef(q),G={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 545.5L536.1 163a31.96 31.96 0 00-48.3 0L156 545.5a7.97 7.97 0 006 13.2h81c4.6 0 9-2 12.1-5.5L474 300.9V864c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V300.9l218.9 252.3c3 3.5 7.4 5.5 12.1 5.5h81c6.8 0 10.5-8 6-13.2z"}}]},name:"arrow-up",theme:"outlined"},H=function(r,n){return i.createElement(u,p({},r,{ref:n,icon:G}))},J=i.forwardRef(H),X={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M832.6 191.4c-84.6-84.6-221.5-84.6-306 0l-96.9 96.9 51 51 96.9-96.9c53.8-53.8 144.6-59.5 204 0 59.5 59.5 53.8 150.2 0 204l-96.9 96.9 51.1 51.1 96.9-96.9c84.4-84.6 84.4-221.5-.1-306.1zM446.5 781.6c-53.8 53.8-144.6 59.5-204 0-59.5-59.5-53.8-150.2 0-204l96.9-96.9-51.1-51.1-96.9 96.9c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l96.9-96.9-51-51-96.8 97zM260.3 209.4a8.03 8.03 0 00-11.3 0L209.4 249a8.03 8.03 0 000 11.3l554.4 554.4c3.1 3.1 8.2 3.1 11.3 0l39.6-39.6c3.1-3.1 3.1-8.2 0-11.3L260.3 209.4z"}}]},name:"disconnect",theme:"outlined"},Y=function(r,n){return i.createElement(u,p({},r,{ref:n,icon:X}))},Z=i.forwardRef(Y),ee={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M574 665.4a8.03 8.03 0 00-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8a8.03 8.03 0 00-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3L574 665.4zm258.6-474c-84.6-84.6-221.5-84.6-306 0L410.3 307.6a8.03 8.03 0 000 11.3l39.7 39.7c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0 59.5 59.5 53.8 150.2 0 204L665.3 562.6a8.03 8.03 0 000 11.3l39.8 39.8c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1zM610.1 372.3a8.03 8.03 0 00-11.3 0L372.3 598.7a8.03 8.03 0 000 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l226.4-226.4c3.1-3.1 3.1-8.2 0-11.3l-39.5-39.6z"}}]},name:"link",theme:"outlined"},te=function(r,n){return i.createElement(u,p({},r,{ref:n,icon:ee}))},oe=i.forwardRef(te),re={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M723 620.5C666.8 571.6 593.4 542 513 542s-153.8 29.6-210.1 78.6a8.1 8.1 0 00-.8 11.2l36 42.9c2.9 3.4 8 3.8 11.4.9C393.1 637.2 450.3 614 513 614s119.9 23.2 163.5 61.5c3.4 2.9 8.5 2.5 11.4-.9l36-42.9c2.8-3.3 2.4-8.3-.9-11.2zm117.4-140.1C751.7 406.5 637.6 362 513 362s-238.7 44.5-327.5 118.4a8.05 8.05 0 00-1 11.3l36 42.9c2.8 3.4 7.9 3.8 11.2 1C308 472.2 406.1 434 513 434s205 38.2 281.2 101.6c3.4 2.8 8.4 2.4 11.2-1l36-42.9c2.8-3.4 2.4-8.5-1-11.3zm116.7-139C835.7 241.8 680.3 182 511 182c-168.2 0-322.6 59-443.7 157.4a8 8 0 00-1.1 11.4l36 42.9c2.8 3.3 7.8 3.8 11.1 1.1C222 306.7 360.3 254 511 254c151.8 0 291 53.5 400 142.7 3.4 2.8 8.4 2.3 11.2-1.1l36-42.9c2.9-3.4 2.4-8.5-1.1-11.3zM448 778a64 64 0 10128 0 64 64 0 10-128 0z"}}]},name:"wifi",theme:"outlined"},ne=function(r,n){return i.createElement(u,p({},r,{ref:n,icon:re}))},ae=i.forwardRef(ne);const{Text:a}=v,le=({maxItems:c=50})=>{const{trades:r,isConnected:n,isLoading:y,dataSource:x,clearTrades:C}=A(),{getWalletRemark:z}=D(),{data:m}=F({queryKey:["walletConfigs"],queryFn:M.getWalletConfigurations}),S=async t=>{try{await navigator.clipboard.writeText(t),j.success("地址已复制到剪贴板")}catch{j.error("复制失败")}},L=t=>t?`${t.slice(0,6)}...${t.slice(-4)}`:"",R=t=>{if(t){const o=`https://solscan.io/tx/${t}`;window.open(o,"_blank")}},h=t=>t.toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:6}),T=t=>{const o=t/1e6;return o>=1e9?(o/1e9).toFixed(2)+"B":o>=1e6?(o/1e6).toFixed(2)+"M":o>=1e3?(o/1e3).toFixed(2)+"K":o.toFixed(2)},_=t=>{switch(t){case"buy":return{icon:e.jsx(J,{}),color:"#52c41a",text:"买入"};case"sell":return{icon:e.jsx(Q,{}),color:"#ff4d4f",text:"卖出"};default:return{icon:null,color:"#666",text:t}}},$=t=>{switch(t){case"Pending":return"orange";case"Confirmed":return"green";case"Failed":return"red";default:return"default"}},I=[{title:"时间",dataIndex:"block_time",key:"time",width:80,render:t=>e.jsx(a,{style:{fontSize:"12px",fontFamily:"monospace",color:"#ffffff"},children:new Date(t*1e3).toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit",second:"2-digit"})})},{title:"类型",dataIndex:"trade_type",key:"type",width:80,render:t=>{const o=_(t),l=t.toLowerCase()==="buy";return e.jsx(f,{icon:o.icon,color:l?"green":"red",style:{fontWeight:"bold",backgroundColor:l?"#52c41a":"#ff4d4f",borderColor:l?"#52c41a":"#ff4d4f",color:"#ffffff"},children:o.text})}},{title:"金额 USD",dataIndex:"usd_amount",key:"usd_amount",width:100,render:t=>e.jsxs(a,{style:{fontSize:"12px",fontWeight:"bold",color:"#ffffff"},children:["$",h(t)]})},{title:"数量",dataIndex:"token_amount",key:"token_amount",width:120,render:(t,o)=>{const l=o.trade_type.toLowerCase().includes("buy")&&o.status==="Pending";return e.jsxs(d,{direction:"vertical",size:0,children:[e.jsx(a,{style:{fontSize:"12px",color:"#ffffff"},children:T(t)}),l&&e.jsx(a,{style:{fontSize:"10px",color:"#cccccc"},children:"(预计)"})]})}},{title:"价格",dataIndex:"sol_price_usd",key:"price",width:100,render:t=>e.jsxs(a,{style:{fontSize:"12px",color:"#ffffff"},children:["$",h(t)]})},{title:"交易者",dataIndex:"user_wallet",key:"trader",width:120,render:(t,o)=>{const l=o.followed_wallet||o.target_wallet,s=l||t;let g="pump";if(m){const w=m[s];m[`${s}_bonk`]?g="bonk":w&&(g=w.protocol||"pump")}const O=z(s,g),W=!l;return e.jsxs(d,{direction:"vertical",size:0,children:[e.jsx(b,{title:`点击复制地址: ${s}`,children:e.jsx(a,{style:{fontSize:"11px",cursor:"pointer",color:W?"#ffa940":"#52c41a",fontWeight:"bold"},onClick:()=>S(s),children:O})}),e.jsx(b,{title:`代币: ${o.mint}`,children:e.jsx(a,{code:!0,style:{fontSize:"10px",color:"#cccccc",cursor:"pointer",backgroundColor:"#333333"},children:L(o.mint)})})]})}},{title:"状态",dataIndex:"status",key:"status",width:80,render:t=>e.jsx(f,{color:$(t),style:{fontSize:"11px"},children:t})},{title:"盈亏",dataIndex:"profit_usd",key:"profit",width:90,render:t=>{if(t==null)return e.jsx(a,{style:{fontSize:"10px",color:"#666666"},children:"-"});const o=t>=0,l=Math.abs(t);return e.jsxs(d,{direction:"vertical",size:0,style:{textAlign:"center"},children:[e.jsxs(a,{style:{fontSize:"11px",color:o?"#52c41a":"#ff4d4f",fontWeight:"bold"},children:[o?"+":"-","$",h(l)]}),e.jsx(a,{style:{fontSize:"9px",color:o?"#52c41a":"#ff4d4f",opacity:.8},children:o?"盈利":"亏损"})]})}},{title:"",key:"action",width:50,render:(t,o)=>e.jsx(b,{title:"查看区块链浏览器",children:e.jsx(k,{type:"text",size:"small",icon:e.jsx(oe,{}),onClick:()=>R(o.signature),disabled:!o.signature,style:{color:"#ffffff",fontSize:"12px",padding:"2px 4px",height:"24px",width:"24px"}})})}];return e.jsxs(E,{title:e.jsxs(d,{children:[e.jsx("span",{style:{color:"#ffffff"},children:"实时交易记录"}),e.jsx(f,{icon:n?e.jsx(ae,{}):e.jsx(Z,{}),color:n?"green":"red",children:n?"已连接":"未连接"}),r.length>0&&e.jsxs(f,{color:"blue",children:[r.length," 条记录"]}),x!=="none"&&e.jsx(f,{color:x==="backend"?"green":"orange",children:x==="backend"?"后端数据":"本地缓存"})]}),extra:e.jsxs(d,{children:[e.jsxs(a,{style:{fontSize:"12px",color:"#cccccc"},children:["最大 ",c," 条"]}),e.jsx(k,{type:"text",icon:e.jsx(U,{}),onClick:C,size:"small",disabled:r.length===0,style:{color:"#ffffff"},children:"清空"})]}),size:"small",style:{backgroundColor:"#1f1f1f",borderColor:"#404040"},headStyle:{backgroundColor:"#2a2a2a",borderBottom:"1px solid #404040"},bodyStyle:{backgroundColor:"#1f1f1f",padding:"12px"},children:[e.jsx(B,{spinning:y,tip:"加载交易记录中...",children:e.jsx(P,{columns:I,dataSource:r,rowKey:"trade_id",pagination:!1,size:"small",locale:{emptyText:y?"加载中...":x==="none"?"暂无交易记录，等待实时数据...":"暂无交易记录"},scroll:{y:"calc(100vh - 300px)"},className:"dark-table",rowClassName:t=>t.status==="Pending"?"pending-trade-row":"normal-trade-row"})}),e.jsx("style",{children:`
        .dark-table {
          background-color: #1f1f1f !important;
        }
        .dark-table .ant-table {
          background-color: #1f1f1f !important;
          color: #ffffff !important;
        }
        .dark-table .ant-table-thead > tr > th {
          background-color: #2a2a2a !important;
          color: #ffffff !important;
          border-bottom: 1px solid #404040 !important;
        }
        .dark-table .ant-table-tbody > tr > td {
          background-color: #1f1f1f !important;
          color: #ffffff !important;
          border-bottom: 1px solid #404040 !important;
        }
        .dark-table .ant-table-tbody > tr:hover > td {
          background-color: #2a2a2a !important;
        }
        .normal-trade-row > td {
          background-color: #1f1f1f !important;
        }
        .normal-trade-row:hover > td {
          background-color: #2a2a2a !important;
        }
        .pending-trade-row > td {
          background-color: #3a2a00 !important;
        }
        .pending-trade-row:hover > td {
          background-color: #4a3500 !important;
        }
        .dark-table .ant-empty-description {
          color: #ffffff !important;
        }
      `})]})},{Title:ie,Paragraph:ce}=v,ge=()=>e.jsxs("div",{children:[e.jsx(ie,{level:2,children:"实时交易记录"}),e.jsx(ce,{type:"secondary",children:"实时监控所有钱包的交易活动，包括买入、卖出操作和交易状态变化"}),e.jsx(N,{gutter:[24,24],children:e.jsx(V,{span:24,children:e.jsx(le,{maxItems:200})})})]});export{ge as default};
