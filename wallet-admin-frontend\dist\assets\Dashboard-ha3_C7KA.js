import{r,I as D,_ as V,b as O,C as ce,c as F,d as ye,g as $e,m as Se,e as j,f as be,h as Ce,i as _e,k as ge,l as He,n as fe,S as Ae,o as We,w as pe,p as ke,j as i,T as De,B as Ve,A as Fe}from"./index-CW-Whzws.js";import{m as Ge,D as Xe,u as Ue,T as ne}from"./index-pOBwrJ9T.js";import{P as qe,u as Ke}from"./useWalletRemarks-BeWTBa_i.js";import{A as Je,M as Ye}from"./index-CBm39ssz.js";import{p as Qe,S as Ze,C as M,s as he}from"./index-BVbup1Oj.js";import{C as R,R as X}from"./row-fflaQe-U.js";var et={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M847.9 592H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h605.2L612.9 851c-4.1 5.2-.4 13 6.3 13h72.5c4.9 0 9.5-2.2 12.6-6.1l168.8-214.1c16.5-21 1.6-51.8-25.2-51.8zM872 356H266.8l144.3-183c4.1-5.2.4-13-6.3-13h-72.5c-4.9 0-9.5 2.2-12.6 6.1L150.9 380.2c-16.5 21-1.6 51.8 25.1 51.8h696c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"}}]},name:"swap",theme:"outlined"},tt=function(t,n){return r.createElement(D,V({},t,{ref:n,icon:et}))},nt=r.forwardRef(tt);const de=O.createContext({});de.Consumer;var je=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,a=Object.getOwnPropertySymbols(e);l<a.length;l++)t.indexOf(a[l])<0&&Object.prototype.propertyIsEnumerable.call(e,a[l])&&(n[a[l]]=e[a[l]]);return n};const at=e=>{var{prefixCls:t,className:n,avatar:a,title:l,description:p}=e,g=je(e,["prefixCls","className","avatar","title","description"]);const{getPrefixCls:h}=r.useContext(ce),d=h("list",t),o=F(`${d}-item-meta`,n),v=O.createElement("div",{className:`${d}-item-meta-content`},l&&O.createElement("h4",{className:`${d}-item-meta-title`},l),p&&O.createElement("div",{className:`${d}-item-meta-description`},p));return O.createElement("div",Object.assign({},g,{className:o}),a&&O.createElement("div",{className:`${d}-item-meta-avatar`},a),(l||p)&&v)},it=O.forwardRef((e,t)=>{const{prefixCls:n,children:a,actions:l,extra:p,styles:g,className:h,classNames:d,colStyle:o}=e,v=je(e,["prefixCls","children","actions","extra","styles","className","classNames","colStyle"]),{grid:y,itemLayout:f}=r.useContext(de),{getPrefixCls:s,list:m}=r.useContext(ce),u=C=>{var x,I;return F((I=(x=m==null?void 0:m.item)===null||x===void 0?void 0:x.classNames)===null||I===void 0?void 0:I[C],d==null?void 0:d[C])},$=C=>{var x,I;return Object.assign(Object.assign({},(I=(x=m==null?void 0:m.item)===null||x===void 0?void 0:x.styles)===null||I===void 0?void 0:I[C]),g==null?void 0:g[C])},T=()=>{let C=!1;return r.Children.forEach(a,x=>{typeof x=="string"&&(C=!0)}),C&&r.Children.count(a)>1},b=()=>f==="vertical"?!!p:!T(),z=s("list",n),L=l&&l.length>0&&O.createElement("ul",{className:F(`${z}-item-action`,u("actions")),key:"actions",style:$("actions")},l.map((C,x)=>O.createElement("li",{key:`${z}-item-action-${x}`},C,x!==l.length-1&&O.createElement("em",{className:`${z}-item-action-split`})))),A=y?"div":"li",P=O.createElement(A,Object.assign({},v,y?{}:{ref:t},{className:F(`${z}-item`,{[`${z}-item-no-flex`]:!b()},h)}),f==="vertical"&&p?[O.createElement("div",{className:`${z}-item-main`,key:"content"},a,L),O.createElement("div",{className:F(`${z}-item-extra`,u("extra")),key:"extra",style:$("extra")},p)]:[a,L,ye(p,{key:"extra"})]);return y?O.createElement(R,{ref:t,flex:1,style:o},P):P}),ze=it;ze.Meta=at;const rt=e=>{const{listBorderedCls:t,componentCls:n,paddingLG:a,margin:l,itemPaddingSM:p,itemPaddingLG:g,marginLG:h,borderRadiusLG:d}=e;return{[t]:{border:`${j(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:d,[`${n}-header,${n}-footer,${n}-item`]:{paddingInline:a},[`${n}-pagination`]:{margin:`${j(l)} ${j(h)}`}},[`${t}${n}-sm`]:{[`${n}-item,${n}-header,${n}-footer`]:{padding:p}},[`${t}${n}-lg`]:{[`${n}-item,${n}-header,${n}-footer`]:{padding:g}}}},lt=e=>{const{componentCls:t,screenSM:n,screenMD:a,marginLG:l,marginSM:p,margin:g}=e;return{[`@media screen and (max-width:${a}px)`]:{[t]:{[`${t}-item`]:{[`${t}-item-action`]:{marginInlineStart:l}}},[`${t}-vertical`]:{[`${t}-item`]:{[`${t}-item-extra`]:{marginInlineStart:l}}}},[`@media screen and (max-width: ${n}px)`]:{[t]:{[`${t}-item`]:{flexWrap:"wrap",[`${t}-action`]:{marginInlineStart:p}}},[`${t}-vertical`]:{[`${t}-item`]:{flexWrap:"wrap-reverse",[`${t}-item-main`]:{minWidth:e.contentWidth},[`${t}-item-extra`]:{margin:`auto auto ${j(g)}`}}}}}},st=e=>{const{componentCls:t,antCls:n,controlHeight:a,minHeight:l,paddingSM:p,marginLG:g,padding:h,itemPadding:d,colorPrimary:o,itemPaddingSM:v,itemPaddingLG:y,paddingXS:f,margin:s,colorText:m,colorTextDescription:u,motionDurationSlow:$,lineWidth:T,headerBg:b,footerBg:z,emptyTextPadding:L,metaMarginBottom:A,avatarMarginRight:P,titleMarginBottom:C,descriptionFontSize:x}=e;return{[t]:Object.assign(Object.assign({},be(e)),{position:"relative","*":{outline:"none"},[`${t}-header`]:{background:b},[`${t}-footer`]:{background:z},[`${t}-header, ${t}-footer`]:{paddingBlock:p},[`${t}-pagination`]:{marginBlockStart:g,[`${n}-pagination-options`]:{textAlign:"start"}},[`${t}-spin`]:{minHeight:l,textAlign:"center"},[`${t}-items`]:{margin:0,padding:0,listStyle:"none"},[`${t}-item`]:{display:"flex",alignItems:"center",justifyContent:"space-between",padding:d,color:m,[`${t}-item-meta`]:{display:"flex",flex:1,alignItems:"flex-start",maxWidth:"100%",[`${t}-item-meta-avatar`]:{marginInlineEnd:P},[`${t}-item-meta-content`]:{flex:"1 0",width:0,color:m},[`${t}-item-meta-title`]:{margin:`0 0 ${j(e.marginXXS)} 0`,color:m,fontSize:e.fontSize,lineHeight:e.lineHeight,"> a":{color:m,transition:`all ${$}`,"&:hover":{color:o}}},[`${t}-item-meta-description`]:{color:u,fontSize:x,lineHeight:e.lineHeight}},[`${t}-item-action`]:{flex:"0 0 auto",marginInlineStart:e.marginXXL,padding:0,fontSize:0,listStyle:"none","& > li":{position:"relative",display:"inline-block",padding:`0 ${j(f)}`,color:u,fontSize:e.fontSize,lineHeight:e.lineHeight,textAlign:"center","&:first-child":{paddingInlineStart:0}},[`${t}-item-action-split`]:{position:"absolute",insetBlockStart:"50%",insetInlineEnd:0,width:T,height:e.calc(e.fontHeight).sub(e.calc(e.marginXXS).mul(2)).equal(),transform:"translateY(-50%)",backgroundColor:e.colorSplit}}},[`${t}-empty`]:{padding:`${j(h)} 0`,color:u,fontSize:e.fontSizeSM,textAlign:"center"},[`${t}-empty-text`]:{padding:L,color:e.colorTextDisabled,fontSize:e.fontSize,textAlign:"center"},[`${t}-item-no-flex`]:{display:"block"}}),[`${t}-grid ${n}-col > ${t}-item`]:{display:"block",maxWidth:"100%",marginBlockEnd:s,paddingBlock:0,borderBlockEnd:"none"},[`${t}-vertical ${t}-item`]:{alignItems:"initial",[`${t}-item-main`]:{display:"block",flex:1},[`${t}-item-extra`]:{marginInlineStart:g},[`${t}-item-meta`]:{marginBlockEnd:A,[`${t}-item-meta-title`]:{marginBlockStart:0,marginBlockEnd:C,color:m,fontSize:e.fontSizeLG,lineHeight:e.lineHeightLG}},[`${t}-item-action`]:{marginBlockStart:h,marginInlineStart:"auto","> li":{padding:`0 ${j(h)}`,"&:first-child":{paddingInlineStart:0}}}},[`${t}-split ${t}-item`]:{borderBlockEnd:`${j(e.lineWidth)} ${e.lineType} ${e.colorSplit}`,"&:last-child":{borderBlockEnd:"none"}},[`${t}-split ${t}-header`]:{borderBlockEnd:`${j(e.lineWidth)} ${e.lineType} ${e.colorSplit}`},[`${t}-split${t}-empty ${t}-footer`]:{borderTop:`${j(e.lineWidth)} ${e.lineType} ${e.colorSplit}`},[`${t}-loading ${t}-spin-nested-loading`]:{minHeight:a},[`${t}-split${t}-something-after-last-item ${n}-spin-container > ${t}-items > ${t}-item:last-child`]:{borderBlockEnd:`${j(e.lineWidth)} ${e.lineType} ${e.colorSplit}`},[`${t}-lg ${t}-item`]:{padding:y},[`${t}-sm ${t}-item`]:{padding:v},[`${t}:not(${t}-vertical)`]:{[`${t}-item-no-flex`]:{[`${t}-item-action`]:{float:"right"}}}}},ot=e=>({contentWidth:220,itemPadding:`${j(e.paddingContentVertical)} 0`,itemPaddingSM:`${j(e.paddingContentVerticalSM)} ${j(e.paddingContentHorizontal)}`,itemPaddingLG:`${j(e.paddingContentVerticalLG)} ${j(e.paddingContentHorizontalLG)}`,headerBg:"transparent",footerBg:"transparent",emptyTextPadding:e.padding,metaMarginBottom:e.padding,avatarMarginRight:e.padding,titleMarginBottom:e.paddingSM,descriptionFontSize:e.fontSize}),ct=$e("List",e=>{const t=Se(e,{listBorderedCls:`${e.componentCls}-bordered`,minHeight:e.controlHeightLG});return[st(t),rt(t),lt(t)]},ot);var dt=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,a=Object.getOwnPropertySymbols(e);l<a.length;l++)t.indexOf(a[l])<0&&Object.prototype.propertyIsEnumerable.call(e,a[l])&&(n[a[l]]=e[a[l]]);return n};function mt(e,t){const{pagination:n=!1,prefixCls:a,bordered:l=!1,split:p=!0,className:g,rootClassName:h,style:d,children:o,itemLayout:v,loadMore:y,grid:f,dataSource:s=[],size:m,header:u,footer:$,loading:T=!1,rowKey:b,renderItem:z,locale:L}=e,A=dt(e,["pagination","prefixCls","bordered","split","className","rootClassName","style","children","itemLayout","loadMore","grid","dataSource","size","header","footer","loading","rowKey","renderItem","locale"]),P=n&&typeof n=="object"?n:{},[C,x]=r.useState(P.defaultCurrent||1),[I,q]=r.useState(P.defaultPageSize||10),{getPrefixCls:K,direction:U,className:J,style:W}=Ce("list"),{renderEmpty:H}=r.useContext(ce),Y={current:1,total:0,position:"bottom"},Q=S=>(B,k)=>{var oe;x(B),q(k),n&&((oe=n==null?void 0:n[S])===null||oe===void 0||oe.call(n,B,k))},c=Q("onChange"),N=Q("onShowSizeChange"),ie=(S,B)=>{if(!z)return null;let k;return typeof b=="function"?k=b(S):b?k=S[b]:k=S.key,k||(k=`list-item-${B}`),r.createElement(r.Fragment,{key:k},z(S,B))},Oe=!!(y||n||$),E=K("list",a),[Te,Ee,Me]=ct(E);let G=T;typeof G=="boolean"&&(G={spinning:G});const re=!!(G!=null&&G.spinning),Re=_e(m);let Z="";switch(Re){case"large":Z="lg";break;case"small":Z="sm";break}const Pe=F(E,{[`${E}-vertical`]:v==="vertical",[`${E}-${Z}`]:Z,[`${E}-split`]:p,[`${E}-bordered`]:l,[`${E}-loading`]:re,[`${E}-grid`]:!!f,[`${E}-something-after-last-item`]:Oe,[`${E}-rtl`]:U==="rtl"},J,g,h,Ee,Me),_=Ge(Y,{total:s.length,current:C,pageSize:I},n||{}),Le=Math.ceil(_.total/_.pageSize);_.current=Math.min(_.current,Le);const me=n&&r.createElement("div",{className:F(`${E}-pagination`)},r.createElement(qe,Object.assign({align:"end"},_,{onChange:c,onShowSizeChange:N})));let le=ge(s);n&&s.length>(_.current-1)*_.pageSize&&(le=ge(s).splice((_.current-1)*_.pageSize,_.pageSize));const Ie=Object.keys(f||{}).some(S=>["xs","sm","md","lg","xl","xxl"].includes(S)),ue=He(Ie),ee=r.useMemo(()=>{for(let S=0;S<fe.length;S+=1){const B=fe[S];if(ue[B])return B}},[ue]),Ne=r.useMemo(()=>{if(!f)return;const S=ee&&f[ee]?f[ee]:f.column;if(S)return{width:`${100/S}%`,maxWidth:`${100/S}%`}},[JSON.stringify(f),ee]);let se=re&&r.createElement("div",{style:{minHeight:53}});if(le.length>0){const S=le.map(ie);se=f?r.createElement(X,{gutter:f.gutter},r.Children.map(S,B=>r.createElement("div",{key:B==null?void 0:B.key,style:Ne},B))):r.createElement("ul",{className:`${E}-items`},S)}else!o&&!re&&(se=r.createElement("div",{className:`${E}-empty-text`},(L==null?void 0:L.emptyText)||(H==null?void 0:H("List"))||r.createElement(Xe,{componentName:"List"})));const te=_.position,Be=r.useMemo(()=>({grid:f,itemLayout:v}),[JSON.stringify(f),v]);return Te(r.createElement(de.Provider,{value:Be},r.createElement("div",Object.assign({ref:t,style:Object.assign(Object.assign({},W),d),className:Pe},A),(te==="top"||te==="both")&&me,u&&r.createElement("div",{className:`${E}-header`},u),r.createElement(Ae,Object.assign({},G),se,o),$&&r.createElement("div",{className:`${E}-footer`},$),y||(te==="bottom"||te==="both")&&me)))}const ut=r.forwardRef(mt),ae=ut;ae.Item=ze;var gt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0051.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"}},{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}}]},name:"check-circle",theme:"outlined"},ft=function(t,n){return r.createElement(D,V({},t,{ref:n,icon:gt}))},pt=r.forwardRef(ft),ht={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372zm47.7-395.2l-25.4-5.9V348.6c38 5.2 61.5 29 65.5 58.2.5 4 3.9 6.9 7.9 6.9h44.9c4.7 0 8.4-4.1 8-8.8-6.1-62.3-57.4-102.3-125.9-109.2V263c0-4.4-3.6-8-8-8h-28.1c-4.4 0-8 3.6-8 8v33c-70.8 6.9-126.2 46-126.2 119 0 67.6 49.8 100.2 102.1 112.7l24.7 6.3v142.7c-44.2-5.9-69-29.5-74.1-61.3-.6-3.8-4-6.6-7.9-6.6H363c-4.7 0-8.4 4-8 8.7 4.5 55 46.2 105.6 135.2 112.1V761c0 4.4 3.6 8 8 8h28.4c4.4 0 8-3.6 8-8.1l-.2-31.7c78.3-6.9 134.3-48.8 134.3-124-.1-69.4-44.2-100.4-109-116.4zm-68.6-16.2c-5.6-1.6-10.3-3.1-15-5-33.8-12.2-49.5-31.9-49.5-57.3 0-36.3 27.5-57 64.5-61.7v124zM534.3 677V543.3c3.1.9 5.9 1.6 8.8 2.2 47.3 14.4 63.2 34.4 63.2 65.1 0 39.1-29.4 62.6-72 66.4z"}}]},name:"dollar",theme:"outlined"},xt=function(t,n){return r.createElement(D,V({},t,{ref:n,icon:ht}))},vt=r.forwardRef(xt),yt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M925.9 804l-24-199.2c-.8-6.6-8.9-9.4-13.6-4.7L829 659.5 557.7 388.3c-6.3-6.2-16.4-6.2-22.6 0L433.3 490 156.6 213.3a8.03 8.03 0 00-11.3 0l-45 45.2a8.03 8.03 0 000 11.3L422 591.7c6.2 6.3 16.4 6.3 22.6 0L546.4 490l226.1 226-59.3 59.3a8.01 8.01 0 004.7 13.6l199.2 24c5.1.7 9.5-3.7 8.8-8.9z"}}]},name:"fall",theme:"outlined"},$t=function(t,n){return r.createElement(D,V({},t,{ref:n,icon:yt}))},St=r.forwardRef($t),bt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M855.7 210.8l-42.4-42.4a8.03 8.03 0 00-11.3 0L168.3 801.9a8.03 8.03 0 000 11.3l42.4 42.4c3.1 3.1 8.2 3.1 11.3 0L855.6 222c3.2-3 3.2-8.1.1-11.2zM304 448c79.4 0 144-64.6 144-144s-64.6-144-144-144-144 64.6-144 144 64.6 144 144 144zm0-216c39.7 0 72 32.3 72 72s-32.3 72-72 72-72-32.3-72-72 32.3-72 72-72zm416 344c-79.4 0-144 64.6-144 144s64.6 144 144 144 144-64.6 144-144-64.6-144-144-144zm0 216c-39.7 0-72-32.3-72-72s32.3-72 72-72 72 32.3 72 72-32.3 72-72 72z"}}]},name:"percentage",theme:"outlined"},Ct=function(t,n){return r.createElement(D,V({},t,{ref:n,icon:bt}))},jt=r.forwardRef(Ct),zt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M917 211.1l-199.2 24c-6.6.8-9.4 8.9-4.7 13.6l59.3 59.3-226 226-101.8-101.7c-6.3-6.3-16.4-6.2-22.6 0L100.3 754.1a8.03 8.03 0 000 11.3l45 45.2c3.1 3.1 8.2 3.1 11.3 0L433.3 534 535 635.7c6.3 6.2 16.4 6.2 22.6 0L829 364.5l59.3 59.3a8.01 8.01 0 0013.6-4.7l24-199.2c.7-5.1-3.7-9.5-8.9-8.8z"}}]},name:"rise",theme:"outlined"},wt=function(t,n){return r.createElement(D,V({},t,{ref:n,icon:zt}))},Ot=r.forwardRef(wt),Tt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M848 359.3H627.7L825.8 109c4.1-5.3.4-13-6.3-13H436c-2.8 0-5.5 1.5-6.9 4L170 547.5c-3.1 5.3.7 12 6.9 12h174.4l-89.4 357.6c-1.9 7.8 7.5 13.3 13.3 7.7L853.5 373c5.2-4.9 1.7-13.7-5.5-13.7zM378.2 732.5l60.3-241H281.1l189.6-327.4h224.6L487 427.4h211L378.2 732.5z"}}]},name:"thunderbolt",theme:"outlined"},Et=function(t,n){return r.createElement(D,V({},t,{ref:n,icon:Tt}))},xe=r.forwardRef(Et),Mt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M868 160h-92v-40c0-4.4-3.6-8-8-8H256c-4.4 0-8 3.6-8 8v40h-92a44 44 0 00-44 44v148c0 81.7 60 149.6 138.2 162C265.7 630.2 359 721.7 476 734.5v105.2H280c-17.7 0-32 14.3-32 32V904c0 4.4 3.6 8 8 8h512c4.4 0 8-3.6 8-8v-32.3c0-17.7-14.3-32-32-32H548V734.5C665 721.7 758.3 630.2 773.8 514 852 501.6 912 433.7 912 352V204a44 44 0 00-44-44zM184 352V232h64v207.6a91.99 91.99 0 01-64-87.6zm520 128c0 49.1-19.1 95.4-53.9 130.1-34.8 34.8-81 53.9-130.1 53.9h-16c-49.1 0-95.4-19.1-130.1-53.9-34.8-34.8-53.9-81-53.9-130.1V184h384v296zm136-128c0 41-26.9 75.8-64 87.6V232h64v120z"}}]},name:"trophy",theme:"outlined"},Rt=function(t,n){return r.createElement(D,V({},t,{ref:n,icon:Mt}))},ve=r.forwardRef(Rt),Pt={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M464 720a48 48 0 1096 0 48 48 0 10-96 0zm16-304v184c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V416c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8zm475.7 440l-416-720c-6.2-10.7-16.9-16-27.7-16s-21.6 5.3-27.7 16l-416 720C56 877.4 71.4 904 96 904h832c24.6 0 40-26.6 27.7-48zm-783.5-27.9L512 239.9l339.8 588.2H172.2z"}}]},name:"warning",theme:"outlined"},Lt=function(t,n){return r.createElement(D,V({},t,{ref:n,icon:Pt}))},It=r.forwardRef(Lt);const Nt=e=>{const{value:t,formatter:n,precision:a,decimalSeparator:l,groupSeparator:p="",prefixCls:g}=e;let h;if(typeof n=="function")h=n(t);else{const d=String(t),o=d.match(/^(-?)(\d*)(\.(\d+))?$/);if(!o||d==="-")h=d;else{const v=o[1];let y=o[2]||"0",f=o[4]||"";y=y.replace(/\B(?=(\d{3})+(?!\d))/g,p),typeof a=="number"&&(f=f.padEnd(a,"0").slice(0,a>0?a:0)),f&&(f=`${l}${f}`),h=[r.createElement("span",{key:"int",className:`${g}-content-value-int`},v,y),f&&r.createElement("span",{key:"decimal",className:`${g}-content-value-decimal`},f)]}}return r.createElement("span",{className:`${g}-content-value`},h)},Bt=e=>{const{componentCls:t,marginXXS:n,padding:a,colorTextDescription:l,titleFontSize:p,colorTextHeading:g,contentFontSize:h,fontFamily:d}=e;return{[t]:Object.assign(Object.assign({},be(e)),{[`${t}-title`]:{marginBottom:n,color:l,fontSize:p},[`${t}-skeleton`]:{paddingTop:a},[`${t}-content`]:{color:g,fontSize:h,fontFamily:d,[`${t}-content-value`]:{display:"inline-block",direction:"ltr"},[`${t}-content-prefix, ${t}-content-suffix`]:{display:"inline-block"},[`${t}-content-prefix`]:{marginInlineEnd:n},[`${t}-content-suffix`]:{marginInlineStart:n}}})}},_t=e=>{const{fontSizeHeading3:t,fontSize:n}=e;return{titleFontSize:n,contentFontSize:t}},Ht=$e("Statistic",e=>{const t=Se(e,{});return[Bt(t)]},_t);var At=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,a=Object.getOwnPropertySymbols(e);l<a.length;l++)t.indexOf(a[l])<0&&Object.prototype.propertyIsEnumerable.call(e,a[l])&&(n[a[l]]=e[a[l]]);return n};const w=r.forwardRef((e,t)=>{const{prefixCls:n,className:a,rootClassName:l,style:p,valueStyle:g,value:h=0,title:d,valueRender:o,prefix:v,suffix:y,loading:f=!1,formatter:s,precision:m,decimalSeparator:u=".",groupSeparator:$=",",onMouseEnter:T,onMouseLeave:b}=e,z=At(e,["prefixCls","className","rootClassName","style","valueStyle","value","title","valueRender","prefix","suffix","loading","formatter","precision","decimalSeparator","groupSeparator","onMouseEnter","onMouseLeave"]),{getPrefixCls:L,direction:A,className:P,style:C}=Ce("statistic"),x=L("statistic",n),[I,q,K]=Ht(x),U=r.createElement(Nt,{decimalSeparator:u,groupSeparator:$,prefixCls:x,formatter:s,precision:m,value:h}),J=F(x,{[`${x}-rtl`]:A==="rtl"},P,a,l,q,K),W=r.useRef(null);r.useImperativeHandle(t,()=>({nativeElement:W.current}));const H=Qe(z,{aria:!0,data:!0});return I(r.createElement("div",Object.assign({},H,{ref:W,className:J,style:Object.assign(Object.assign({},C),p),onMouseEnter:T,onMouseLeave:b}),d&&r.createElement("div",{className:`${x}-title`},d),r.createElement(Ze,{paragraph:!1,loading:f,className:`${x}-skeleton`},r.createElement("div",{style:g,className:`${x}-content`},v&&r.createElement("span",{className:`${x}-content-prefix`},v),o?o(U):U,y&&r.createElement("span",{className:`${x}-content-suffix`},y)))))}),Wt=[["Y",1e3*60*60*24*365],["M",1e3*60*60*24*30],["D",1e3*60*60*24],["H",1e3*60*60],["m",1e3*60],["s",1e3],["S",1]];function kt(e,t){let n=e;const a=/\[[^\]]*]/g,l=(t.match(a)||[]).map(d=>d.slice(1,-1)),p=t.replace(a,"[]"),g=Wt.reduce((d,[o,v])=>{if(d.includes(o)){const y=Math.floor(n/v);return n-=y*v,d.replace(new RegExp(`${o}+`,"g"),f=>{const s=f.length;return y.toString().padStart(s,"0")})}return d},p);let h=0;return g.replace(a,()=>{const d=l[h];return h+=1,d})}function Dt(e,t,n){const{format:a=""}=t,l=new Date(e).getTime(),p=Date.now(),g=Math.max(n?l-p:p-l,0);return kt(g,a)}var Vt=function(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var l=0,a=Object.getOwnPropertySymbols(e);l<a.length;l++)t.indexOf(a[l])<0&&Object.prototype.propertyIsEnumerable.call(e,a[l])&&(n[a[l]]=e[a[l]]);return n};function Ft(e){return new Date(e).getTime()}const we=e=>{const{value:t,format:n="HH:mm:ss",onChange:a,onFinish:l,type:p}=e,g=Vt(e,["value","format","onChange","onFinish","type"]),h=p==="countdown",[d,o]=r.useState(null),v=We(()=>{const s=Date.now(),m=Ft(t);o({});const u=h?m-s:s-m;return a==null||a(u),h&&m<s?(l==null||l(),!1):!0});r.useEffect(()=>{let s;const m=()=>pe.cancel(s),u=()=>{s=pe(()=>{v()&&u()})};return u(),m},[t,h]),r.useEffect(()=>{o({})},[]);const y=(s,m)=>d?Dt(s,Object.assign(Object.assign({},m),{format:n}),h):"-",f=s=>ye(s,{title:void 0});return r.createElement(w,Object.assign({},g,{value:t,valueRender:f,formatter:y}))},Gt=e=>r.createElement(we,Object.assign({},e,{type:"countdown"})),Xt=r.memo(Gt);w.Timer=we;w.Countdown=Xt;const{Title:Ut,Paragraph:qt}=De,tn=()=>{const{trades:e}=ke(),{getWalletRemark:t}=Ke(),[n,a]=r.useState(!1),[l,p]=r.useState(null),{data:g,isLoading:h,error:d}=Ue({queryKey:["walletConfigs"],queryFn:Fe.getWalletConfigurations,refetchInterval:3e4}),o=O.useMemo(()=>{if(!e||e.length===0)return{totalTrades:0,buyTrades:0,sellTrades:0,successTrades:0,failedTrades:0,totalUsdAmount:0,totalSolAmount:0,totalProfit:0,profitableTrades:0,lossTrades:0,avgTradeAmount:0,maxTradeAmount:0,minTradeAmount:0,buySuccessRate:0,sellSuccessRate:0,overallSuccessRate:0,profitRate:0,currentHoldings:0,uniqueTokens:0};const s=e.filter(c=>c.status==="Confirmed"),m=s.filter(c=>c.trade_type.toLowerCase()==="buy"),u=s.filter(c=>c.trade_type.toLowerCase()==="sell"),$=e.filter(c=>c.status==="Failed"),T=s.reduce((c,N)=>c+N.usd_amount,0),b=s.reduce((c,N)=>c+N.sol_amount,0),z=u.reduce((c,N)=>c+(N.profit_usd||0),0),L=u.filter(c=>(c.profit_usd||0)>0).length,A=u.filter(c=>(c.profit_usd||0)<0).length,P=s.map(c=>c.usd_amount),C=P.length>0?Math.max(...P):0,x=P.length>0?Math.min(...P):0,I=s.length>0?T/s.length:0,q=e.filter(c=>c.trade_type.toLowerCase()==="buy").length>0?m.length/e.filter(c=>c.trade_type.toLowerCase()==="buy").length*100:0,K=e.filter(c=>c.trade_type.toLowerCase()==="sell").length>0?u.length/e.filter(c=>c.trade_type.toLowerCase()==="sell").length*100:0,U=e.length>0?s.length/e.length*100:0,J=u.length>0?L/u.length*100:0,W=new Map,H=new Map;m.forEach(c=>{const N=W.get(c.mint)||0;W.set(c.mint,N+c.token_amount)}),u.forEach(c=>{const N=H.get(c.mint)||0;H.set(c.mint,N+c.token_amount)});let Y=0;const Q=new Set([...W.keys(),...H.keys()]).size;return W.forEach((c,N)=>{const ie=H.get(N)||0;c>ie&&Y++}),{totalTrades:e.length,buyTrades:m.length,sellTrades:u.length,successTrades:s.length,failedTrades:$.length,totalUsdAmount:T,totalSolAmount:b,totalProfit:z,profitableTrades:L,lossTrades:A,avgTradeAmount:I,maxTradeAmount:C,minTradeAmount:x,buySuccessRate:q,sellSuccessRate:K,overallSuccessRate:U,profitRate:J,currentHoldings:Y,uniqueTokens:Q}},[e]),v=O.useMemo(()=>{if(!g)return{activeWallets:0,strategyCounts:{},strategyWallets:{}};const s=Object.values(g),m=s.filter(T=>T.is_active),u={},$={};return s.forEach(T=>{const b=T.take_profit_strategy||"none";u[b]=(u[b]||0)+1,$[b]||($[b]=[]),$[b].push(T)}),{activeWallets:m.length,strategyCounts:u,strategyWallets:$}},[g]),y=s=>{const u={standard:"标准分步",trailing:"追踪止盈",exponential:"指数加码",none:"未设置"}[s]||s,$=v.strategyWallets[s]||[];p({strategy:s,text:u,wallets:$}),a(!0)},f=async s=>{try{await navigator.clipboard.writeText(s),he.success("地址已复制到剪贴板")}catch{he.error("复制失败")}};return d?i.jsx(Je,{message:"数据加载失败",description:"无法获取钱包配置数据，请检查网络连接或稍后重试。",type:"error",showIcon:!0}):i.jsxs("div",{children:[i.jsx(Ut,{level:2,children:"系统概览"}),i.jsx(qt,{type:"secondary",children:"实时监控钱包跟单系统的运行状态和关键指标"}),i.jsxs(M,{title:i.jsxs("span",{children:[i.jsx(xe,{style:{marginRight:8}}),"交易统计"]}),extra:i.jsx(ne,{color:"green",children:"实时"}),style:{marginBottom:24},children:[i.jsxs(X,{gutter:[16,16],children:[i.jsx(R,{xs:24,sm:12,lg:6,children:i.jsx(M,{size:"small",children:i.jsx(w,{title:"总交易次数",value:o.totalTrades,prefix:i.jsx(nt,{}),valueStyle:{color:"#1890ff"}})})}),i.jsx(R,{xs:24,sm:12,lg:6,children:i.jsx(M,{size:"small",children:i.jsx(w,{title:"买入次数",value:o.buyTrades,prefix:i.jsx(Ot,{}),valueStyle:{color:"#52c41a"}})})}),i.jsx(R,{xs:24,sm:12,lg:6,children:i.jsx(M,{size:"small",children:i.jsx(w,{title:"卖出次数",value:o.sellTrades,prefix:i.jsx(St,{}),valueStyle:{color:"#f5222d"}})})}),i.jsx(R,{xs:24,sm:12,lg:6,children:i.jsx(M,{size:"small",children:i.jsx(w,{title:"成功率",value:o.overallSuccessRate,prefix:i.jsx(pt,{}),suffix:"%",precision:1,valueStyle:{color:o.overallSuccessRate>=80?"#52c41a":o.overallSuccessRate>=60?"#faad14":"#f5222d"}})})})]}),i.jsxs(X,{gutter:[16,16],style:{marginTop:16},children:[i.jsx(R,{xs:24,sm:12,lg:6,children:i.jsx(M,{size:"small",children:i.jsx(w,{title:"总交易额",value:o.totalUsdAmount,prefix:i.jsx(vt,{}),suffix:"USD",precision:2,valueStyle:{color:"#722ed1"}})})}),i.jsx(R,{xs:24,sm:12,lg:6,children:i.jsx(M,{size:"small",children:i.jsx(w,{title:"平均交易额",value:o.avgTradeAmount,prefix:i.jsx(jt,{}),suffix:"USD",precision:2,valueStyle:{color:"#13c2c2"}})})}),i.jsx(R,{xs:24,sm:12,lg:6,children:i.jsx(M,{size:"small",children:i.jsx(w,{title:"总盈亏",value:o.totalProfit,prefix:o.totalProfit>=0?i.jsx(ve,{}):i.jsx(It,{}),suffix:"USD",precision:4,valueStyle:{color:o.totalProfit>=0?"#52c41a":"#f5222d"}})})}),i.jsx(R,{xs:24,sm:12,lg:6,children:i.jsx(M,{size:"small",children:i.jsx(w,{title:"盈利率",value:o.profitRate,prefix:i.jsx(ve,{}),suffix:"%",precision:1,valueStyle:{color:o.profitRate>=60?"#52c41a":o.profitRate>=40?"#faad14":"#f5222d"}})})})]}),i.jsxs(X,{gutter:[16,16],style:{marginTop:16},children:[i.jsx(R,{xs:24,sm:12,lg:8,children:i.jsx(M,{size:"small",children:i.jsx(w,{title:"当前持仓",value:o.currentHoldings,suffix:"个代币",valueStyle:{color:"#fa8c16"}})})}),i.jsx(R,{xs:24,sm:12,lg:8,children:i.jsx(M,{size:"small",children:i.jsx(w,{title:"交易代币数",value:o.uniqueTokens,suffix:"种",valueStyle:{color:"#eb2f96"}})})}),i.jsx(R,{xs:24,sm:12,lg:8,children:i.jsx(M,{size:"small",children:i.jsx(w,{title:"买卖比例",value:o.buyTrades>0&&o.sellTrades>0?(o.buyTrades/o.sellTrades).toFixed(2):o.buyTrades>0?"∞":"0",suffix:o.buyTrades>0&&o.sellTrades>0?":1":"",valueStyle:{color:"#1890ff"}})})})]})]}),i.jsx(X,{gutter:[16,16],style:{marginBottom:24},children:i.jsx(R,{xs:24,children:i.jsx(M,{children:i.jsx(w,{title:"活跃钱包",value:v.activeWallets,prefix:i.jsx(xe,{}),loading:h,suffix:i.jsx(ne,{color:v.activeWallets>0?"green":"red",children:v.activeWallets>0?"运行中":"已停止"})})})})}),i.jsx(X,{gutter:[16,16],style:{marginBottom:24},children:i.jsx(R,{xs:24,children:i.jsx(M,{title:"策略分布统计",children:i.jsx(X,{gutter:16,children:Object.entries(v.strategyCounts).map(([s,m])=>{const $={standard:{text:"标准分步",color:"blue"},trailing:{text:"追踪止盈",color:"green"},exponential:{text:"指数加码",color:"orange"},none:{text:"未设置",color:"default"}}[s]||{text:s,color:"default"};return i.jsx(R,{xs:12,sm:8,md:6,children:i.jsx(M,{size:"small",hoverable:!0,onClick:()=>y(s),style:{cursor:"pointer"},children:i.jsx(w,{title:i.jsx(ne,{color:$.color,children:$.text}),value:m,suffix:"个钱包"})})},s)})})})})}),i.jsx(Ye,{title:`${(l==null?void 0:l.text)||""} - 钱包列表`,open:n,onCancel:()=>a(!1),footer:null,width:600,children:l&&i.jsx(ae,{dataSource:l.wallets,renderItem:s=>{const m=t(s.wallet_address),u=s.is_active;return i.jsx(ae.Item,{actions:[i.jsx(Ve,{type:"link",size:"small",onClick:()=>f(s.wallet_address),children:"复制地址"})],children:i.jsx(ae.Item.Meta,{title:i.jsxs("div",{style:{display:"flex",alignItems:"center",gap:8},children:[i.jsx("span",{style:{fontWeight:"bold"},children:m}),i.jsx(ne,{color:u?"green":"red",children:u?"运行中":"已停止"})]}),description:i.jsxs("div",{children:[i.jsxs("div",{style:{fontSize:"12px",color:"#666",marginBottom:4},children:["地址: ",s.wallet_address]}),i.jsxs("div",{style:{fontSize:"12px",color:"#666"},children:["跟单比例: ",s.follow_percentage||0,"% | 滑点: ",s.slippage_percentage||0,"%"]})]})})})}})})]})};export{tn as default};
