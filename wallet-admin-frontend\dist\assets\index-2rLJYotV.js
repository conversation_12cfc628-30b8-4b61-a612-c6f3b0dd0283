import{r as n,I as te,_ as ne,C as L,b6 as re,c as K,a2 as U,g as ae,m as ie,M as le,K as ce,w as ue,i as oe,a7 as fe,o as Z,k as pe,a3 as de,ag as me,J as se,a1 as ve,d as ee,B as ge,b7 as Ce}from"./index-CW-Whzws.js";import{I as G,u as be}from"./Input-Cr3didPW.js";import{p as ye,c as Oe}from"./index-BVbup1Oj.js";var xe={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M942.2 486.2C847.4 286.5 704.1 186 512 186c-192.2 0-335.4 100.5-430.2 300.3a60.3 60.3 0 000 51.5C176.6 737.5 319.9 838 512 838c192.2 0 335.4-100.5 430.2-300.3 7.7-16.2 7.7-35 0-51.5zM512 766c-161.3 0-279.4-81.8-362.7-254C232.6 339.8 350.7 258 512 258c161.3 0 279.4 81.8 362.7 254C791.5 684.2 673.4 766 512 766zm-4-430c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm0 288c-61.9 0-112-50.1-112-112s50.1-112 112-112 112 50.1 112 112-50.1 112-112 112z"}}]},name:"eye",theme:"outlined"},he=function(r,a){return n.createElement(te,ne({},r,{ref:a,icon:xe}))},Pe=n.forwardRef(he);const Ee=e=>{const{getPrefixCls:r,direction:a}=n.useContext(L),{prefixCls:t,className:o}=e,p=r("input-group",t),g=r("input"),[u,b,y]=re(g),O=K(p,y,{[`${p}-lg`]:e.size==="large",[`${p}-sm`]:e.size==="small",[`${p}-compact`]:e.compact,[`${p}-rtl`]:a==="rtl"},b,o),C=n.useContext(U),x=n.useMemo(()=>Object.assign(Object.assign({},C),{isFormItemInput:!1}),[C]);return u(n.createElement("span",{className:O,style:e.style,onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,onFocus:e.onFocus,onBlur:e.onBlur},n.createElement(U.Provider,{value:x},e.children)))},Ie=e=>{const{componentCls:r,paddingXS:a}=e;return{[r]:{display:"inline-flex",alignItems:"center",flexWrap:"nowrap",columnGap:a,[`${r}-input-wrapper`]:{position:"relative",[`${r}-mask-icon`]:{position:"absolute",zIndex:"1",top:"50%",right:"50%",transform:"translate(50%, -50%)",pointerEvents:"none"},[`${r}-mask-input`]:{color:"transparent",caretColor:"var(--ant-color-text)"},[`${r}-mask-input[type=number]::-webkit-inner-spin-button`]:{"-webkit-appearance":"none",margin:0},[`${r}-mask-input[type=number]`]:{"-moz-appearance":"textfield"}},"&-rtl":{direction:"rtl"},[`${r}-input`]:{textAlign:"center",paddingInline:e.paddingXXS},[`&${r}-sm ${r}-input`]:{paddingInline:e.calc(e.paddingXXS).div(2).equal()},[`&${r}-lg ${r}-input`]:{paddingInline:e.paddingXS}}}},we=ae(["Input","OTP"],e=>{const r=ie(e,le(e));return[Ie(r)]},ce);var Se=function(e,r){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,t=Object.getOwnPropertySymbols(e);o<t.length;o++)r.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(a[t[o]]=e[t[o]]);return a};const $e=n.forwardRef((e,r)=>{const{className:a,value:t,onChange:o,onActiveChange:p,index:g,mask:u}=e,b=Se(e,["className","value","onChange","onActiveChange","index","mask"]),{getPrefixCls:y}=n.useContext(L),O=y("otp"),C=typeof u=="string"?u:t,x=n.useRef(null);n.useImperativeHandle(r,()=>x.current);const j=m=>{o(g,m.target.value)},h=()=>{ue(()=>{var m;const d=(m=x.current)===null||m===void 0?void 0:m.input;document.activeElement===d&&d&&d.select()})},S=m=>{const{key:d,ctrlKey:_,metaKey:z}=m;d==="ArrowLeft"?p(g-1):d==="ArrowRight"?p(g+1):d==="z"&&(_||z)&&m.preventDefault(),h()},$=m=>{m.key==="Backspace"&&!t&&p(g-1),h()};return n.createElement("span",{className:`${O}-input-wrapper`,role:"presentation"},u&&t!==""&&t!==void 0&&n.createElement("span",{className:`${O}-mask-icon`,"aria-hidden":"true"},C),n.createElement(G,Object.assign({"aria-label":`OTP Input ${g+1}`,type:u===!0?"password":"text"},b,{ref:x,value:t,onInput:j,onFocus:h,onKeyDown:S,onKeyUp:$,onMouseDown:h,onMouseUp:h,className:K(a,{[`${O}-mask-input`]:u})})))});var ze=function(e,r){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,t=Object.getOwnPropertySymbols(e);o<t.length;o++)r.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(a[t[o]]=e[t[o]]);return a};function q(e){return(e||"").split("")}const je=e=>{const{index:r,prefixCls:a,separator:t}=e,o=typeof t=="function"?t(r):t;return o?n.createElement("span",{className:`${a}-separator`},o):null},ke=n.forwardRef((e,r)=>{const{prefixCls:a,length:t=6,size:o,defaultValue:p,value:g,onChange:u,formatter:b,separator:y,variant:O,disabled:C,status:x,autoFocus:j,mask:h,type:S,onInput:$,inputMode:m}=e,d=ze(e,["prefixCls","length","size","defaultValue","value","onChange","formatter","separator","variant","disabled","status","autoFocus","mask","type","onInput","inputMode"]),{getPrefixCls:_,direction:z}=n.useContext(L),v=_("otp",a),T=ye(d,{aria:!0,data:!0,attr:!0}),[F,k,R]=we(v),P=oe(s=>o??s),E=n.useContext(U),I=fe(E.status,x),D=n.useMemo(()=>Object.assign(Object.assign({},E),{status:I,hasFeedback:!1,feedbackIcon:null}),[E,I]),N=n.useRef(null),M=n.useRef({});n.useImperativeHandle(r,()=>({focus:()=>{var s;(s=M.current[0])===null||s===void 0||s.focus()},blur:()=>{var s;for(let i=0;i<t;i+=1)(s=M.current[i])===null||s===void 0||s.blur()},nativeElement:N.current}));const A=s=>b?b(s):s,[w,V]=n.useState(()=>q(A(p||"")));n.useEffect(()=>{g!==void 0&&V(q(g))},[g]);const H=Z(s=>{V(s),$&&$(s),u&&s.length===t&&s.every(i=>i)&&s.some((i,c)=>w[c]!==i)&&u(s.join(""))}),Q=Z((s,i)=>{let c=pe(w);for(let f=0;f<s;f+=1)c[f]||(c[f]="");i.length<=1?c[s]=i:c=c.slice(0,s).concat(q(i)),c=c.slice(0,t);for(let f=c.length-1;f>=0&&!c[f];f-=1)c.pop();const B=A(c.map(f=>f||" ").join(""));return c=q(B).map((f,Y)=>f===" "&&!c[Y]?c[Y]:f),c}),J=(s,i)=>{var c;const B=Q(s,i),f=Math.min(s+i.length,t-1);f!==s&&B[s]!==void 0&&((c=M.current[f])===null||c===void 0||c.focus()),H(B)},W=s=>{var i;(i=M.current[s])===null||i===void 0||i.focus()},l={variant:O,disabled:C,status:I,mask:h,type:S,inputMode:m};return F(n.createElement("div",Object.assign({},T,{ref:N,className:K(v,{[`${v}-sm`]:P==="small",[`${v}-lg`]:P==="large",[`${v}-rtl`]:z==="rtl"},R,k),role:"group"}),n.createElement(U.Provider,{value:D},Array.from({length:t}).map((s,i)=>{const c=`otp-${i}`,B=w[i]||"";return n.createElement(n.Fragment,{key:c},n.createElement($e,Object.assign({ref:f=>{M.current[i]=f},index:i,size:P,htmlSize:1,className:`${v}-input`,onChange:J,value:B,onActiveChange:W,autoFocus:i===0&&j},l)),i<t-1&&n.createElement(je,{separator:y,index:i,prefixCls:v}))}))))});var _e={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z"}},{tag:"path",attrs:{d:"M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z"}}]},name:"eye-invisible",theme:"outlined"},Re=function(r,a){return n.createElement(te,ne({},r,{ref:a,icon:_e}))},Ne=n.forwardRef(Re),Me=function(e,r){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,t=Object.getOwnPropertySymbols(e);o<t.length;o++)r.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(a[t[o]]=e[t[o]]);return a};const Ae=e=>e?n.createElement(Pe,null):n.createElement(Ne,null),Te={click:"onClick",hover:"onMouseOver"},Be=n.forwardRef((e,r)=>{const{disabled:a,action:t="click",visibilityToggle:o=!0,iconRender:p=Ae}=e,g=n.useContext(de),u=a??g,b=typeof o=="object"&&o.visible!==void 0,[y,O]=n.useState(()=>b?o.visible:!1),C=n.useRef(null);n.useEffect(()=>{b&&O(o.visible)},[b,o]);const x=be(C),j=()=>{var P;if(u)return;y&&x();const E=!y;O(E),typeof o=="object"&&((P=o.onVisibleChange)===null||P===void 0||P.call(o,E))},h=P=>{const E=Te[t]||"",I=p(y),D={[E]:j,className:`${P}-icon`,key:"passwordIcon",onMouseDown:N=>{N.preventDefault()},onMouseUp:N=>{N.preventDefault()}};return n.cloneElement(n.isValidElement(I)?I:n.createElement("span",null,I),D)},{className:S,prefixCls:$,inputPrefixCls:m,size:d}=e,_=Me(e,["className","prefixCls","inputPrefixCls","size"]),{getPrefixCls:z}=n.useContext(L),v=z("input",m),T=z("input-password",$),F=o&&h(T),k=K(T,S,{[`${T}-${d}`]:!!d}),R=Object.assign(Object.assign({},me(_,["suffix","iconRender","visibilityToggle"])),{type:y?"text":"password",className:k,prefixCls:v,suffix:F});return d&&(R.size=d),n.createElement(G,Object.assign({ref:se(r,C)},R))});var Fe=function(e,r){var a={};for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.indexOf(t)<0&&(a[t]=e[t]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,t=Object.getOwnPropertySymbols(e);o<t.length;o++)r.indexOf(t[o])<0&&Object.prototype.propertyIsEnumerable.call(e,t[o])&&(a[t[o]]=e[t[o]]);return a};const De=n.forwardRef((e,r)=>{const{prefixCls:a,inputPrefixCls:t,className:o,size:p,suffix:g,enterButton:u=!1,addonAfter:b,loading:y,disabled:O,onSearch:C,onChange:x,onCompositionStart:j,onCompositionEnd:h,variant:S,onPressEnter:$}=e,m=Fe(e,["prefixCls","inputPrefixCls","className","size","suffix","enterButton","addonAfter","loading","disabled","onSearch","onChange","onCompositionStart","onCompositionEnd","variant","onPressEnter"]),{getPrefixCls:d,direction:_}=n.useContext(L),z=n.useRef(!1),v=d("input-search",a),T=d("input",t),{compactSize:F}=ve(v,_),k=oe(l=>{var s;return(s=p??F)!==null&&s!==void 0?s:l}),R=n.useRef(null),P=l=>{l!=null&&l.target&&l.type==="click"&&C&&C(l.target.value,l,{source:"clear"}),x==null||x(l)},E=l=>{var s;document.activeElement===((s=R.current)===null||s===void 0?void 0:s.input)&&l.preventDefault()},I=l=>{var s,i;C&&C((i=(s=R.current)===null||s===void 0?void 0:s.input)===null||i===void 0?void 0:i.value,l,{source:"input"})},D=l=>{z.current||y||($==null||$(l),I(l))},N=typeof u=="boolean"?n.createElement(Oe,null):null,M=`${v}-button`;let A;const w=u||{},V=w.type&&w.type.__ANT_BUTTON===!0;V||w.type==="button"?A=ee(w,Object.assign({onMouseDown:E,onClick:l=>{var s,i;(i=(s=w==null?void 0:w.props)===null||s===void 0?void 0:s.onClick)===null||i===void 0||i.call(s,l),I(l)},key:"enterButton"},V?{className:M,size:k}:{})):A=n.createElement(ge,{className:M,color:u?"primary":"default",size:k,disabled:O,key:"enterButton",onMouseDown:E,onClick:I,loading:y,icon:N,variant:S==="borderless"||S==="filled"||S==="underlined"?"text":u?"solid":void 0},u),b&&(A=[A,ee(b,{key:"addonAfter"})]);const H=K(v,{[`${v}-rtl`]:_==="rtl",[`${v}-${k}`]:!!k,[`${v}-with-button`]:!!u},o),Q=l=>{z.current=!0,j==null||j(l)},J=l=>{z.current=!1,h==null||h(l)},W=Object.assign(Object.assign({},m),{className:H,prefixCls:T,type:"search",size:k,variant:S,onPressEnter:D,onCompositionStart:Q,onCompositionEnd:J,addonAfter:A,suffix:g,onChange:P,disabled:O});return n.createElement(G,Object.assign({ref:se(R,r)},W))}),X=G;X.Group=Ee;X.Search=De;X.TextArea=Ce;X.Password=Be;X.OTP=ke;export{X as I,Pe as R};
