import{g as He,f as _e,e as O,r as a,h as Ct,c as R,aA as ve,J as bt,b8 as pt,am as Le,b9 as yt,ba as xe,bb as ht,bc as xt,q as $t,s as St,k as K,bd as Ot,al as De,aV as Et,B as Ce,ac as Fe,b as $,be as wt,ai as j,G as $e,_ as J,x as be,bf as Pt,bg as Se,ao as Oe,bh as It,ah as Nt,o as Rt,a9 as pe,bi as Ae,aG as Tt,bj as jt,m as Mt,ap as Bt,C as de,a0 as We,bk as zt,a6 as Ht,bl as _t,bm as ce,aN as Lt,bn as Dt,a8 as Ve,bo as Ft,bp as At,bq as Wt,br as Vt,aa as Gt,bs as qt}from"./index-CW-Whzws.js";import{p as ye,R as Ge,d as qe,b as Xe,a as he,g as Xt,S as Ut}from"./index-BVbup1Oj.js";import{a as Kt,p as Ee}from"./index-pOBwrJ9T.js";const se=(e,t,o,n,r)=>({background:e,border:`${O(n.lineWidth)} ${n.lineType} ${t}`,[`${r}-icon`]:{color:o}}),Zt=e=>{const{componentCls:t,motionDurationSlow:o,marginXS:n,marginSM:r,fontSize:s,fontSizeLG:d,lineHeight:c,borderRadiusLG:i,motionEaseInOutCirc:u,withDescriptionIconSize:l,colorText:f,colorTextHeading:g,withDescriptionPadding:b,defaultPadding:m}=e;return{[t]:Object.assign(Object.assign({},_e(e)),{position:"relative",display:"flex",alignItems:"center",padding:m,wordWrap:"break-word",borderRadius:i,[`&${t}-rtl`]:{direction:"rtl"},[`${t}-content`]:{flex:1,minWidth:0},[`${t}-icon`]:{marginInlineEnd:n,lineHeight:0},"&-description":{display:"none",fontSize:s,lineHeight:c},"&-message":{color:g},[`&${t}-motion-leave`]:{overflow:"hidden",opacity:1,transition:`max-height ${o} ${u}, opacity ${o} ${u},
        padding-top ${o} ${u}, padding-bottom ${o} ${u},
        margin-bottom ${o} ${u}`},[`&${t}-motion-leave-active`]:{maxHeight:0,marginBottom:"0 !important",paddingTop:0,paddingBottom:0,opacity:0}}),[`${t}-with-description`]:{alignItems:"flex-start",padding:b,[`${t}-icon`]:{marginInlineEnd:r,fontSize:l,lineHeight:0},[`${t}-message`]:{display:"block",marginBottom:n,color:g,fontSize:d},[`${t}-description`]:{display:"block",color:f}},[`${t}-banner`]:{marginBottom:0,border:"0 !important",borderRadius:0}}},Qt=e=>{const{componentCls:t,colorSuccess:o,colorSuccessBorder:n,colorSuccessBg:r,colorWarning:s,colorWarningBorder:d,colorWarningBg:c,colorError:i,colorErrorBorder:u,colorErrorBg:l,colorInfo:f,colorInfoBorder:g,colorInfoBg:b}=e;return{[t]:{"&-success":se(r,n,o,e,t),"&-info":se(b,g,f,e,t),"&-warning":se(c,d,s,e,t),"&-error":Object.assign(Object.assign({},se(l,u,i,e,t)),{[`${t}-description > pre`]:{margin:0,padding:0}})}}},Yt=e=>{const{componentCls:t,iconCls:o,motionDurationMid:n,marginXS:r,fontSizeIcon:s,colorIcon:d,colorIconHover:c}=e;return{[t]:{"&-action":{marginInlineStart:r},[`${t}-close-icon`]:{marginInlineStart:r,padding:0,overflow:"hidden",fontSize:s,lineHeight:O(s),backgroundColor:"transparent",border:"none",outline:"none",cursor:"pointer",[`${o}-close`]:{color:d,transition:`color ${n}`,"&:hover":{color:c}}},"&-close-text":{color:d,transition:`color ${n}`,"&:hover":{color:c}}}}},Jt=e=>({withDescriptionIconSize:e.fontSizeHeading3,defaultPadding:`${e.paddingContentVerticalSM}px 12px`,withDescriptionPadding:`${e.paddingMD}px ${e.paddingContentHorizontalLG}px`}),kt=He("Alert",e=>[Zt(e),Qt(e),Yt(e)],Jt);var we=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o};const en={success:Xe,info:qe,error:Le,warning:Ge},tn=e=>{const{icon:t,prefixCls:o,type:n}=e,r=en[n]||null;return t?pt(t,a.createElement("span",{className:`${o}-icon`},t),()=>({className:R(`${o}-icon`,t.props.className)})):a.createElement(r,{className:`${o}-icon`})},nn=e=>{const{isClosable:t,prefixCls:o,closeIcon:n,handleClose:r,ariaProps:s}=e,d=n===!0||n===void 0?a.createElement(he,null):n;return t?a.createElement("button",Object.assign({type:"button",onClick:r,className:`${o}-close-icon`,tabIndex:0},s),d):null},Ue=a.forwardRef((e,t)=>{const{description:o,prefixCls:n,message:r,banner:s,className:d,rootClassName:c,style:i,onMouseEnter:u,onMouseLeave:l,onClick:f,afterClose:g,showIcon:b,closable:m,closeText:v,closeIcon:p,action:y,id:C}=e,h=we(e,["description","prefixCls","message","banner","className","rootClassName","style","onMouseEnter","onMouseLeave","onClick","afterClose","showIcon","closable","closeText","closeIcon","action","id"]),[E,T]=a.useState(!1),x=a.useRef(null);a.useImperativeHandle(t,()=>({nativeElement:x.current}));const{getPrefixCls:N,direction:k,closable:M,closeIcon:q,className:D,style:S}=Ct("alert"),w=N("alert",n),[H,I,_]=kt(w),L=z=>{var W;T(!0),(W=e.onClose)===null||W===void 0||W.call(e,z)},F=a.useMemo(()=>e.type!==void 0?e.type:s?"warning":"info",[e.type,s]),Z=a.useMemo(()=>typeof m=="object"&&m.closeIcon||v?!0:typeof m=="boolean"?m:p!==!1&&p!==null&&p!==void 0?!0:!!M,[v,p,m,M]),A=s&&b===void 0?!0:b,X=R(w,`${w}-${F}`,{[`${w}-with-description`]:!!o,[`${w}-no-icon`]:!A,[`${w}-banner`]:!!s,[`${w}-rtl`]:k==="rtl"},D,d,c,_,I),B=ye(h,{aria:!0,data:!0}),Q=a.useMemo(()=>typeof m=="object"&&m.closeIcon?m.closeIcon:v||(p!==void 0?p:typeof M=="object"&&M.closeIcon?M.closeIcon:q),[p,m,v,q]),U=a.useMemo(()=>{const z=m??M;if(typeof z=="object"){const{closeIcon:W}=z;return we(z,["closeIcon"])}return{}},[m,M]);return H(a.createElement(ve,{visible:!E,motionName:`${w}-motion`,motionAppear:!1,motionEnter:!1,onLeaveStart:z=>({maxHeight:z.offsetHeight}),onLeaveEnd:g},({className:z,style:W},Y)=>a.createElement("div",Object.assign({id:C,ref:bt(x,Y),"data-show":!E,className:R(X,z),style:Object.assign(Object.assign(Object.assign({},S),i),W),onMouseEnter:u,onMouseLeave:l,onClick:f,role:"alert"},B),A?a.createElement(tn,{description:o,icon:e.icon,prefixCls:w,type:F}):null,a.createElement("div",{className:`${w}-content`},r?a.createElement("div",{className:`${w}-message`},r):null,o?a.createElement("div",{className:`${w}-description`},o):null),y?a.createElement("div",{className:`${w}-action`},y):null,a.createElement(nn,{isClosable:Z,prefixCls:w,closeIcon:Q,handleClose:L,ariaProps:U}))))});function on(e,t,o){return t=xe(t),yt(e,ht()?Reflect.construct(t,o||[],xe(e).constructor):t.apply(e,o))}let rn=function(e){function t(){var o;return St(this,t),o=on(this,t,arguments),o.state={error:void 0,info:{componentStack:""}},o}return xt(t,e),$t(t,[{key:"componentDidCatch",value:function(n,r){this.setState({error:n,info:r})}},{key:"render",value:function(){const{message:n,description:r,id:s,children:d}=this.props,{error:c,info:i}=this.state,u=(i==null?void 0:i.componentStack)||null,l=typeof n>"u"?(c||"").toString():n,f=typeof r>"u"?u:r;return c?a.createElement(Ue,{id:s,type:"error",message:l,description:a.createElement("pre",{style:{fontSize:"0.9em",overflowX:"auto"}},f)}):d}}])}(a.Component);const an=Ue;an.ErrorBoundary=rn;function ln(){const[e,t]=a.useState([]),o=a.useCallback(n=>(t(r=>[].concat(K(r),[n])),()=>{t(r=>r.filter(s=>s!==n))}),[]);return[e,o]}const sn=new De("antFadeIn",{"0%":{opacity:0},"100%":{opacity:1}}),cn=new De("antFadeOut",{"0%":{opacity:1},"100%":{opacity:0}}),dn=(e,t=!1)=>{const{antCls:o}=e,n=`${o}-fade`,r=t?"&":"";return[Ot(n,sn,cn,e.motionDurationMid,t),{[`
        ${r}${n}-enter,
        ${r}${n}-appear
      `]:{opacity:0,animationTimingFunction:"linear"},[`${r}${n}-leave`]:{animationTimingFunction:"linear"}}]};function me(e){return!!(e!=null&&e.then)}const Ke=e=>{const{type:t,children:o,prefixCls:n,buttonProps:r,close:s,autoFocus:d,emitEvent:c,isSilent:i,quitOnNullishReturnValue:u,actionFn:l}=e,f=a.useRef(!1),g=a.useRef(null),[b,m]=Et(!1),v=(...C)=>{s==null||s.apply(void 0,C)};a.useEffect(()=>{let C=null;return d&&(C=setTimeout(()=>{var h;(h=g.current)===null||h===void 0||h.focus({preventScroll:!0})})),()=>{C&&clearTimeout(C)}},[]);const p=C=>{me(C)&&(m(!0),C.then((...h)=>{m(!1,!0),v.apply(void 0,h),f.current=!1},h=>{if(m(!1,!0),f.current=!1,!(i!=null&&i()))return Promise.reject(h)}))},y=C=>{if(f.current)return;if(f.current=!0,!l){v();return}let h;if(c){if(h=l(C),u&&!me(h)){f.current=!1,v(C);return}}else if(l.length)h=l(s),f.current=!1;else if(h=l(),!me(h)){v();return}p(h)};return a.createElement(Ce,Object.assign({},Fe(t),{onClick:y,loading:b,prefixCls:n},r,{ref:g}),o)},re=$.createContext({}),{Provider:Ze}=re,Pe=()=>{const{autoFocusButton:e,cancelButtonProps:t,cancelTextLocale:o,isSilent:n,mergedOkCancel:r,rootPrefixCls:s,close:d,onCancel:c,onConfirm:i}=a.useContext(re);return r?$.createElement(Ke,{isSilent:n,actionFn:c,close:(...u)=>{d==null||d.apply(void 0,u),i==null||i(!1)},autoFocus:e==="cancel",buttonProps:t,prefixCls:`${s}-btn`},o):null},Ie=()=>{const{autoFocusButton:e,close:t,isSilent:o,okButtonProps:n,rootPrefixCls:r,okTextLocale:s,okType:d,onConfirm:c,onOk:i}=a.useContext(re);return $.createElement(Ke,{isSilent:o,type:d||"primary",actionFn:i,close:(...u)=>{t==null||t.apply(void 0,u),c==null||c(!0)},autoFocus:e==="ok",buttonProps:n,prefixCls:`${r}-btn`},s)};var Qe=a.createContext({});function Ne(e,t,o){var n=t;return!n&&o&&(n="".concat(e,"-").concat(o)),n}function Re(e,t){var o=e["page".concat(t?"Y":"X","Offset")],n="scroll".concat(t?"Top":"Left");if(typeof o!="number"){var r=e.document;o=r.documentElement[n],typeof o!="number"&&(o=r.body[n])}return o}function un(e){var t=e.getBoundingClientRect(),o={left:t.left,top:t.top},n=e.ownerDocument,r=n.defaultView||n.parentWindow;return o.left+=Re(r),o.top+=Re(r,!0),o}const fn=a.memo(function(e){var t=e.children;return t},function(e,t){var o=t.shouldUpdate;return!o});var mn={width:0,height:0,overflow:"hidden",outline:"none"},gn={outline:"none"},Ye=$.forwardRef(function(e,t){var o=e.prefixCls,n=e.className,r=e.style,s=e.title,d=e.ariaId,c=e.footer,i=e.closable,u=e.closeIcon,l=e.onClose,f=e.children,g=e.bodyStyle,b=e.bodyProps,m=e.modalRender,v=e.onMouseDown,p=e.onMouseUp,y=e.holderRef,C=e.visible,h=e.forceRender,E=e.width,T=e.height,x=e.classNames,N=e.styles,k=$.useContext(Qe),M=k.panel,q=wt(y,M),D=a.useRef(),S=a.useRef();$.useImperativeHandle(t,function(){return{focus:function(){var B;(B=D.current)===null||B===void 0||B.focus({preventScroll:!0})},changeActive:function(B){var Q=document,U=Q.activeElement;B&&U===S.current?D.current.focus({preventScroll:!0}):!B&&U===D.current&&S.current.focus({preventScroll:!0})}}});var w={};E!==void 0&&(w.width=E),T!==void 0&&(w.height=T);var H=c?$.createElement("div",{className:R("".concat(o,"-footer"),x==null?void 0:x.footer),style:j({},N==null?void 0:N.footer)},c):null,I=s?$.createElement("div",{className:R("".concat(o,"-header"),x==null?void 0:x.header),style:j({},N==null?void 0:N.header)},$.createElement("div",{className:"".concat(o,"-title"),id:d},s)):null,_=a.useMemo(function(){return $e(i)==="object"&&i!==null?i:i?{closeIcon:u??$.createElement("span",{className:"".concat(o,"-close-x")})}:{}},[i,u,o]),L=ye(_,!0),F=$e(i)==="object"&&i.disabled,Z=i?$.createElement("button",J({type:"button",onClick:l,"aria-label":"Close"},L,{className:"".concat(o,"-close"),disabled:F}),_.closeIcon):null,A=$.createElement("div",{className:R("".concat(o,"-content"),x==null?void 0:x.content),style:N==null?void 0:N.content},Z,I,$.createElement("div",J({className:R("".concat(o,"-body"),x==null?void 0:x.body),style:j(j({},g),N==null?void 0:N.body)},b),f),H);return $.createElement("div",{key:"dialog-element",role:"dialog","aria-labelledby":s?d:null,"aria-modal":"true",ref:q,style:j(j({},r),w),className:R(o,n),onMouseDown:v,onMouseUp:p},$.createElement("div",{ref:D,tabIndex:0,style:gn},$.createElement(fn,{shouldUpdate:C||h},m?m(A):A)),$.createElement("div",{tabIndex:0,ref:S,style:mn}))}),Je=a.forwardRef(function(e,t){var o=e.prefixCls,n=e.title,r=e.style,s=e.className,d=e.visible,c=e.forceRender,i=e.destroyOnClose,u=e.motionName,l=e.ariaId,f=e.onVisibleChanged,g=e.mousePosition,b=a.useRef(),m=a.useState(),v=be(m,2),p=v[0],y=v[1],C={};p&&(C.transformOrigin=p);function h(){var E=un(b.current);y(g&&(g.x||g.y)?"".concat(g.x-E.left,"px ").concat(g.y-E.top,"px"):"")}return a.createElement(ve,{visible:d,onVisibleChanged:f,onAppearPrepare:h,onEnterPrepare:h,forceRender:c,motionName:u,removeOnLeave:i,ref:b},function(E,T){var x=E.className,N=E.style;return a.createElement(Ye,J({},e,{ref:t,title:n,ariaId:l,prefixCls:o,holderRef:T,style:j(j(j({},N),r),C),className:R(s,x)}))})});Je.displayName="Content";var vn=function(t){var o=t.prefixCls,n=t.style,r=t.visible,s=t.maskProps,d=t.motionName,c=t.className;return a.createElement(ve,{key:"mask",visible:r,motionName:d,leavedClassName:"".concat(o,"-mask-hidden")},function(i,u){var l=i.className,f=i.style;return a.createElement("div",J({ref:u,style:j(j({},f),n),className:R("".concat(o,"-mask"),l,c)},s))})},Cn=function(t){var o=t.prefixCls,n=o===void 0?"rc-dialog":o,r=t.zIndex,s=t.visible,d=s===void 0?!1:s,c=t.keyboard,i=c===void 0?!0:c,u=t.focusTriggerAfterClose,l=u===void 0?!0:u,f=t.wrapStyle,g=t.wrapClassName,b=t.wrapProps,m=t.onClose,v=t.afterOpenChange,p=t.afterClose,y=t.transitionName,C=t.animation,h=t.closable,E=h===void 0?!0:h,T=t.mask,x=T===void 0?!0:T,N=t.maskTransitionName,k=t.maskAnimation,M=t.maskClosable,q=M===void 0?!0:M,D=t.maskStyle,S=t.maskProps,w=t.rootClassName,H=t.classNames,I=t.styles,_=a.useRef(),L=a.useRef(),F=a.useRef(),Z=a.useState(d),A=be(Z,2),X=A[0],B=A[1],Q=Pt();function U(){Se(L.current,document.activeElement)||(_.current=document.activeElement)}function z(){if(!Se(L.current,document.activeElement)){var P;(P=F.current)===null||P===void 0||P.focus()}}function W(P){if(P)z();else{if(B(!1),x&&_.current&&l){try{_.current.focus({preventScroll:!0})}catch{}_.current=null}X&&(p==null||p())}v==null||v(P)}function Y(P){m==null||m(P)}var te=a.useRef(!1),oe=a.useRef(),ue=function(){clearTimeout(oe.current),te.current=!0},ne=function(){oe.current=setTimeout(function(){te.current=!1})},le=null;q&&(le=function(fe){te.current?te.current=!1:L.current===fe.target&&Y(fe)});function V(P){if(i&&P.keyCode===Oe.ESC){P.stopPropagation(),Y(P);return}d&&P.keyCode===Oe.TAB&&F.current.changeActive(!P.shiftKey)}a.useEffect(function(){d&&(B(!0),U())},[d]),a.useEffect(function(){return function(){clearTimeout(oe.current)}},[]);var ie=j(j(j({zIndex:r},f),I==null?void 0:I.wrapper),{},{display:X?null:"none"});return a.createElement("div",J({className:R("".concat(n,"-root"),w)},ye(t,{data:!0})),a.createElement(vn,{prefixCls:n,visible:x&&d,motionName:Ne(n,N,k),style:j(j({zIndex:r},D),I==null?void 0:I.mask),maskProps:S,className:H==null?void 0:H.mask}),a.createElement("div",J({tabIndex:-1,onKeyDown:V,className:R("".concat(n,"-wrap"),g,H==null?void 0:H.wrapper),ref:L,onClick:le,style:ie},b),a.createElement(Je,J({},t,{onMouseDown:ue,onMouseUp:ne,ref:F,closable:E,ariaId:Q,prefixCls:n,visible:d&&X,onClose:Y,onVisibleChanged:W,motionName:Ne(n,y,C)}))))},ke=function(t){var o=t.visible,n=t.getContainer,r=t.forceRender,s=t.destroyOnClose,d=s===void 0?!1:s,c=t.afterClose,i=t.panelRef,u=a.useState(o),l=be(u,2),f=l[0],g=l[1],b=a.useMemo(function(){return{panel:i}},[i]);return a.useEffect(function(){o&&g(!0)},[o]),!r&&d&&!f?null:a.createElement(Qe.Provider,{value:b},a.createElement(It,{open:o||r||f,autoDestroy:!1,getContainer:n,autoLock:o||f},a.createElement(Cn,J({},t,{destroyOnClose:d,afterClose:function(){c==null||c(),g(!1)}}))))};ke.displayName="Dialog";const bn=()=>Nt()&&window.document.documentElement;function Te(){}const pn=a.createContext({add:Te,remove:Te});function yn(e){const t=a.useContext(pn),o=a.useRef(null);return Rt(r=>{if(r){const s=e?r.querySelector(e):r;t.add(s),o.current=s}else t.remove(o.current)})}const je=()=>{const{cancelButtonProps:e,cancelTextLocale:t,onCancel:o}=a.useContext(re);return $.createElement(Ce,Object.assign({onClick:o},e),t)},Me=()=>{const{confirmLoading:e,okButtonProps:t,okType:o,okTextLocale:n,onOk:r}=a.useContext(re);return $.createElement(Ce,Object.assign({},Fe(o),{loading:e,onClick:r},t),n)};function et(e,t){return $.createElement("span",{className:`${e}-close-x`},t||$.createElement(he,{className:`${e}-close-icon`}))}const tt=e=>{const{okText:t,okType:o="primary",cancelText:n,confirmLoading:r,onOk:s,onCancel:d,okButtonProps:c,cancelButtonProps:i,footer:u}=e,[l]=pe("Modal",Ae()),f=t||(l==null?void 0:l.okText),g=n||(l==null?void 0:l.cancelText),b={confirmLoading:r,okButtonProps:c,cancelButtonProps:i,okTextLocale:f,cancelTextLocale:g,okType:o,onOk:s,onCancel:d},m=$.useMemo(()=>b,K(Object.values(b)));let v;return typeof u=="function"||typeof u>"u"?(v=$.createElement($.Fragment,null,$.createElement(je,null),$.createElement(Me,null)),typeof u=="function"&&(v=u(v,{OkBtn:Me,CancelBtn:je})),v=$.createElement(Ze,{value:m},v)):v=u,$.createElement(Tt,{disabled:!1},v)};function Be(e){return{position:e,inset:0}}const hn=e=>{const{componentCls:t,antCls:o}=e;return[{[`${t}-root`]:{[`${t}${o}-zoom-enter, ${t}${o}-zoom-appear`]:{transform:"none",opacity:0,animationDuration:e.motionDurationSlow,userSelect:"none"},[`${t}${o}-zoom-leave ${t}-content`]:{pointerEvents:"none"},[`${t}-mask`]:Object.assign(Object.assign({},Be("fixed")),{zIndex:e.zIndexPopupBase,height:"100%",backgroundColor:e.colorBgMask,pointerEvents:"none",[`${t}-hidden`]:{display:"none"}}),[`${t}-wrap`]:Object.assign(Object.assign({},Be("fixed")),{zIndex:e.zIndexPopupBase,overflow:"auto",outline:0,WebkitOverflowScrolling:"touch"})}},{[`${t}-root`]:dn(e)}]},xn=e=>{const{componentCls:t}=e;return[{[`${t}-root`]:{[`${t}-wrap-rtl`]:{direction:"rtl"},[`${t}-centered`]:{textAlign:"center","&::before":{display:"inline-block",width:0,height:"100%",verticalAlign:"middle",content:'""'},[t]:{top:0,display:"inline-block",paddingBottom:0,textAlign:"start",verticalAlign:"middle"}},[`@media (max-width: ${e.screenSMMax}px)`]:{[t]:{maxWidth:"calc(100vw - 16px)",margin:`${O(e.marginXS)} auto`},[`${t}-centered`]:{[t]:{flex:1}}}}},{[t]:Object.assign(Object.assign({},_e(e)),{pointerEvents:"none",position:"relative",top:100,width:"auto",maxWidth:`calc(100vw - ${O(e.calc(e.margin).mul(2).equal())})`,margin:"0 auto",paddingBottom:e.paddingLG,[`${t}-title`]:{margin:0,color:e.titleColor,fontWeight:e.fontWeightStrong,fontSize:e.titleFontSize,lineHeight:e.titleLineHeight,wordWrap:"break-word"},[`${t}-content`]:{position:"relative",backgroundColor:e.contentBg,backgroundClip:"padding-box",border:0,borderRadius:e.borderRadiusLG,boxShadow:e.boxShadow,pointerEvents:"auto",padding:e.contentPadding},[`${t}-close`]:Object.assign({position:"absolute",top:e.calc(e.modalHeaderHeight).sub(e.modalCloseBtnSize).div(2).equal(),insetInlineEnd:e.calc(e.modalHeaderHeight).sub(e.modalCloseBtnSize).div(2).equal(),zIndex:e.calc(e.zIndexPopupBase).add(10).equal(),padding:0,color:e.modalCloseIconColor,fontWeight:e.fontWeightStrong,lineHeight:1,textDecoration:"none",background:"transparent",borderRadius:e.borderRadiusSM,width:e.modalCloseBtnSize,height:e.modalCloseBtnSize,border:0,outline:0,cursor:"pointer",transition:`color ${e.motionDurationMid}, background-color ${e.motionDurationMid}`,"&-x":{display:"flex",fontSize:e.fontSizeLG,fontStyle:"normal",lineHeight:O(e.modalCloseBtnSize),justifyContent:"center",textTransform:"none",textRendering:"auto"},"&:disabled":{pointerEvents:"none"},"&:hover":{color:e.modalCloseIconHoverColor,backgroundColor:e.colorBgTextHover,textDecoration:"none"},"&:active":{backgroundColor:e.colorBgTextActive}},Bt(e)),[`${t}-header`]:{color:e.colorText,background:e.headerBg,borderRadius:`${O(e.borderRadiusLG)} ${O(e.borderRadiusLG)} 0 0`,marginBottom:e.headerMarginBottom,padding:e.headerPadding,borderBottom:e.headerBorderBottom},[`${t}-body`]:{fontSize:e.fontSize,lineHeight:e.lineHeight,wordWrap:"break-word",padding:e.bodyPadding,[`${t}-body-skeleton`]:{width:"100%",height:"100%",display:"flex",justifyContent:"center",alignItems:"center",margin:`${O(e.margin)} auto`}},[`${t}-footer`]:{textAlign:"end",background:e.footerBg,marginTop:e.footerMarginTop,padding:e.footerPadding,borderTop:e.footerBorderTop,borderRadius:e.footerBorderRadius,[`> ${e.antCls}-btn + ${e.antCls}-btn`]:{marginInlineStart:e.marginXS}},[`${t}-open`]:{overflow:"hidden"}})},{[`${t}-pure-panel`]:{top:"auto",padding:0,display:"flex",flexDirection:"column",[`${t}-content,
          ${t}-body,
          ${t}-confirm-body-wrapper`]:{display:"flex",flexDirection:"column",flex:"auto"},[`${t}-confirm-body`]:{marginBottom:"auto"}}}]},$n=e=>{const{componentCls:t}=e;return{[`${t}-root`]:{[`${t}-wrap-rtl`]:{direction:"rtl",[`${t}-confirm-body`]:{direction:"rtl"}}}}},Sn=e=>{const{componentCls:t}=e,o=Xt(e);delete o.xs;const n=Object.keys(o).map(r=>({[`@media (min-width: ${O(o[r])})`]:{width:`var(--${t.replace(".","")}-${r}-width)`}}));return{[`${t}-root`]:{[t]:[{width:`var(--${t.replace(".","")}-xs-width)`}].concat(K(n))}}},nt=e=>{const t=e.padding,o=e.fontSizeHeading5,n=e.lineHeightHeading5;return Mt(e,{modalHeaderHeight:e.calc(e.calc(n).mul(o).equal()).add(e.calc(t).mul(2).equal()).equal(),modalFooterBorderColorSplit:e.colorSplit,modalFooterBorderStyle:e.lineType,modalFooterBorderWidth:e.lineWidth,modalCloseIconColor:e.colorIcon,modalCloseIconHoverColor:e.colorIconHover,modalCloseBtnSize:e.controlHeight,modalConfirmIconSize:e.fontHeight,modalTitleHeight:e.calc(e.titleFontSize).mul(e.titleLineHeight).equal()})},ot=e=>({footerBg:"transparent",headerBg:e.colorBgElevated,titleLineHeight:e.lineHeightHeading5,titleFontSize:e.fontSizeHeading5,contentBg:e.colorBgElevated,titleColor:e.colorTextHeading,contentPadding:e.wireframe?0:`${O(e.paddingMD)} ${O(e.paddingContentHorizontalLG)}`,headerPadding:e.wireframe?`${O(e.padding)} ${O(e.paddingLG)}`:0,headerBorderBottom:e.wireframe?`${O(e.lineWidth)} ${e.lineType} ${e.colorSplit}`:"none",headerMarginBottom:e.wireframe?0:e.marginXS,bodyPadding:e.wireframe?e.paddingLG:0,footerPadding:e.wireframe?`${O(e.paddingXS)} ${O(e.padding)}`:0,footerBorderTop:e.wireframe?`${O(e.lineWidth)} ${e.lineType} ${e.colorSplit}`:"none",footerBorderRadius:e.wireframe?`0 0 ${O(e.borderRadiusLG)} ${O(e.borderRadiusLG)}`:0,footerMarginTop:e.wireframe?0:e.marginSM,confirmBodyPadding:e.wireframe?`${O(e.padding*2)} ${O(e.padding*2)} ${O(e.paddingLG)}`:0,confirmIconMarginInlineEnd:e.wireframe?e.margin:e.marginSM,confirmBtnsMarginTop:e.wireframe?e.marginLG:e.marginSM}),rt=He("Modal",e=>{const t=nt(e);return[xn(t),$n(t),hn(t),jt(t,"zoom"),Sn(t)]},ot,{unitless:{titleLineHeight:!0}});var On=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o};let ge;const En=e=>{ge={x:e.pageX,y:e.pageY},setTimeout(()=>{ge=null},100)};bn()&&document.documentElement.addEventListener("click",En,!0);const at=e=>{const{prefixCls:t,className:o,rootClassName:n,open:r,wrapClassName:s,centered:d,getContainer:c,focusTriggerAfterClose:i=!0,style:u,visible:l,width:f=520,footer:g,classNames:b,styles:m,children:v,loading:p,confirmLoading:y,zIndex:C,mousePosition:h,onOk:E,onCancel:T,destroyOnHidden:x,destroyOnClose:N}=e,k=On(e,["prefixCls","className","rootClassName","open","wrapClassName","centered","getContainer","focusTriggerAfterClose","style","visible","width","footer","classNames","styles","children","loading","confirmLoading","zIndex","mousePosition","onOk","onCancel","destroyOnHidden","destroyOnClose"]),{getPopupContainer:M,getPrefixCls:q,direction:D,modal:S}=a.useContext(de),w=V=>{y||T==null||T(V)},H=V=>{E==null||E(V)},I=q("modal",t),_=q(),L=We(I),[F,Z,A]=rt(I,L),X=R(s,{[`${I}-centered`]:d??(S==null?void 0:S.centered),[`${I}-wrap-rtl`]:D==="rtl"}),B=g!==null&&!p?a.createElement(tt,Object.assign({},e,{onOk:H,onCancel:w})):null,[Q,U,z,W]=Kt(Ee(e),Ee(S),{closable:!0,closeIcon:a.createElement(he,{className:`${I}-close-icon`}),closeIconRender:V=>et(I,V)}),Y=yn(`.${I}-content`),[te,oe]=zt("Modal",C),[ue,ne]=a.useMemo(()=>f&&typeof f=="object"?[void 0,f]:[f,void 0],[f]),le=a.useMemo(()=>{const V={};return ne&&Object.keys(ne).forEach(ie=>{const P=ne[ie];P!==void 0&&(V[`--${I}-${ie}-width`]=typeof P=="number"?`${P}px`:P)}),V},[ne]);return F(a.createElement(Ht,{form:!0,space:!0},a.createElement(_t.Provider,{value:oe},a.createElement(ke,Object.assign({width:ue},k,{zIndex:te,getContainer:c===void 0?M:c,prefixCls:I,rootClassName:R(Z,n,A,L),footer:B,visible:r??l,mousePosition:h??ge,onClose:w,closable:Q&&Object.assign({disabled:z,closeIcon:U},W),closeIcon:U,focusTriggerAfterClose:i,transitionName:ce(_,"zoom",e.transitionName),maskTransitionName:ce(_,"fade",e.maskTransitionName),className:R(Z,o,S==null?void 0:S.className),style:Object.assign(Object.assign(Object.assign({},S==null?void 0:S.style),u),le),classNames:Object.assign(Object.assign(Object.assign({},S==null?void 0:S.classNames),b),{wrapper:R(X,b==null?void 0:b.wrapper)}),styles:Object.assign(Object.assign({},S==null?void 0:S.styles),m),panelRef:Y,destroyOnClose:x??N}),p?a.createElement(Ut,{active:!0,title:!1,paragraph:{rows:4},className:`${I}-body-skeleton`}):v))))},wn=e=>{const{componentCls:t,titleFontSize:o,titleLineHeight:n,modalConfirmIconSize:r,fontSize:s,lineHeight:d,modalTitleHeight:c,fontHeight:i,confirmBodyPadding:u}=e,l=`${t}-confirm`;return{[l]:{"&-rtl":{direction:"rtl"},[`${e.antCls}-modal-header`]:{display:"none"},[`${l}-body-wrapper`]:Object.assign({},Dt()),[`&${t} ${t}-body`]:{padding:u},[`${l}-body`]:{display:"flex",flexWrap:"nowrap",alignItems:"start",[`> ${e.iconCls}`]:{flex:"none",fontSize:r,marginInlineEnd:e.confirmIconMarginInlineEnd,marginTop:e.calc(e.calc(i).sub(r).equal()).div(2).equal()},[`&-has-title > ${e.iconCls}`]:{marginTop:e.calc(e.calc(c).sub(r).equal()).div(2).equal()}},[`${l}-paragraph`]:{display:"flex",flexDirection:"column",flex:"auto",rowGap:e.marginXS,maxWidth:`calc(100% - ${O(e.marginSM)})`},[`${e.iconCls} + ${l}-paragraph`]:{maxWidth:`calc(100% - ${O(e.calc(e.modalConfirmIconSize).add(e.marginSM).equal())})`},[`${l}-title`]:{color:e.colorTextHeading,fontWeight:e.fontWeightStrong,fontSize:o,lineHeight:n},[`${l}-content`]:{color:e.colorText,fontSize:s,lineHeight:d},[`${l}-btns`]:{textAlign:"end",marginTop:e.confirmBtnsMarginTop,[`${e.antCls}-btn + ${e.antCls}-btn`]:{marginBottom:0,marginInlineStart:e.marginXS}}},[`${l}-error ${l}-body > ${e.iconCls}`]:{color:e.colorError},[`${l}-warning ${l}-body > ${e.iconCls},
        ${l}-confirm ${l}-body > ${e.iconCls}`]:{color:e.colorWarning},[`${l}-info ${l}-body > ${e.iconCls}`]:{color:e.colorInfo},[`${l}-success ${l}-body > ${e.iconCls}`]:{color:e.colorSuccess}}},Pn=Lt(["Modal","confirm"],e=>{const t=nt(e);return[wn(t)]},ot,{order:-1e3});var In=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o};function lt(e){const{prefixCls:t,icon:o,okText:n,cancelText:r,confirmPrefixCls:s,type:d,okCancel:c,footer:i,locale:u}=e,l=In(e,["prefixCls","icon","okText","cancelText","confirmPrefixCls","type","okCancel","footer","locale"]);let f=o;if(!o&&o!==null)switch(d){case"info":f=a.createElement(qe,null);break;case"success":f=a.createElement(Xe,null);break;case"error":f=a.createElement(Le,null);break;default:f=a.createElement(Ge,null)}const g=c??d==="confirm",b=e.autoFocusButton===null?!1:e.autoFocusButton||"ok",[m]=pe("Modal"),v=u||m,p=n||(g?v==null?void 0:v.okText:v==null?void 0:v.justOkText),y=r||(v==null?void 0:v.cancelText),C=Object.assign({autoFocusButton:b,cancelTextLocale:y,okTextLocale:p,mergedOkCancel:g},l),h=a.useMemo(()=>C,K(Object.values(C))),E=a.createElement(a.Fragment,null,a.createElement(Pe,null),a.createElement(Ie,null)),T=e.title!==void 0&&e.title!==null,x=`${s}-body`;return a.createElement("div",{className:`${s}-body-wrapper`},a.createElement("div",{className:R(x,{[`${x}-has-title`]:T})},f,a.createElement("div",{className:`${s}-paragraph`},T&&a.createElement("span",{className:`${s}-title`},e.title),a.createElement("div",{className:`${s}-content`},e.content))),i===void 0||typeof i=="function"?a.createElement(Ze,{value:h},a.createElement("div",{className:`${s}-btns`},typeof i=="function"?i(E,{OkBtn:Ie,CancelBtn:Pe}):E)):i,a.createElement(Pn,{prefixCls:t}))}const Nn=e=>{const{close:t,zIndex:o,maskStyle:n,direction:r,prefixCls:s,wrapClassName:d,rootPrefixCls:c,bodyStyle:i,closable:u=!1,onConfirm:l,styles:f}=e,g=`${s}-confirm`,b=e.width||416,m=e.style||{},v=e.mask===void 0?!0:e.mask,p=e.maskClosable===void 0?!1:e.maskClosable,y=R(g,`${g}-${e.type}`,{[`${g}-rtl`]:r==="rtl"},e.className),[,C]=Ft(),h=a.useMemo(()=>o!==void 0?o:C.zIndexPopupBase+At,[o,C]);return a.createElement(at,Object.assign({},e,{className:y,wrapClassName:R({[`${g}-centered`]:!!e.centered},d),onCancel:()=>{t==null||t({triggerCancel:!0}),l==null||l(!1)},title:"",footer:null,transitionName:ce(c||"","zoom",e.transitionName),maskTransitionName:ce(c||"","fade",e.maskTransitionName),mask:v,maskClosable:p,style:m,styles:Object.assign({body:i,mask:n},f),width:b,zIndex:h,closable:u}),a.createElement(lt,Object.assign({},e,{confirmPrefixCls:g})))},it=e=>{const{rootPrefixCls:t,iconPrefixCls:o,direction:n,theme:r}=e;return a.createElement(Ve,{prefixCls:t,iconPrefixCls:o,direction:n,theme:r},a.createElement(Nn,Object.assign({},e)))},ee=[];let st="";function ct(){return st}const Rn=e=>{var t,o;const{prefixCls:n,getContainer:r,direction:s}=e,d=Ae(),c=a.useContext(de),i=ct()||c.getPrefixCls(),u=n||`${i}-modal`;let l=r;return l===!1&&(l=void 0),$.createElement(it,Object.assign({},e,{rootPrefixCls:i,prefixCls:u,iconPrefixCls:c.iconPrefixCls,theme:c.theme,direction:s??c.direction,locale:(o=(t=c.locale)===null||t===void 0?void 0:t.Modal)!==null&&o!==void 0?o:d,getContainer:l}))};function ae(e){const t=Wt(),o=document.createDocumentFragment();let n=Object.assign(Object.assign({},e),{close:i,open:!0}),r,s;function d(...l){var f;if(l.some(m=>m==null?void 0:m.triggerCancel)){var b;(f=e.onCancel)===null||f===void 0||(b=f).call.apply(b,[e,()=>{}].concat(K(l.slice(1))))}for(let m=0;m<ee.length;m++)if(ee[m]===i){ee.splice(m,1);break}s()}function c(l){clearTimeout(r),r=setTimeout(()=>{const f=t.getPrefixCls(void 0,ct()),g=t.getIconPrefixCls(),b=t.getTheme(),m=$.createElement(Rn,Object.assign({},l));s=Vt()($.createElement(Ve,{prefixCls:f,iconPrefixCls:g,theme:b},t.holderRender?t.holderRender(m):m),o)})}function i(...l){n=Object.assign(Object.assign({},n),{open:!1,afterClose:()=>{typeof e.afterClose=="function"&&e.afterClose(),d.apply(this,l)}}),n.visible&&delete n.visible,c(n)}function u(l){typeof l=="function"?n=l(n):n=Object.assign(Object.assign({},n),l),c(n)}return c(n),ee.push(i),{destroy:i,update:u}}function dt(e){return Object.assign(Object.assign({},e),{type:"warning"})}function ut(e){return Object.assign(Object.assign({},e),{type:"info"})}function ft(e){return Object.assign(Object.assign({},e),{type:"success"})}function mt(e){return Object.assign(Object.assign({},e),{type:"error"})}function gt(e){return Object.assign(Object.assign({},e),{type:"confirm"})}function Tn({rootPrefixCls:e}){st=e}var jn=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o};const Mn=(e,t)=>{var o,{afterClose:n,config:r}=e,s=jn(e,["afterClose","config"]);const[d,c]=a.useState(!0),[i,u]=a.useState(r),{direction:l,getPrefixCls:f}=a.useContext(de),g=f("modal"),b=f(),m=()=>{var C;n(),(C=i.afterClose)===null||C===void 0||C.call(i)},v=(...C)=>{var h;if(c(!1),C.some(x=>x==null?void 0:x.triggerCancel)){var T;(h=i.onCancel)===null||h===void 0||(T=h).call.apply(T,[i,()=>{}].concat(K(C.slice(1))))}};a.useImperativeHandle(t,()=>({destroy:v,update:C=>{u(h=>{const E=typeof C=="function"?C(h):C;return Object.assign(Object.assign({},h),E)})}}));const p=(o=i.okCancel)!==null&&o!==void 0?o:i.type==="confirm",[y]=pe("Modal",Gt.Modal);return a.createElement(it,Object.assign({prefixCls:g,rootPrefixCls:b},i,{close:v,open:d,afterClose:m,okText:i.okText||(p?y==null?void 0:y.okText:y==null?void 0:y.justOkText),direction:i.direction||l,cancelText:i.cancelText||(y==null?void 0:y.cancelText)},s))},Bn=a.forwardRef(Mn);let ze=0;const zn=a.memo(a.forwardRef((e,t)=>{const[o,n]=ln();return a.useImperativeHandle(t,()=>({patchElement:n}),[]),a.createElement(a.Fragment,null,o)}));function Hn(){const e=a.useRef(null),[t,o]=a.useState([]);a.useEffect(()=>{t.length&&(K(t).forEach(d=>{d()}),o([]))},[t]);const n=a.useCallback(s=>function(c){var i;ze+=1;const u=a.createRef();let l;const f=new Promise(p=>{l=p});let g=!1,b;const m=a.createElement(Bn,{key:`modal-${ze}`,config:s(c),ref:u,afterClose:()=>{b==null||b()},isSilent:()=>g,onConfirm:p=>{l(p)}});return b=(i=e.current)===null||i===void 0?void 0:i.patchElement(m),b&&ee.push(b),{destroy:()=>{function p(){var y;(y=u.current)===null||y===void 0||y.destroy()}u.current?p():o(y=>[].concat(K(y),[p]))},update:p=>{function y(){var C;(C=u.current)===null||C===void 0||C.update(p)}u.current?y():o(C=>[].concat(K(C),[y]))},then:p=>(g=!0,f.then(p))}},[]);return[a.useMemo(()=>({info:n(ut),success:n(ft),error:n(mt),warning:n(dt),confirm:n(gt)}),[]),a.createElement(zn,{key:"modal-holder",ref:e})]}var _n=function(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(o[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)t.indexOf(n[r])<0&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o};const Ln=e=>{const{prefixCls:t,className:o,closeIcon:n,closable:r,type:s,title:d,children:c,footer:i}=e,u=_n(e,["prefixCls","className","closeIcon","closable","type","title","children","footer"]),{getPrefixCls:l}=a.useContext(de),f=l(),g=t||l("modal"),b=We(f),[m,v,p]=rt(g,b),y=`${g}-confirm`;let C={};return s?C={closable:r??!1,title:"",footer:"",children:a.createElement(lt,Object.assign({},e,{prefixCls:g,confirmPrefixCls:y,rootPrefixCls:f,content:c}))}:C={closable:r??!0,title:d,footer:i!==null&&a.createElement(tt,Object.assign({},e)),children:c},m(a.createElement(Ye,Object.assign({prefixCls:g,className:R(v,`${g}-pure-panel`,s&&y,s&&`${y}-${s}`,o,p,b)},u,{closeIcon:et(g,n),closable:r},C)))},Dn=qt(Ln);function vt(e){return ae(dt(e))}const G=at;G.useModal=Hn;G.info=function(t){return ae(ut(t))};G.success=function(t){return ae(ft(t))};G.error=function(t){return ae(mt(t))};G.warning=vt;G.warn=vt;G.confirm=function(t){return ae(gt(t))};G.destroyAll=function(){for(;ee.length;){const t=ee.pop();t&&t()}};G.config=Tn;G._InternalPanelDoNotUseOrYouWillBeFired=Dn;export{an as A,G as M,Ke as a};
