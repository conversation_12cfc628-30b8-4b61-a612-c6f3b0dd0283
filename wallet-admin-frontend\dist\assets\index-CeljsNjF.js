import{r as l,g as ke,aw as Je,ax as _e,m as Ze,f as Ve,e as ee,ay as pe,a0 as he,az as xe,k as U,aA as et,c as ne,aB as tt,aC as nt,aD as rt,a3 as ot,h as lt,i as at,aE as it,aF as st,aG as ct,aH as ut,aI as Le,aJ as re,aK as dt,aL as mt,a2 as ue,w as we,aM as ft,J as gt,aN as pt,aO as Se,aP as ht,y as Te,I as bt,_ as yt,a9 as vt,ak as Ct,aa as $t,ar as xt,am as wt,aQ as St,ag as It,aR as We,C as He,aS as Ot,aT as Et,aU as Ft,aV as Mt,aW as jt,aX as Nt,d as Pt,aY as Rt,aZ as _t}from"./index-CW-Whzws.js";import{C as ze,R as Vt}from"./row-fflaQe-U.js";import{R as Lt,b as Tt}from"./index-BVbup1Oj.js";const Ie=e=>typeof e=="object"&&e!=null&&e.nodeType===1,Oe=(e,t)=>(!t||e!=="hidden")&&e!=="visible"&&e!=="clip",se=(e,t)=>{if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){const n=getComputedStyle(e,null);return Oe(n.overflowY,t)||Oe(n.overflowX,t)||(r=>{const o=(a=>{if(!a.ownerDocument||!a.ownerDocument.defaultView)return null;try{return a.ownerDocument.defaultView.frameElement}catch{return null}})(r);return!!o&&(o.clientHeight<r.scrollHeight||o.clientWidth<r.scrollWidth)})(e)}return!1},ce=(e,t,n,r,o,a,s,i)=>a<e&&s>t||a>e&&s<t?0:a<=e&&i<=n||s>=t&&i>=n?a-e-r:s>t&&i<n||a<e&&i>n?s-t+o:0,Wt=e=>{const t=e.parentElement;return t??(e.getRootNode().host||null)},Ee=(e,t)=>{var n,r,o,a;if(typeof document>"u")return[];const{scrollMode:s,block:i,inline:m,boundary:f,skipOverflowHiddenElements:g}=t,M=typeof f=="function"?f:T=>T!==f;if(!Ie(e))throw new TypeError("Invalid target");const N=document.scrollingElement||document.documentElement,L=[];let h=e;for(;Ie(h)&&M(h);){if(h=Wt(h),h===N){L.push(h);break}h!=null&&h===document.body&&se(h)&&!se(document.documentElement)||h!=null&&se(h,g)&&L.push(h)}const y=(r=(n=window.visualViewport)==null?void 0:n.width)!=null?r:innerWidth,p=(a=(o=window.visualViewport)==null?void 0:o.height)!=null?a:innerHeight,{scrollX:x,scrollY:v}=window,{height:c,width:u,top:O,right:z,bottom:E,left:w}=e.getBoundingClientRect(),{top:S,right:P,bottom:R,left:X}=(T=>{const d=window.getComputedStyle(T);return{top:parseFloat(d.scrollMarginTop)||0,right:parseFloat(d.scrollMarginRight)||0,bottom:parseFloat(d.scrollMarginBottom)||0,left:parseFloat(d.scrollMarginLeft)||0}})(e);let j=i==="start"||i==="nearest"?O-S:i==="end"?E+R:O+c/2-S+R,b=m==="center"?w+u/2-X+P:m==="end"?z+P:w-X;const D=[];for(let T=0;T<L.length;T++){const d=L[T],{height:W,width:I,top:B,right:Y,bottom:Q,left:J}=d.getBoundingClientRect();if(s==="if-needed"&&O>=0&&w>=0&&E<=p&&z<=y&&(d===N&&!se(d)||O>=B&&E<=Q&&w>=J&&z<=Y))return D;const oe=getComputedStyle(d),q=parseInt(oe.borderLeftWidth,10),G=parseInt(oe.borderTopWidth,10),$=parseInt(oe.borderRightWidth,10),_=parseInt(oe.borderBottomWidth,10);let C=0,H=0;const F="offsetWidth"in d?d.offsetWidth-d.clientWidth-q-$:0,A="offsetHeight"in d?d.offsetHeight-d.clientHeight-G-_:0,K="offsetWidth"in d?d.offsetWidth===0?0:I/d.offsetWidth:0,te="offsetHeight"in d?d.offsetHeight===0?0:W/d.offsetHeight:0;if(N===d)C=i==="start"?j:i==="end"?j-p:i==="nearest"?ce(v,v+p,p,G,_,v+j,v+j+c,c):j-p/2,H=m==="start"?b:m==="center"?b-y/2:m==="end"?b-y:ce(x,x+y,y,q,$,x+b,x+b+u,u),C=Math.max(0,C+v),H=Math.max(0,H+x);else{C=i==="start"?j-B-G:i==="end"?j-Q+_+A:i==="nearest"?ce(B,Q,W,G,_+A,j,j+c,c):j-(B+W/2)+A/2,H=m==="start"?b-J-q:m==="center"?b-(J+I/2)+F/2:m==="end"?b-Y+$+F:ce(J,Y,I,q,$+F,b,b+u,u);const{scrollLeft:V,scrollTop:Z}=d;C=te===0?0:Math.max(0,Math.min(Z+C/te,d.scrollHeight-W/te+A)),H=K===0?0:Math.max(0,Math.min(V+H/K,d.scrollWidth-I/K+F)),j+=Z-C,b+=V-H}D.push({el:d,top:C,left:H})}return D},Ht=e=>e===!1?{block:"end",inline:"nearest"}:(t=>t===Object(t)&&Object.keys(t).length!==0)(e)?e:{block:"start",inline:"nearest"};function zt(e,t){if(!e.isConnected||!(o=>{let a=o;for(;a&&a.parentNode;){if(a.parentNode===document)return!0;a=a.parentNode instanceof ShadowRoot?a.parentNode.host:a.parentNode}return!1})(e))return;const n=(o=>{const a=window.getComputedStyle(o);return{top:parseFloat(a.scrollMarginTop)||0,right:parseFloat(a.scrollMarginRight)||0,bottom:parseFloat(a.scrollMarginBottom)||0,left:parseFloat(a.scrollMarginLeft)||0}})(e);if((o=>typeof o=="object"&&typeof o.behavior=="function")(t))return t.behavior(Ee(e,t));const r=typeof t=="boolean"||t==null?void 0:t.behavior;for(const{el:o,top:a,left:s}of Ee(e,Ht(t))){const i=a-n.top+n.bottom,m=s-n.left+n.right;o.scroll({top:i,left:m,behavior:r})}}function Dt(e){return e==null?null:typeof e=="object"&&!l.isValidElement(e)?e:{title:e}}function de(e){const[t,n]=l.useState(e);return l.useEffect(()=>{const r=setTimeout(()=>{n(e)},e.length?0:10);return()=>{clearTimeout(r)}},[e]),t}const At=e=>{const{componentCls:t}=e,n=`${t}-show-help`,r=`${t}-show-help-item`;return{[n]:{transition:`opacity ${e.motionDurationFast} ${e.motionEaseInOut}`,"&-appear, &-enter":{opacity:0,"&-active":{opacity:1}},"&-leave":{opacity:1,"&-active":{opacity:0}},[r]:{overflow:"hidden",transition:`height ${e.motionDurationFast} ${e.motionEaseInOut},
                     opacity ${e.motionDurationFast} ${e.motionEaseInOut},
                     transform ${e.motionDurationFast} ${e.motionEaseInOut} !important`,[`&${r}-appear, &${r}-enter`]:{transform:"translateY(-5px)",opacity:0,"&-active":{transform:"translateY(0)",opacity:1}},[`&${r}-leave-active`]:{transform:"translateY(-5px)"}}}}},Bt=e=>({legend:{display:"block",width:"100%",marginBottom:e.marginLG,padding:0,color:e.colorTextDescription,fontSize:e.fontSizeLG,lineHeight:"inherit",border:0,borderBottom:`${ee(e.lineWidth)} ${e.lineType} ${e.colorBorder}`},'input[type="search"]':{boxSizing:"border-box"},'input[type="radio"], input[type="checkbox"]':{lineHeight:"normal"},'input[type="file"]':{display:"block"},'input[type="range"]':{display:"block",width:"100%"},"select[multiple], select[size]":{height:"auto"},"input[type='file']:focus,\n  input[type='radio']:focus,\n  input[type='checkbox']:focus":{outline:0,boxShadow:`0 0 0 ${ee(e.controlOutlineWidth)} ${e.controlOutline}`},output:{display:"block",paddingTop:15,color:e.colorText,fontSize:e.fontSize,lineHeight:e.lineHeight}}),Fe=(e,t)=>{const{formItemCls:n}=e;return{[n]:{[`${n}-label > label`]:{height:t},[`${n}-control-input`]:{minHeight:t}}}},qt=e=>{const{componentCls:t}=e;return{[e.componentCls]:Object.assign(Object.assign(Object.assign({},Ve(e)),Bt(e)),{[`${t}-text`]:{display:"inline-block",paddingInlineEnd:e.paddingSM},"&-small":Object.assign({},Fe(e,e.controlHeightSM)),"&-large":Object.assign({},Fe(e,e.controlHeightLG))})}},Xt=e=>{const{formItemCls:t,iconCls:n,rootPrefixCls:r,antCls:o,labelRequiredMarkColor:a,labelColor:s,labelFontSize:i,labelHeight:m,labelColonMarginInlineStart:f,labelColonMarginInlineEnd:g,itemMarginBottom:M}=e;return{[t]:Object.assign(Object.assign({},Ve(e)),{marginBottom:M,verticalAlign:"top","&-with-help":{transition:"none"},[`&-hidden,
        &-hidden${o}-row`]:{display:"none"},"&-has-warning":{[`${t}-split`]:{color:e.colorError}},"&-has-error":{[`${t}-split`]:{color:e.colorWarning}},[`${t}-label`]:{flexGrow:0,overflow:"hidden",whiteSpace:"nowrap",textAlign:"end",verticalAlign:"middle","&-left":{textAlign:"start"},"&-wrap":{overflow:"unset",lineHeight:e.lineHeight,whiteSpace:"unset","> label":{verticalAlign:"middle",textWrap:"balance"}},"> label":{position:"relative",display:"inline-flex",alignItems:"center",maxWidth:"100%",height:m,color:s,fontSize:i,[`> ${n}`]:{fontSize:e.fontSize,verticalAlign:"top"},[`&${t}-required`]:{"&::before":{display:"inline-block",marginInlineEnd:e.marginXXS,color:a,fontSize:e.fontSize,fontFamily:"SimSun, sans-serif",lineHeight:1,content:'"*"'},[`&${t}-required-mark-hidden, &${t}-required-mark-optional`]:{"&::before":{display:"none"}}},[`${t}-optional`]:{display:"inline-block",marginInlineStart:e.marginXXS,color:e.colorTextDescription,[`&${t}-required-mark-hidden`]:{display:"none"}},[`${t}-tooltip`]:{color:e.colorTextDescription,cursor:"help",writingMode:"horizontal-tb",marginInlineStart:e.marginXXS},"&::after":{content:'":"',position:"relative",marginBlock:0,marginInlineStart:f,marginInlineEnd:g},[`&${t}-no-colon::after`]:{content:'"\\a0"'}}},[`${t}-control`]:{"--ant-display":"flex",flexDirection:"column",flexGrow:1,[`&:first-child:not([class^="'${r}-col-'"]):not([class*="' ${r}-col-'"])`]:{width:"100%"},"&-input":{position:"relative",display:"flex",alignItems:"center",minHeight:e.controlHeight,"&-content":{flex:"auto",maxWidth:"100%"}}},[t]:{"&-additional":{display:"flex",flexDirection:"column"},"&-explain, &-extra":{clear:"both",color:e.colorTextDescription,fontSize:e.fontSize,lineHeight:e.lineHeight},"&-explain-connected":{width:"100%"},"&-extra":{minHeight:e.controlHeightSM,transition:`color ${e.motionDurationMid} ${e.motionEaseOut}`},"&-explain":{"&-error":{color:e.colorError},"&-warning":{color:e.colorWarning}}},[`&-with-help ${t}-explain`]:{height:"auto",opacity:1},[`${t}-feedback-icon`]:{fontSize:e.fontSize,textAlign:"center",visibility:"visible",animationName:_e,animationDuration:e.motionDurationMid,animationTimingFunction:e.motionEaseOutBack,pointerEvents:"none","&-success":{color:e.colorSuccess},"&-error":{color:e.colorError},"&-warning":{color:e.colorWarning},"&-validating":{color:e.colorPrimary}}})}},Me=(e,t)=>{const{formItemCls:n}=e;return{[`${t}-horizontal`]:{[`${n}-label`]:{flexGrow:0},[`${n}-control`]:{flex:"1 1 0",minWidth:0},[`${n}-label[class$='-24'], ${n}-label[class*='-24 ']`]:{[`& + ${n}-control`]:{minWidth:"unset"}}}}},Gt=e=>{const{componentCls:t,formItemCls:n,inlineItemMarginBottom:r}=e;return{[`${t}-inline`]:{display:"flex",flexWrap:"wrap",[n]:{flex:"none",marginInlineEnd:e.margin,marginBottom:r,"&-row":{flexWrap:"nowrap"},[`> ${n}-label,
        > ${n}-control`]:{display:"inline-block",verticalAlign:"top"},[`> ${n}-label`]:{flex:"none"},[`${t}-text`]:{display:"inline-block"},[`${n}-has-feedback`]:{display:"inline-block"}}}}},k=e=>({padding:e.verticalLabelPadding,margin:e.verticalLabelMargin,whiteSpace:"initial",textAlign:"start","> label":{margin:0,"&::after":{visibility:"hidden"}}}),De=e=>{const{componentCls:t,formItemCls:n,rootPrefixCls:r}=e;return{[`${n} ${n}-label`]:k(e),[`${t}:not(${t}-inline)`]:{[n]:{flexWrap:"wrap",[`${n}-label, ${n}-control`]:{[`&:not([class*=" ${r}-col-xs"])`]:{flex:"0 0 100%",maxWidth:"100%"}}}}}},Kt=e=>{const{componentCls:t,formItemCls:n,antCls:r}=e;return{[`${t}-vertical`]:{[`${n}:not(${n}-horizontal)`]:{[`${n}-row`]:{flexDirection:"column"},[`${n}-label > label`]:{height:"auto"},[`${n}-control`]:{width:"100%"},[`${n}-label,
        ${r}-col-24${n}-label,
        ${r}-col-xl-24${n}-label`]:k(e)}},[`@media (max-width: ${ee(e.screenXSMax)})`]:[De(e),{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-xs-24${n}-label`]:k(e)}}}],[`@media (max-width: ${ee(e.screenSMMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-sm-24${n}-label`]:k(e)}}},[`@media (max-width: ${ee(e.screenMDMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-md-24${n}-label`]:k(e)}}},[`@media (max-width: ${ee(e.screenLGMax)})`]:{[t]:{[`${n}:not(${n}-horizontal)`]:{[`${r}-col-lg-24${n}-label`]:k(e)}}}}},Yt=e=>{const{formItemCls:t,antCls:n}=e;return{[`${t}-vertical`]:{[`${t}-row`]:{flexDirection:"column"},[`${t}-label > label`]:{height:"auto"},[`${t}-control`]:{width:"100%"}},[`${t}-vertical ${t}-label,
      ${n}-col-24${t}-label,
      ${n}-col-xl-24${t}-label`]:k(e),[`@media (max-width: ${ee(e.screenXSMax)})`]:[De(e),{[t]:{[`${n}-col-xs-24${t}-label`]:k(e)}}],[`@media (max-width: ${ee(e.screenSMMax)})`]:{[t]:{[`${n}-col-sm-24${t}-label`]:k(e)}},[`@media (max-width: ${ee(e.screenMDMax)})`]:{[t]:{[`${n}-col-md-24${t}-label`]:k(e)}},[`@media (max-width: ${ee(e.screenLGMax)})`]:{[t]:{[`${n}-col-lg-24${t}-label`]:k(e)}}}},Qt=e=>({labelRequiredMarkColor:e.colorError,labelColor:e.colorTextHeading,labelFontSize:e.fontSize,labelHeight:e.controlHeight,labelColonMarginInlineStart:e.marginXXS/2,labelColonMarginInlineEnd:e.marginXS,itemMarginBottom:e.marginLG,verticalLabelPadding:`0 0 ${e.paddingXS}px`,verticalLabelMargin:0,inlineItemMarginBottom:0}),Ae=(e,t)=>Ze(e,{formItemCls:`${e.componentCls}-item`,rootPrefixCls:t}),be=ke("Form",(e,{rootPrefixCls:t})=>{const n=Ae(e,t);return[qt(n),Xt(n),At(n),Me(n,n.componentCls),Me(n,n.formItemCls),Gt(n),Kt(n),Yt(n),Je(n),_e]},Qt,{order:-1e3}),je=[];function ge(e,t,n,r=0){return{key:typeof e=="string"?e:`${t}-${r}`,error:e,errorStatus:n}}const Be=({help:e,helpStatus:t,errors:n=je,warnings:r=je,className:o,fieldId:a,onVisibleChanged:s})=>{const{prefixCls:i}=l.useContext(pe),m=`${i}-item-explain`,f=he(i),[g,M,N]=be(i,f),L=l.useMemo(()=>xe(i),[i]),h=de(n),y=de(r),p=l.useMemo(()=>e!=null?[ge(e,"help",t)]:[].concat(U(h.map((c,u)=>ge(c,"error","error",u))),U(y.map((c,u)=>ge(c,"warning","warning",u)))),[e,t,h,y]),x=l.useMemo(()=>{const c={};return p.forEach(({key:u})=>{c[u]=(c[u]||0)+1}),p.map((u,O)=>Object.assign(Object.assign({},u),{key:c[u.key]>1?`${u.key}-fallback-${O}`:u.key}))},[p]),v={};return a&&(v.id=`${a}_help`),g(l.createElement(et,{motionDeadline:L.motionDeadline,motionName:`${i}-show-help`,visible:!!x.length,onVisibleChanged:s},c=>{const{className:u,style:O}=c;return l.createElement("div",Object.assign({},v,{className:ne(m,u,N,f,o,M),style:O}),l.createElement(tt,Object.assign({keys:x},xe(i),{motionName:`${i}-show-help-item`,component:!1}),z=>{const{key:E,error:w,errorStatus:S,className:P,style:R}=z;return l.createElement("div",{key:E,className:ne(P,{[`${m}-${S}`]:S}),style:R},w)}))}))},Ut=["parentNode"],kt="form_item";function ie(e){return e===void 0||e===!1?[]:Array.isArray(e)?e:[e]}function qe(e,t){if(!e.length)return;const n=e.join("_");return t?`${t}_${n}`:Ut.includes(n)?`${kt}_${n}`:n}function Xe(e,t,n,r,o,a){let s=r;return a!==void 0?s=a:n.validating?s="validating":e.length?s="error":t.length?s="warning":(n.touched||o&&n.validated)&&(s="success"),s}var Jt=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function Ne(e){return ie(e).join("_")}function Pe(e,t){const n=t.getFieldInstance(e),r=rt(n);if(r)return r;const o=qe(ie(e),t.__INTERNAL__.name);if(o)return document.getElementById(o)}function Ge(e){const[t]=nt(),n=l.useRef({}),r=l.useMemo(()=>e??Object.assign(Object.assign({},t),{__INTERNAL__:{itemRef:o=>a=>{const s=Ne(o);a?n.current[s]=a:delete n.current[s]}},scrollToField:(o,a={})=>{const{focus:s}=a,i=Jt(a,["focus"]),m=Pe(o,r);m&&(zt(m,Object.assign({scrollMode:"if-needed",block:"nearest"},i)),s&&r.focusField(o))},focusField:o=>{var a,s;const i=r.getFieldInstance(o);typeof(i==null?void 0:i.focus)=="function"?i.focus():(s=(a=Pe(o,r))===null||a===void 0?void 0:a.focus)===null||s===void 0||s.call(a)},getFieldInstance:o=>{const a=Ne(o);return n.current[a]}}),[e,t]);return[r]}var Zt=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const en=(e,t)=>{const n=l.useContext(ot),{getPrefixCls:r,direction:o,requiredMark:a,colon:s,scrollToFirstError:i,className:m,style:f}=lt("form"),{prefixCls:g,className:M,rootClassName:N,size:L,disabled:h=n,form:y,colon:p,labelAlign:x,labelWrap:v,labelCol:c,wrapperCol:u,hideRequiredMark:O,layout:z="horizontal",scrollToFirstError:E,requiredMark:w,onFinishFailed:S,name:P,style:R,feedbackIcons:X,variant:j}=e,b=Zt(e,["prefixCls","className","rootClassName","size","disabled","form","colon","labelAlign","labelWrap","labelCol","wrapperCol","hideRequiredMark","layout","scrollToFirstError","requiredMark","onFinishFailed","name","style","feedbackIcons","variant"]),D=at(L),T=l.useContext(it),d=l.useMemo(()=>w!==void 0?w:O?!1:a!==void 0?a:!0,[O,w,a]),W=p??s,I=r("form",g),B=he(I),[Y,Q,J]=be(I,B),oe=ne(I,`${I}-${z}`,{[`${I}-hide-required-mark`]:d===!1,[`${I}-rtl`]:o==="rtl",[`${I}-${D}`]:D},J,B,Q,m,M,N),[q]=Ge(y),{__INTERNAL__:G}=q;G.name=P;const $=l.useMemo(()=>({name:P,labelAlign:x,labelCol:c,labelWrap:v,wrapperCol:u,vertical:z==="vertical",colon:W,requiredMark:d,itemRef:G.itemRef,form:q,feedbackIcons:X}),[P,x,c,u,z,W,d,q,X]),_=l.useRef(null);l.useImperativeHandle(t,()=>{var F;return Object.assign(Object.assign({},q),{nativeElement:(F=_.current)===null||F===void 0?void 0:F.nativeElement})});const C=(F,A)=>{if(F){let K={block:"nearest"};typeof F=="object"&&(K=Object.assign(Object.assign({},K),F)),q.scrollToField(A,K)}},H=F=>{if(S==null||S(F),F.errorFields.length){const A=F.errorFields[0].name;if(E!==void 0){C(E,A);return}i!==void 0&&C(i,A)}};return Y(l.createElement(st.Provider,{value:j},l.createElement(ct,{disabled:h},l.createElement(ut.Provider,{value:D},l.createElement(Le,{validateMessages:T},l.createElement(re.Provider,{value:$},l.createElement(dt,Object.assign({id:P},b,{name:P,onFinishFailed:H,form:q,ref:_,style:Object.assign(Object.assign({},f),R),className:oe}))))))))},tn=l.forwardRef(en);function nn(e){if(typeof e=="function")return e;const t=mt(e);return t.length<=1?t[0]:t}const Ke=()=>{const{status:e,errors:t=[],warnings:n=[]}=l.useContext(ue);return{status:e,errors:t,warnings:n}};Ke.Context=ue;function rn(e){const[t,n]=l.useState(e),r=l.useRef(null),o=l.useRef([]),a=l.useRef(!1);l.useEffect(()=>(a.current=!1,()=>{a.current=!0,we.cancel(r.current),r.current=null}),[]);function s(i){a.current||(r.current===null&&(o.current=[],r.current=we(()=>{r.current=null,n(m=>{let f=m;return o.current.forEach(g=>{f=g(f)}),f})})),o.current.push(i))}return[t,s]}function on(){const{itemRef:e}=l.useContext(re),t=l.useRef({});function n(r,o){const a=o&&typeof o=="object"&&ft(o),s=r.join("_");return(t.current.name!==s||t.current.originRef!==a)&&(t.current.name=s,t.current.originRef=a,t.current.ref=gt(e(r),a)),t.current.ref}return n}const ln=e=>{const{formItemCls:t}=e;return{"@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none)":{[`${t}-control`]:{display:"flex"}}}},an=pt(["Form","item-item"],(e,{rootPrefixCls:t})=>{const n=Ae(e,t);return[ln(n)]});var sn=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const cn=24,un=e=>{const{prefixCls:t,status:n,labelCol:r,wrapperCol:o,children:a,errors:s,warnings:i,_internalItemRender:m,extra:f,help:g,fieldId:M,marginBottom:N,onErrorVisibleChanged:L,label:h}=e,y=`${t}-item`,p=l.useContext(re),x=l.useMemo(()=>{let b=Object.assign({},o||p.wrapperCol||{});return h===null&&!r&&!o&&p.labelCol&&[void 0,"xs","sm","md","lg","xl","xxl"].forEach(T=>{const d=T?[T]:[],W=Se(p.labelCol,d),I=typeof W=="object"?W:{},B=Se(b,d),Y=typeof B=="object"?B:{};"span"in I&&!("offset"in Y)&&I.span<cn&&(b=ht(b,[].concat(d,["offset"]),I.span))}),b},[o,p]),v=ne(`${y}-control`,x.className),c=l.useMemo(()=>{const{labelCol:b,wrapperCol:D}=p;return sn(p,["labelCol","wrapperCol"])},[p]),u=l.useRef(null),[O,z]=l.useState(0);Te(()=>{f&&u.current?z(u.current.clientHeight):z(0)},[f]);const E=l.createElement("div",{className:`${y}-control-input`},l.createElement("div",{className:`${y}-control-input-content`},a)),w=l.useMemo(()=>({prefixCls:t,status:n}),[t,n]),S=N!==null||s.length||i.length?l.createElement(pe.Provider,{value:w},l.createElement(Be,{fieldId:M,errors:s,warnings:i,help:g,helpStatus:n,className:`${y}-explain-connected`,onVisibleChanged:L})):null,P={};M&&(P.id=`${M}_extra`);const R=f?l.createElement("div",Object.assign({},P,{className:`${y}-extra`,ref:u}),f):null,X=S||R?l.createElement("div",{className:`${y}-additional`,style:N?{minHeight:N+O}:{}},S,R):null,j=m&&m.mark==="pro_table_render"&&m.render?m.render(e,{input:E,errorList:S,extra:R}):l.createElement(l.Fragment,null,E,X);return l.createElement(re.Provider,{value:c},l.createElement(ze,Object.assign({},x,{className:v}),j),l.createElement(an,{prefixCls:t}))};var dn={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M623.6 316.7C593.6 290.4 554 276 512 276s-81.6 14.5-111.6 40.7C369.2 344 352 380.7 352 420v7.6c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V420c0-44.1 43.1-80 96-80s96 35.9 96 80c0 31.1-22 59.6-56.1 72.7-21.2 8.1-39.2 22.3-52.1 40.9-13.1 19-19.9 41.8-19.9 64.9V620c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-22.7a48.3 48.3 0 0130.9-44.8c59-22.7 97.1-74.7 97.1-132.5.1-39.3-17.1-76-48.3-103.3zM472 732a40 40 0 1080 0 40 40 0 10-80 0z"}}]},name:"question-circle",theme:"outlined"},mn=function(t,n){return l.createElement(bt,yt({},t,{ref:n,icon:dn}))},fn=l.forwardRef(mn),gn=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const pn=({prefixCls:e,label:t,htmlFor:n,labelCol:r,labelAlign:o,colon:a,required:s,requiredMark:i,tooltip:m,vertical:f})=>{var g;const[M]=vt("Form"),{labelAlign:N,labelCol:L,labelWrap:h,colon:y}=l.useContext(re);if(!t)return null;const p=r||L||{},x=o||N,v=`${e}-item-label`,c=ne(v,x==="left"&&`${v}-left`,p.className,{[`${v}-wrap`]:!!h});let u=t;const O=a===!0||y!==!1&&a!==!1;O&&!f&&typeof t=="string"&&t.trim()&&(u=t.replace(/[:|：]\s*$/,""));const E=Dt(m);if(E){const{icon:j=l.createElement(fn,null)}=E,b=gn(E,["icon"]),D=l.createElement(Ct,Object.assign({},b),l.cloneElement(j,{className:`${e}-item-tooltip`,title:"",onClick:T=>{T.preventDefault()},tabIndex:null}));u=l.createElement(l.Fragment,null,u,D)}const w=i==="optional",S=typeof i=="function",P=i===!1;S?u=i(u,{required:!!s}):w&&!s&&(u=l.createElement(l.Fragment,null,u,l.createElement("span",{className:`${e}-item-optional`,title:""},(M==null?void 0:M.optional)||((g=$t.Form)===null||g===void 0?void 0:g.optional))));let R;P?R="hidden":(w||S)&&(R="optional");const X=ne({[`${e}-item-required`]:s,[`${e}-item-required-mark-${R}`]:R,[`${e}-item-no-colon`]:!O});return l.createElement(ze,Object.assign({},p,{className:c}),l.createElement("label",{htmlFor:n,className:X,title:typeof t=="string"?t:""},u))},hn={success:Tt,warning:Lt,error:wt,validating:xt};function Ye({children:e,errors:t,warnings:n,hasFeedback:r,validateStatus:o,prefixCls:a,meta:s,noStyle:i}){const m=`${a}-item`,{feedbackIcons:f}=l.useContext(re),g=Xe(t,n,s,null,!!r,o),{isFormItemInput:M,status:N,hasFeedback:L,feedbackIcon:h}=l.useContext(ue),y=l.useMemo(()=>{var p;let x;if(r){const c=r!==!0&&r.icons||f,u=g&&((p=c==null?void 0:c({status:g,errors:t,warnings:n}))===null||p===void 0?void 0:p[g]),O=g&&hn[g];x=u!==!1&&O?l.createElement("span",{className:ne(`${m}-feedback-icon`,`${m}-feedback-icon-${g}`)},u||l.createElement(O,null)):null}const v={status:g||"",errors:t,warnings:n,hasFeedback:!!r,feedbackIcon:x,isFormItemInput:!0};return i&&(v.status=(g??N)||"",v.isFormItemInput=M,v.hasFeedback=!!(r??L),v.feedbackIcon=r!==void 0?v.feedbackIcon:h),v},[g,r,i,M,N]);return l.createElement(ue.Provider,{value:y},e)}var bn=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};function yn(e){const{prefixCls:t,className:n,rootClassName:r,style:o,help:a,errors:s,warnings:i,validateStatus:m,meta:f,hasFeedback:g,hidden:M,children:N,fieldId:L,required:h,isRequired:y,onSubItemMetaChange:p,layout:x}=e,v=bn(e,["prefixCls","className","rootClassName","style","help","errors","warnings","validateStatus","meta","hasFeedback","hidden","children","fieldId","required","isRequired","onSubItemMetaChange","layout"]),c=`${t}-item`,{requiredMark:u,vertical:O}=l.useContext(re),z=O||x==="vertical",E=l.useRef(null),w=de(s),S=de(i),P=a!=null,R=!!(P||s.length||i.length),X=!!E.current&&St(E.current),[j,b]=l.useState(null);Te(()=>{if(R&&E.current){const I=getComputedStyle(E.current);b(parseInt(I.marginBottom,10))}},[R,X]);const D=I=>{I||b(null)},d=((I=!1)=>{const B=I?w:f.errors,Y=I?S:f.warnings;return Xe(B,Y,f,"",!!g,m)})(),W=ne(c,n,r,{[`${c}-with-help`]:P||w.length||S.length,[`${c}-has-feedback`]:d&&g,[`${c}-has-success`]:d==="success",[`${c}-has-warning`]:d==="warning",[`${c}-has-error`]:d==="error",[`${c}-is-validating`]:d==="validating",[`${c}-hidden`]:M,[`${c}-${x}`]:x});return l.createElement("div",{className:W,style:o,ref:E},l.createElement(Vt,Object.assign({className:`${c}-row`},It(v,["_internalItemRender","colon","dependencies","extra","fieldKey","getValueFromEvent","getValueProps","htmlFor","id","initialValue","isListField","label","labelAlign","labelCol","labelWrap","messageVariables","name","normalize","noStyle","preserve","requiredMark","rules","shouldUpdate","trigger","tooltip","validateFirst","validateTrigger","valuePropName","wrapperCol","validateDebounce"])),l.createElement(pn,Object.assign({htmlFor:L},e,{requiredMark:u,required:h??y,prefixCls:t,vertical:z})),l.createElement(un,Object.assign({},e,f,{errors:w,warnings:S,prefixCls:t,status:d,help:a,marginBottom:j,onErrorVisibleChanged:D}),l.createElement(We.Provider,{value:p},l.createElement(Ye,{prefixCls:t,meta:f,errors:f.errors,warnings:f.warnings,hasFeedback:g,validateStatus:d},N)))),!!j&&l.createElement("div",{className:`${c}-margin-offset`,style:{marginBottom:-j}}))}const vn="__SPLIT__";function Cn(e,t){const n=Object.keys(e),r=Object.keys(t);return n.length===r.length&&n.every(o=>{const a=e[o],s=t[o];return a===s||typeof a=="function"||typeof s=="function"})}const $n=l.memo(({children:e})=>e,(e,t)=>Cn(e.control,t.control)&&e.update===t.update&&e.childProps.length===t.childProps.length&&e.childProps.every((n,r)=>n===t.childProps[r]));function Re(){return{errors:[],warnings:[],touched:!1,validating:!1,name:[],validated:!1}}function xn(e){const{name:t,noStyle:n,className:r,dependencies:o,prefixCls:a,shouldUpdate:s,rules:i,children:m,required:f,label:g,messageVariables:M,trigger:N="onChange",validateTrigger:L,hidden:h,help:y,layout:p}=e,{getPrefixCls:x}=l.useContext(He),{name:v}=l.useContext(re),c=nn(m),u=typeof c=="function",O=l.useContext(We),{validateTrigger:z}=l.useContext(Ot),E=L!==void 0?L:z,w=t!=null,S=x("form",a),P=he(S),[R,X,j]=be(S,P);Et();const b=l.useContext(Ft),D=l.useRef(null),[T,d]=rn({}),[W,I]=Mt(()=>Re()),B=$=>{const _=b==null?void 0:b.getKey($.name);if(I($.destroy?Re():$,!0),n&&y!==!1&&O){let C=$.name;if($.destroy)C=D.current||C;else if(_!==void 0){const[H,F]=_;C=[H].concat(U(F)),D.current=C}O($,C)}},Y=($,_)=>{d(C=>{const H=Object.assign({},C),A=[].concat(U($.name.slice(0,-1)),U(_)).join(vn);return $.destroy?delete H[A]:H[A]=$,H})},[Q,J]=l.useMemo(()=>{const $=U(W.errors),_=U(W.warnings);return Object.values(T).forEach(C=>{$.push.apply($,U(C.errors||[])),_.push.apply(_,U(C.warnings||[]))}),[$,_]},[T,W.errors,W.warnings]),oe=on();function q($,_,C){return n&&!h?l.createElement(Ye,{prefixCls:S,hasFeedback:e.hasFeedback,validateStatus:e.validateStatus,meta:W,errors:Q,warnings:J,noStyle:!0},$):l.createElement(yn,Object.assign({key:"row"},e,{className:ne(r,j,P,X),prefixCls:S,fieldId:_,isRequired:C,errors:Q,warnings:J,meta:W,onSubItemMetaChange:Y,layout:p}),$)}if(!w&&!u&&!o)return R(q(c));let G={};return typeof g=="string"?G.label=g:t&&(G.label=String(t)),M&&(G=Object.assign(Object.assign({},G),M)),R(l.createElement(jt,Object.assign({},e,{messageVariables:G,trigger:N,validateTrigger:E,onMetaChange:B}),($,_,C)=>{const H=ie(t).length&&_?_.name:[],F=qe(H,v),A=f!==void 0?f:!!(i!=null&&i.some(V=>{if(V&&typeof V=="object"&&V.required&&!V.warningOnly)return!0;if(typeof V=="function"){const Z=V(C);return(Z==null?void 0:Z.required)&&!(Z!=null&&Z.warningOnly)}return!1})),K=Object.assign({},$);let te=null;if(Array.isArray(c)&&w)te=c;else if(!(u&&(!(s||o)||w))){if(!(o&&!u&&!w))if(l.isValidElement(c)){const V=Object.assign(Object.assign({},c.props),K);if(V.id||(V.id=F),y||Q.length>0||J.length>0||e.extra){const ae=[];(y||Q.length>0)&&ae.push(`${F}_help`),e.extra&&ae.push(`${F}_extra`),V["aria-describedby"]=ae.join(" ")}Q.length>0&&(V["aria-invalid"]="true"),A&&(V["aria-required"]="true"),Nt(c)&&(V.ref=oe(H,c)),new Set([].concat(U(ie(N)),U(ie(E)))).forEach(ae=>{V[ae]=(...ye)=>{var ve,Ce,me,$e,fe;(me=K[ae])===null||me===void 0||(ve=me).call.apply(ve,[K].concat(ye)),(fe=($e=c.props)[ae])===null||fe===void 0||(Ce=fe).call.apply(Ce,[$e].concat(ye))}});const Ue=[V["aria-required"],V["aria-invalid"],V["aria-describedby"]];te=l.createElement($n,{control:K,update:c,childProps:Ue},Pt(c,V))}else u&&(s||o)&&!w?te=c(C):te=c}return q(te,F,A)}))}const Qe=xn;Qe.useStatus=Ke;var wn=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n};const Sn=e=>{var{prefixCls:t,children:n}=e,r=wn(e,["prefixCls","children"]);const{getPrefixCls:o}=l.useContext(He),a=o("form",t),s=l.useMemo(()=>({prefixCls:a,status:"error"}),[a]);return l.createElement(Rt,Object.assign({},r),(i,m,f)=>l.createElement(pe.Provider,{value:s},n(i.map(g=>Object.assign(Object.assign({},g),{fieldKey:g.key})),m,{errors:f.errors,warnings:f.warnings})))};function In(){const{form:e}=l.useContext(re);return e}const le=tn;le.Item=Qe;le.List=Sn;le.ErrorList=Be;le.useForm=Ge;le.useFormInstance=In;le.useWatch=_t;le.Provider=Le;le.create=()=>{};export{le as F};
