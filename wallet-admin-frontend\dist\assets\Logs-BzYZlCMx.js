import{r,I as w,_ as b,au as W,j as e,T as V,a_ as G,B as j,as as _,S as q,A as y}from"./index-CW-Whzws.js";import{u as P,S as v,T as C}from"./index-pOBwrJ9T.js";import{u as Q,a as H,R as K}from"./ReloadOutlined-DZubnAgL.js";import{A as J,M as X}from"./index-CBm39ssz.js";import{C as E,c as Y,s as O}from"./index-BVbup1Oj.js";import{I as Z}from"./index-2rLJYotV.js";import"./Input-Cr3didPW.js";var ee={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M505.7 661a8 8 0 0012.6 0l112-141.7c4.1-5.2.4-12.9-6.3-12.9h-74.1V168c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v338.3H400c-6.7 0-10.4 7.7-6.3 12.9l112 141.8zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"}}]},name:"download",theme:"outlined"},te=function(a,o){return r.createElement(w,b({},a,{ref:o,icon:ee}))},ne=r.forwardRef(te),se={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"}},{tag:"path",attrs:{d:"M464 688a48 48 0 1096 0 48 48 0 10-96 0zm24-112h48c4.4 0 8-3.6 8-8V296c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8z"}}]},name:"exclamation-circle",theme:"outlined"},re=function(a,o){return r.createElement(w,b({},a,{ref:o,icon:se}))},oe=r.forwardRef(re);const{Title:le,Text:f}=V,{Option:d}=v,ie={INFO:"blue",WARN:"orange",ERROR:"red",DEBUG:"green",TRACE:"purple"},ge=()=>{const[n,a]=r.useState(""),[o,S]=r.useState(""),[R,x]=r.useState(""),[u,A]=r.useState("ALL"),[I,g]=r.useState(!1),T=W(),{data:l,isLoading:m,refetch:B,error:F}=P({queryKey:["logs"],queryFn:y.getLogs,staleTime:0,refetchOnMount:!0,refetchOnWindowFocus:!1}),p=Q({mutationFn:y.clearLogs,onSuccess:t=>{O.success(t.message||"日志清除成功！"),T.invalidateQueries({queryKey:["logs"]}),a(""),x("")},onError:t=>{console.error("清除日志失败:",t),O.error(`清除日志失败: ${t.message||"未知错误"}`)}}),h=l?Object.keys(l):[];r.useEffect(()=>{if(!l||!n){x("");return}let t=l[n]||"";u!=="ALL"&&(t=t.split(`
`).filter(s=>s.includes(u)||!s.trim()).join(`
`)),o.trim()&&(t=t.split(`
`).filter(s=>s.toLowerCase().includes(o.toLowerCase())).join(`
`)),x(t)},[l,n,o,u]),r.useEffect(()=>{h.length>0&&!n&&a(h[0])},[h,n]);const M=t=>{const c=t.match(/\s+(INFO|WARN|ERROR|DEBUG|TRACE)\s+/);return c?c[1]:"INFO"},z=t=>t?t.split(`
`).map((i,s)=>{if(!i.trim())return e.jsx("br",{},s);const L=M(i),$=ie[L]||"default";return e.jsxs("div",{style:{marginBottom:2,fontFamily:"monospace",fontSize:"12px"},children:[e.jsx(C,{color:$,style:{marginRight:8,minWidth:50,textAlign:"center",fontSize:"10px"},children:L}),e.jsx(f,{code:!0,style:{fontSize:"12px"},children:i.replace(/\s+(INFO|WARN|ERROR|DEBUG|TRACE)\s+/," ")})]},s)}):null,N=()=>{if(!n||!l)return;const t=l[n],c=new Blob([t],{type:"text/plain"}),i=URL.createObjectURL(c),s=document.createElement("a");s.href=i,s.download=n,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(i)},k=()=>{g(!0)},D=()=>{g(!1),p.mutate()},U=()=>{g(!1)};return F?e.jsx(J,{message:"日志加载失败",description:"无法获取系统日志，请检查网络连接或稍后重试。",type:"error",showIcon:!0}):e.jsxs("div",{children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:16},children:[e.jsxs(le,{level:2,children:[e.jsx(G,{})," 系统日志"]}),e.jsx(j,{icon:e.jsx(H,{}),onClick:()=>B(),loading:m,children:"刷新"})]}),e.jsx(E,{style:{marginBottom:16},children:e.jsxs(_,{wrap:!0,children:[e.jsxs("div",{children:[e.jsx(f,{strong:!0,children:"日志文件:"}),e.jsx(v,{style:{width:200,marginLeft:8},value:n,onChange:a,loading:m,children:h.map(t=>e.jsx(d,{value:t,children:t},t))})]}),e.jsxs("div",{children:[e.jsx(f,{strong:!0,children:"日志级别:"}),e.jsxs(v,{style:{width:120,marginLeft:8},value:u,onChange:A,children:[e.jsx(d,{value:"ALL",children:"全部"}),e.jsx(d,{value:"ERROR",children:"ERROR"}),e.jsx(d,{value:"WARN",children:"WARN"}),e.jsx(d,{value:"INFO",children:"INFO"}),e.jsx(d,{value:"DEBUG",children:"DEBUG"}),e.jsx(d,{value:"TRACE",children:"TRACE"})]})]}),e.jsx(Z,{placeholder:"搜索日志内容...",prefix:e.jsx(Y,{}),value:o,onChange:t=>S(t.target.value),style:{width:250},allowClear:!0}),e.jsx(j,{icon:e.jsx(ne,{}),onClick:N,disabled:!n||!l,children:"下载"}),e.jsx(j,{type:"primary",danger:!0,icon:e.jsx(K,{}),onClick:k,loading:p.isPending,children:"清除日志"})]})}),e.jsx(E,{title:`日志内容 - ${n}`,extra:e.jsx(C,{color:"blue",children:"手动刷新"}),children:m?e.jsx("div",{style:{textAlign:"center",padding:"50px 0"},children:e.jsx(q,{size:"large",tip:"加载日志中..."})}):e.jsx("div",{style:{height:"600px",overflow:"auto",backgroundColor:"#f5f5f5",padding:"16px",border:"1px solid #d9d9d9",borderRadius:"6px"},children:R?z(R):e.jsx(f,{type:"secondary",children:n?"没有找到匹配的日志内容":"请选择一个日志文件"})})}),e.jsxs(X,{title:"确认清除所有日志？",open:I,onOk:D,onCancel:U,okText:"确认清除",cancelText:"取消",okType:"danger",confirmLoading:p.isPending,children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",marginBottom:16},children:[e.jsx(oe,{style:{color:"#faad14",fontSize:22,marginRight:12}}),e.jsx("span",{children:"此操作将清除所有历史日志文件和缓存数据。"})]}),e.jsx("p",{style:{color:"#ff4d4f",fontWeight:"bold",marginLeft:34},children:"注意：此操作不可撤销！"})]})]})};export{ge as default};
