import{r as i,I as mt,_ as Q,b as c,x as we,ao as U,c as W,t as H,ae as at,G as Mt,ai as Rt,g as Tt,m as _t,M as Dt,f as At,e as x,bt as rt,ap as Ht,K as Lt,O as Kt,bM as Wt,bN as Vt,bO as qt,aN as Jt,l as Xt,bo as Gt,h as Ut,i as Ft,a9 as kt,bP as Qt,bQ as it,bR as nt,A as Ie}from"./index-CW-Whzws.js";import{p as Yt}from"./index-BVbup1Oj.js";import{S as Zt}from"./index-pOBwrJ9T.js";var ea={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M272.9 512l265.4-339.1c4.1-5.2.4-12.9-6.3-12.9h-77.3c-4.9 0-9.6 2.3-12.6 6.1L186.8 492.3a31.99 31.99 0 000 39.5l255.3 326.1c3 3.9 7.7 6.1 12.6 6.1H532c6.7 0 10.4-7.7 6.3-12.9L272.9 512zm304 0l265.4-339.1c4.1-5.2.4-12.9-6.3-12.9h-77.3c-4.9 0-9.6 2.3-12.6 6.1L490.8 492.3a31.99 31.99 0 000 39.5l255.3 326.1c3 3.9 7.7 6.1 12.6 6.1H836c6.7 0 10.4-7.7 6.3-12.9L576.9 512z"}}]},name:"double-left",theme:"outlined"},ta=function(t,b){return i.createElement(mt,Q({},t,{ref:b,icon:ea}))},ot=i.forwardRef(ta),aa={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M533.2 492.3L277.9 166.1c-3-3.9-7.7-6.1-12.6-6.1H188c-6.7 0-10.4 7.7-6.3 12.9L447.1 512 181.7 851.1A7.98 7.98 0 00188 864h77.3c4.9 0 9.6-2.3 12.6-6.1l255.3-326.1c9.1-11.7 9.1-27.9 0-39.5zm304 0L581.9 166.1c-3-3.9-7.7-6.1-12.6-6.1H492c-6.7 0-10.4 7.7-6.3 12.9L751.1 512 485.7 851.1A7.98 7.98 0 00492 864h77.3c4.9 0 9.6-2.3 12.6-6.1l255.3-326.1c9.1-11.7 9.1-27.9 0-39.5z"}}]},name:"double-right",theme:"outlined"},ra=function(t,b){return i.createElement(mt,Q({},t,{ref:b,icon:aa}))},lt=i.forwardRef(ra),ia={items_per_page:"条/页",jump_to:"跳至",jump_to_confirm:"确定",page:"页",prev_page:"上一页",next_page:"下一页",prev_5:"向前 5 页",next_5:"向后 5 页",prev_3:"向前 3 页",next_3:"向后 3 页",page_size:"页码"},na=[10,20,50,100],oa=function(t){var b=t.pageSizeOptions,a=b===void 0?na:b,h=t.locale,B=t.changeSize,w=t.pageSize,P=t.goButton,C=t.quickGo,M=t.rootPrefixCls,z=t.disabled,p=t.buildOptionText,R=t.showSizeChanger,_=t.sizeChangerRender,L=c.useState(""),T=we(L,2),$=T[0],I=T[1],K=function(){return!$||Number.isNaN($)?void 0:Number($)},l=typeof p=="function"?p:function(v){return"".concat(v," ").concat(h.items_per_page)},d=function(f){I(f.target.value)},o=function(f){P||$===""||(I(""),!(f.relatedTarget&&(f.relatedTarget.className.indexOf("".concat(M,"-item-link"))>=0||f.relatedTarget.className.indexOf("".concat(M,"-item"))>=0))&&(C==null||C(K())))},n=function(f){$!==""&&(f.keyCode===U.ENTER||f.type==="click")&&(I(""),C==null||C(K()))},g=function(){return a.some(function(f){return f.toString()===w.toString()})?a:a.concat([w]).sort(function(f,V){var ie=Number.isNaN(Number(f))?0:Number(f),F=Number.isNaN(Number(V))?0:Number(V);return ie-F})},s="".concat(M,"-options");if(!R&&!C)return null;var y=null,j=null,D=null;return R&&_&&(y=_({disabled:z,size:w,onSizeChange:function(f){B==null||B(Number(f))},"aria-label":h.page_size,className:"".concat(s,"-size-changer"),options:g().map(function(v){return{label:l(v),value:v}})})),C&&(P&&(D=typeof P=="boolean"?c.createElement("button",{type:"button",onClick:n,onKeyUp:n,disabled:z,className:"".concat(s,"-quick-jumper-button")},h.jump_to_confirm):c.createElement("span",{onClick:n,onKeyUp:n},P)),j=c.createElement("div",{className:"".concat(s,"-quick-jumper")},h.jump_to,c.createElement("input",{disabled:z,type:"text",value:$,onChange:d,onKeyUp:n,onBlur:o,"aria-label":h.page}),h.page,D)),c.createElement("li",{className:s},y,j)},pe=function(t){var b=t.rootPrefixCls,a=t.page,h=t.active,B=t.className,w=t.showTitle,P=t.onClick,C=t.onKeyPress,M=t.itemRender,z="".concat(b,"-item"),p=W(z,"".concat(z,"-").concat(a),H(H({},"".concat(z,"-active"),h),"".concat(z,"-disabled"),!a),B),R=function(){P(a)},_=function($){C($,P,a)},L=M(a,"page",c.createElement("a",{rel:"nofollow"},a));return L?c.createElement("li",{title:w?String(a):null,className:p,onClick:R,onKeyDown:_,tabIndex:0},L):null},la=function(t,b,a){return a};function ct(){}function st(e){var t=Number(e);return typeof t=="number"&&!Number.isNaN(t)&&isFinite(t)&&Math.floor(t)===t}function te(e,t,b){var a=typeof e>"u"?t:e;return Math.floor((b-1)/a)+1}var ca=function(t){var b=t.prefixCls,a=b===void 0?"rc-pagination":b,h=t.selectPrefixCls,B=h===void 0?"rc-select":h,w=t.className,P=t.current,C=t.defaultCurrent,M=C===void 0?1:C,z=t.total,p=z===void 0?0:z,R=t.pageSize,_=t.defaultPageSize,L=_===void 0?10:_,T=t.onChange,$=T===void 0?ct:T,I=t.hideOnSinglePage,K=t.align,l=t.showPrevNextJumpers,d=l===void 0?!0:l,o=t.showQuickJumper,n=t.showLessItems,g=t.showTitle,s=g===void 0?!0:g,y=t.onShowSizeChange,j=y===void 0?ct:y,D=t.locale,v=D===void 0?ia:D,f=t.style,V=t.totalBoundaryShowSizeChanger,ie=V===void 0?50:V,F=t.disabled,q=t.simple,ne=t.showTotal,ve=t.showSizeChanger,Be=ve===void 0?p>ie:ve,Me=t.sizeChangerRender,Re=t.pageSizeOptions,be=t.itemRender,Y=be===void 0?la:be,he=t.jumpPrevIcon,J=t.jumpNextIcon,Z=t.prevIcon,oe=t.nextIcon,le=c.useRef(null),ee=at(10,{value:R,defaultValue:L}),fe=we(ee,2),E=fe[0],Se=fe[1],Te=at(1,{value:P,defaultValue:M,postState:function(m){return Math.max(1,Math.min(m,te(void 0,E,p)))}}),ae=we(Te,2),u=ae[0],X=ae[1],_e=c.useState(u),Ke=we(_e,2),re=Ke[0],Ce=Ke[1];i.useEffect(function(){Ce(u)},[u]);var We=Math.max(1,u-(n?3:5)),Ve=Math.min(te(void 0,E,p),u+(n?3:5));function $e(r,m){var S=r||c.createElement("button",{type:"button","aria-label":m,className:"".concat(a,"-item-link")});return typeof r=="function"&&(S=c.createElement(r,Rt({},t))),S}function qe(r){var m=r.target.value,S=te(void 0,E,p),k;return m===""?k=m:Number.isNaN(Number(m))?k=re:m>=S?k=S:k=Number(m),k}function pt(r){return st(r)&&r!==u&&st(p)&&p>0}var vt=p>E?o:!1;function bt(r){(r.keyCode===U.UP||r.keyCode===U.DOWN)&&r.preventDefault()}function Je(r){var m=qe(r);switch(m!==re&&Ce(m),r.keyCode){case U.ENTER:A(m);break;case U.UP:A(m-1);break;case U.DOWN:A(m+1);break}}function ht(r){A(qe(r))}function ft(r){var m=te(r,E,p),S=u>m&&m!==0?m:u;Se(r),Ce(S),j==null||j(u,r),X(S),$==null||$(S,r)}function A(r){if(pt(r)&&!F){var m=te(void 0,E,p),S=r;return r>m?S=m:r<1&&(S=1),S!==re&&Ce(S),X(S),$==null||$(S,E),S}return u}var ye=u>1,xe=u<te(void 0,E,p);function Xe(){ye&&A(u-1)}function Ge(){xe&&A(u+1)}function Ue(){A(We)}function Fe(){A(Ve)}function ce(r,m){if(r.key==="Enter"||r.charCode===U.ENTER||r.keyCode===U.ENTER){for(var S=arguments.length,k=new Array(S>2?S-2:0),Oe=2;Oe<S;Oe++)k[Oe-2]=arguments[Oe];m.apply(void 0,k)}}function St(r){ce(r,Xe)}function Ct(r){ce(r,Ge)}function $t(r){ce(r,Ue)}function yt(r){ce(r,Fe)}function xt(r){var m=Y(r,"prev",$e(Z,"prev page"));return c.isValidElement(m)?c.cloneElement(m,{disabled:!ye}):m}function zt(r){var m=Y(r,"next",$e(oe,"next page"));return c.isValidElement(m)?c.cloneElement(m,{disabled:!xe}):m}function ze(r){(r.type==="click"||r.keyCode===U.ENTER)&&A(re)}var ke=null,Nt=Yt(t,{aria:!0,data:!0}),Et=ne&&c.createElement("li",{className:"".concat(a,"-total-text")},ne(p,[p===0?0:(u-1)*E+1,u*E>p?p:u*E])),Qe=null,N=te(void 0,E,p);if(I&&p<=E)return null;var O=[],se={rootPrefixCls:a,onClick:A,onKeyPress:ce,showTitle:s,itemRender:Y,page:-1},Pt=u-1>0?u-1:0,Ot=u+1<N?u+1:N,Ne=o&&o.goButton,It=Mt(q)==="object"?q.readOnly:!q,ue=Ne,Ye=null;q&&(Ne&&(typeof Ne=="boolean"?ue=c.createElement("button",{type:"button",onClick:ze,onKeyUp:ze},v.jump_to_confirm):ue=c.createElement("span",{onClick:ze,onKeyUp:ze},Ne),ue=c.createElement("li",{title:s?"".concat(v.jump_to).concat(u,"/").concat(N):null,className:"".concat(a,"-simple-pager")},ue)),Ye=c.createElement("li",{title:s?"".concat(u,"/").concat(N):null,className:"".concat(a,"-simple-pager")},It?re:c.createElement("input",{type:"text","aria-label":v.jump_to,value:re,disabled:F,onKeyDown:bt,onKeyUp:Je,onChange:Je,onBlur:ht,size:3}),c.createElement("span",{className:"".concat(a,"-slash")},"/"),N));var G=n?1:2;if(N<=3+G*2){N||O.push(c.createElement(pe,Q({},se,{key:"noPager",page:1,className:"".concat(a,"-item-disabled")})));for(var me=1;me<=N;me+=1)O.push(c.createElement(pe,Q({},se,{key:me,page:me,active:u===me})))}else{var jt=n?v.prev_3:v.prev_5,wt=n?v.next_3:v.next_5,Ze=Y(We,"jump-prev",$e(he,"prev page")),et=Y(Ve,"jump-next",$e(J,"next page"));d&&(ke=Ze?c.createElement("li",{title:s?jt:null,key:"prev",onClick:Ue,tabIndex:0,onKeyDown:$t,className:W("".concat(a,"-jump-prev"),H({},"".concat(a,"-jump-prev-custom-icon"),!!he))},Ze):null,Qe=et?c.createElement("li",{title:s?wt:null,key:"next",onClick:Fe,tabIndex:0,onKeyDown:yt,className:W("".concat(a,"-jump-next"),H({},"".concat(a,"-jump-next-custom-icon"),!!J))},et):null);var De=Math.max(1,u-G),Ae=Math.min(u+G,N);u-1<=G&&(Ae=1+G*2),N-u<=G&&(De=N-G*2);for(var de=De;de<=Ae;de+=1)O.push(c.createElement(pe,Q({},se,{key:de,page:de,active:u===de})));if(u-1>=G*2&&u!==3&&(O[0]=c.cloneElement(O[0],{className:W("".concat(a,"-item-after-jump-prev"),O[0].props.className)}),O.unshift(ke)),N-u>=G*2&&u!==N-2){var tt=O[O.length-1];O[O.length-1]=c.cloneElement(tt,{className:W("".concat(a,"-item-before-jump-next"),tt.props.className)}),O.push(Qe)}De!==1&&O.unshift(c.createElement(pe,Q({},se,{key:1,page:1}))),Ae!==N&&O.push(c.createElement(pe,Q({},se,{key:N,page:N})))}var Ee=xt(Pt);if(Ee){var He=!ye||!N;Ee=c.createElement("li",{title:s?v.prev_page:null,onClick:Xe,tabIndex:He?null:0,onKeyDown:St,className:W("".concat(a,"-prev"),H({},"".concat(a,"-disabled"),He)),"aria-disabled":He},Ee)}var Pe=zt(Ot);if(Pe){var ge,Le;q?(ge=!xe,Le=ye?0:null):(ge=!xe||!N,Le=ge?null:0),Pe=c.createElement("li",{title:s?v.next_page:null,onClick:Ge,tabIndex:Le,onKeyDown:Ct,className:W("".concat(a,"-next"),H({},"".concat(a,"-disabled"),ge)),"aria-disabled":ge},Pe)}var Bt=W(a,w,H(H(H(H(H({},"".concat(a,"-start"),K==="start"),"".concat(a,"-center"),K==="center"),"".concat(a,"-end"),K==="end"),"".concat(a,"-simple"),q),"".concat(a,"-disabled"),F));return c.createElement("ul",Q({className:Bt,style:f,ref:le},Nt),Et,Ee,q?Ye:O,Pe,c.createElement(oa,{locale:v,rootPrefixCls:a,disabled:F,selectPrefixCls:B,changeSize:ft,pageSize:E,pageSizeOptions:Re,quickGo:vt?A:null,goButton:ue,showSizeChanger:Be,sizeChangerRender:Me}))};const sa=e=>{const{componentCls:t}=e;return{[`${t}-disabled`]:{"&, &:hover":{cursor:"not-allowed",[`${t}-item-link`]:{color:e.colorTextDisabled,cursor:"not-allowed"}},"&:focus-visible":{cursor:"not-allowed",[`${t}-item-link`]:{color:e.colorTextDisabled,cursor:"not-allowed"}}},[`&${t}-disabled`]:{cursor:"not-allowed",[`${t}-item`]:{cursor:"not-allowed",backgroundColor:"transparent","&:hover, &:active":{backgroundColor:"transparent"},a:{color:e.colorTextDisabled,backgroundColor:"transparent",border:"none",cursor:"not-allowed"},"&-active":{borderColor:e.colorBorder,backgroundColor:e.itemActiveBgDisabled,"&:hover, &:active":{backgroundColor:e.itemActiveBgDisabled},a:{color:e.itemActiveColorDisabled}}},[`${t}-item-link`]:{color:e.colorTextDisabled,cursor:"not-allowed","&:hover, &:active":{backgroundColor:"transparent"},[`${t}-simple&`]:{backgroundColor:"transparent","&:hover, &:active":{backgroundColor:"transparent"}}},[`${t}-simple-pager`]:{color:e.colorTextDisabled},[`${t}-jump-prev, ${t}-jump-next`]:{[`${t}-item-link-icon`]:{opacity:0},[`${t}-item-ellipsis`]:{opacity:1}}},[`&${t}-simple`]:{[`${t}-prev, ${t}-next`]:{[`&${t}-disabled ${t}-item-link`]:{"&:hover, &:active":{backgroundColor:"transparent"}}}}}},ua=e=>{const{componentCls:t}=e;return{[`&${t}-mini ${t}-total-text, &${t}-mini ${t}-simple-pager`]:{height:e.itemSizeSM,lineHeight:x(e.itemSizeSM)},[`&${t}-mini ${t}-item`]:{minWidth:e.itemSizeSM,height:e.itemSizeSM,margin:0,lineHeight:x(e.calc(e.itemSizeSM).sub(2).equal())},[`&${t}-mini ${t}-prev, &${t}-mini ${t}-next`]:{minWidth:e.itemSizeSM,height:e.itemSizeSM,margin:0,lineHeight:x(e.itemSizeSM)},[`&${t}-mini:not(${t}-disabled)`]:{[`${t}-prev, ${t}-next`]:{[`&:hover ${t}-item-link`]:{backgroundColor:e.colorBgTextHover},[`&:active ${t}-item-link`]:{backgroundColor:e.colorBgTextActive},[`&${t}-disabled:hover ${t}-item-link`]:{backgroundColor:"transparent"}}},[`
    &${t}-mini ${t}-prev ${t}-item-link,
    &${t}-mini ${t}-next ${t}-item-link
    `]:{backgroundColor:"transparent",borderColor:"transparent","&::after":{height:e.itemSizeSM,lineHeight:x(e.itemSizeSM)}},[`&${t}-mini ${t}-jump-prev, &${t}-mini ${t}-jump-next`]:{height:e.itemSizeSM,marginInlineEnd:0,lineHeight:x(e.itemSizeSM)},[`&${t}-mini ${t}-options`]:{marginInlineStart:e.paginationMiniOptionsMarginInlineStart,"&-size-changer":{top:e.miniOptionsSizeChangerTop},"&-quick-jumper":{height:e.itemSizeSM,lineHeight:x(e.itemSizeSM),input:Object.assign(Object.assign({},qt(e)),{width:e.paginationMiniQuickJumperInputWidth,height:e.controlHeightSM})}}}},ma=e=>{const{componentCls:t}=e;return{[`
    &${t}-simple ${t}-prev,
    &${t}-simple ${t}-next
    `]:{height:e.itemSizeSM,lineHeight:x(e.itemSizeSM),verticalAlign:"top",[`${t}-item-link`]:{height:e.itemSizeSM,backgroundColor:"transparent",border:0,"&:hover":{backgroundColor:e.colorBgTextHover},"&:active":{backgroundColor:e.colorBgTextActive},"&::after":{height:e.itemSizeSM,lineHeight:x(e.itemSizeSM)}}},[`&${t}-simple ${t}-simple-pager`]:{display:"inline-block",height:e.itemSizeSM,marginInlineEnd:e.marginXS,input:{boxSizing:"border-box",height:"100%",padding:`0 ${x(e.paginationItemPaddingInline)}`,textAlign:"center",backgroundColor:e.itemInputBg,border:`${x(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadius,outline:"none",transition:`border-color ${e.motionDurationMid}`,color:"inherit","&:hover":{borderColor:e.colorPrimary},"&:focus":{borderColor:e.colorPrimaryHover,boxShadow:`${x(e.inputOutlineOffset)} 0 ${x(e.controlOutlineWidth)} ${e.controlOutline}`},"&[disabled]":{color:e.colorTextDisabled,backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,cursor:"not-allowed"}}}}},da=e=>{const{componentCls:t}=e;return{[`${t}-jump-prev, ${t}-jump-next`]:{outline:0,[`${t}-item-container`]:{position:"relative",[`${t}-item-link-icon`]:{color:e.colorPrimary,fontSize:e.fontSizeSM,opacity:0,transition:`all ${e.motionDurationMid}`,"&-svg":{top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,margin:"auto"}},[`${t}-item-ellipsis`]:{position:"absolute",top:0,insetInlineEnd:0,bottom:0,insetInlineStart:0,display:"block",margin:"auto",color:e.colorTextDisabled,letterSpacing:e.paginationEllipsisLetterSpacing,textAlign:"center",textIndent:e.paginationEllipsisTextIndent,opacity:1,transition:`all ${e.motionDurationMid}`}},"&:hover":{[`${t}-item-link-icon`]:{opacity:1},[`${t}-item-ellipsis`]:{opacity:0}}},[`
    ${t}-prev,
    ${t}-jump-prev,
    ${t}-jump-next
    `]:{marginInlineEnd:e.marginXS},[`
    ${t}-prev,
    ${t}-next,
    ${t}-jump-prev,
    ${t}-jump-next
    `]:{display:"inline-block",minWidth:e.itemSize,height:e.itemSize,color:e.colorText,fontFamily:e.fontFamily,lineHeight:x(e.itemSize),textAlign:"center",verticalAlign:"middle",listStyle:"none",borderRadius:e.borderRadius,cursor:"pointer",transition:`all ${e.motionDurationMid}`},[`${t}-prev, ${t}-next`]:{outline:0,button:{color:e.colorText,cursor:"pointer",userSelect:"none"},[`${t}-item-link`]:{display:"block",width:"100%",height:"100%",padding:0,fontSize:e.fontSizeSM,textAlign:"center",backgroundColor:"transparent",border:`${x(e.lineWidth)} ${e.lineType} transparent`,borderRadius:e.borderRadius,outline:"none",transition:`all ${e.motionDurationMid}`},[`&:hover ${t}-item-link`]:{backgroundColor:e.colorBgTextHover},[`&:active ${t}-item-link`]:{backgroundColor:e.colorBgTextActive},[`&${t}-disabled:hover`]:{[`${t}-item-link`]:{backgroundColor:"transparent"}}},[`${t}-slash`]:{marginInlineEnd:e.paginationSlashMarginInlineEnd,marginInlineStart:e.paginationSlashMarginInlineStart},[`${t}-options`]:{display:"inline-block",marginInlineStart:e.margin,verticalAlign:"middle","&-size-changer":{display:"inline-block",width:"auto"},"&-quick-jumper":{display:"inline-block",height:e.controlHeight,marginInlineStart:e.marginXS,lineHeight:x(e.controlHeight),verticalAlign:"top",input:Object.assign(Object.assign(Object.assign({},Kt(e)),Wt(e,{borderColor:e.colorBorder,hoverBorderColor:e.colorPrimaryHover,activeBorderColor:e.colorPrimary,activeShadow:e.activeShadow})),{"&[disabled]":Object.assign({},Vt(e)),width:e.calc(e.controlHeightLG).mul(1.25).equal(),height:e.controlHeight,boxSizing:"border-box",margin:0,marginInlineStart:e.marginXS,marginInlineEnd:e.marginXS})}}}},ga=e=>{const{componentCls:t}=e;return{[`${t}-item`]:{display:"inline-block",minWidth:e.itemSize,height:e.itemSize,marginInlineEnd:e.marginXS,fontFamily:e.fontFamily,lineHeight:x(e.calc(e.itemSize).sub(2).equal()),textAlign:"center",verticalAlign:"middle",listStyle:"none",backgroundColor:e.itemBg,border:`${x(e.lineWidth)} ${e.lineType} transparent`,borderRadius:e.borderRadius,outline:0,cursor:"pointer",userSelect:"none",a:{display:"block",padding:`0 ${x(e.paginationItemPaddingInline)}`,color:e.colorText,"&:hover":{textDecoration:"none"}},[`&:not(${t}-item-active)`]:{"&:hover":{transition:`all ${e.motionDurationMid}`,backgroundColor:e.colorBgTextHover},"&:active":{backgroundColor:e.colorBgTextActive}},"&-active":{fontWeight:e.fontWeightStrong,backgroundColor:e.itemActiveBg,borderColor:e.colorPrimary,a:{color:e.colorPrimary},"&:hover":{borderColor:e.colorPrimaryHover},"&:hover a":{color:e.colorPrimaryHover}}}}},pa=e=>{const{componentCls:t}=e;return{[t]:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},At(e)),{display:"flex","&-start":{justifyContent:"start"},"&-center":{justifyContent:"center"},"&-end":{justifyContent:"end"},"ul, ol":{margin:0,padding:0,listStyle:"none"},"&::after":{display:"block",clear:"both",height:0,overflow:"hidden",visibility:"hidden",content:'""'},[`${t}-total-text`]:{display:"inline-block",height:e.itemSize,marginInlineEnd:e.marginXS,lineHeight:x(e.calc(e.itemSize).sub(2).equal()),verticalAlign:"middle"}}),ga(e)),da(e)),ma(e)),ua(e)),sa(e)),{[`@media only screen and (max-width: ${e.screenLG}px)`]:{[`${t}-item`]:{"&-after-jump-prev, &-before-jump-next":{display:"none"}}},[`@media only screen and (max-width: ${e.screenSM}px)`]:{[`${t}-options`]:{display:"none"}}}),[`&${e.componentCls}-rtl`]:{direction:"rtl"}}},va=e=>{const{componentCls:t}=e;return{[`${t}:not(${t}-disabled)`]:{[`${t}-item`]:Object.assign({},Ht(e)),[`${t}-jump-prev, ${t}-jump-next`]:{"&:focus-visible":Object.assign({[`${t}-item-link-icon`]:{opacity:1},[`${t}-item-ellipsis`]:{opacity:0}},rt(e))},[`${t}-prev, ${t}-next`]:{[`&:focus-visible ${t}-item-link`]:Object.assign({},rt(e))}}}},dt=e=>Object.assign({itemBg:e.colorBgContainer,itemSize:e.controlHeight,itemSizeSM:e.controlHeightSM,itemActiveBg:e.colorBgContainer,itemLinkBg:e.colorBgContainer,itemActiveColorDisabled:e.colorTextDisabled,itemActiveBgDisabled:e.controlItemBgActiveDisabled,itemInputBg:e.colorBgContainer,miniOptionsSizeChangerTop:0},Lt(e)),gt=e=>_t(e,{inputOutlineOffset:0,paginationMiniOptionsMarginInlineStart:e.calc(e.marginXXS).div(2).equal(),paginationMiniQuickJumperInputWidth:e.calc(e.controlHeightLG).mul(1.1).equal(),paginationItemPaddingInline:e.calc(e.marginXXS).mul(1.5).equal(),paginationEllipsisLetterSpacing:e.calc(e.marginXXS).div(2).equal(),paginationSlashMarginInlineStart:e.marginSM,paginationSlashMarginInlineEnd:e.marginSM,paginationEllipsisTextIndent:"0.13em"},Dt(e)),ba=Tt("Pagination",e=>{const t=gt(e);return[pa(t),va(t)]},dt),ha=e=>{const{componentCls:t}=e;return{[`${t}${t}-bordered${t}-disabled:not(${t}-mini)`]:{"&, &:hover":{[`${t}-item-link`]:{borderColor:e.colorBorder}},"&:focus-visible":{[`${t}-item-link`]:{borderColor:e.colorBorder}},[`${t}-item, ${t}-item-link`]:{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,[`&:hover:not(${t}-item-active)`]:{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,a:{color:e.colorTextDisabled}},[`&${t}-item-active`]:{backgroundColor:e.itemActiveBgDisabled}},[`${t}-prev, ${t}-next`]:{"&:hover button":{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder,color:e.colorTextDisabled},[`${t}-item-link`]:{backgroundColor:e.colorBgContainerDisabled,borderColor:e.colorBorder}}},[`${t}${t}-bordered:not(${t}-mini)`]:{[`${t}-prev, ${t}-next`]:{"&:hover button":{borderColor:e.colorPrimaryHover,backgroundColor:e.itemBg},[`${t}-item-link`]:{backgroundColor:e.itemLinkBg,borderColor:e.colorBorder},[`&:hover ${t}-item-link`]:{borderColor:e.colorPrimary,backgroundColor:e.itemBg,color:e.colorPrimary},[`&${t}-disabled`]:{[`${t}-item-link`]:{borderColor:e.colorBorder,color:e.colorTextDisabled}}},[`${t}-item`]:{backgroundColor:e.itemBg,border:`${x(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,[`&:hover:not(${t}-item-active)`]:{borderColor:e.colorPrimary,backgroundColor:e.itemBg,a:{color:e.colorPrimary}},"&-active":{borderColor:e.colorPrimary}}}}},fa=Jt(["Pagination","bordered"],e=>{const t=gt(e);return[ha(t)]},dt);function ut(e){return i.useMemo(()=>typeof e=="boolean"?[e,{}]:e&&typeof e=="object"?[!0,e]:[void 0,void 0],[e])}var Sa=function(e,t){var b={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(b[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var h=0,a=Object.getOwnPropertySymbols(e);h<a.length;h++)t.indexOf(a[h])<0&&Object.prototype.propertyIsEnumerable.call(e,a[h])&&(b[a[h]]=e[a[h]]);return b};const xa=e=>{const{align:t,prefixCls:b,selectPrefixCls:a,className:h,rootClassName:B,style:w,size:P,locale:C,responsive:M,showSizeChanger:z,selectComponentClass:p,pageSizeOptions:R}=e,_=Sa(e,["align","prefixCls","selectPrefixCls","className","rootClassName","style","size","locale","responsive","showSizeChanger","selectComponentClass","pageSizeOptions"]),{xs:L}=Xt(M),[,T]=Gt(),{getPrefixCls:$,direction:I,showSizeChanger:K,className:l,style:d}=Ut("pagination"),o=$("pagination",b),[n,g,s]=ba(o),y=Ft(P),j=y==="small"||!!(L&&!y&&M),[D]=kt("Pagination",Qt),v=Object.assign(Object.assign({},D),C),[f,V]=ut(z),[ie,F]=ut(K),q=f??ie,ne=V??F,ve=p||Zt,Be=i.useMemo(()=>R?R.map(J=>Number(J)):void 0,[R]),Me=J=>{var Z;const{disabled:oe,size:le,onSizeChange:ee,"aria-label":fe,className:E,options:Se}=J,{className:Te,onChange:ae}=ne||{},u=(Z=Se.find(X=>String(X.value)===String(le)))===null||Z===void 0?void 0:Z.value;return i.createElement(ve,Object.assign({disabled:oe,showSearch:!0,popupMatchSelectWidth:!1,getPopupContainer:X=>X.parentNode,"aria-label":fe,options:Se},ne,{value:u,onChange:(X,_e)=>{ee==null||ee(X),ae==null||ae(X,_e)},size:j?"small":"middle",className:W(E,Te)}))},Re=i.useMemo(()=>{const J=i.createElement("span",{className:`${o}-item-ellipsis`},"•••"),Z=i.createElement("button",{className:`${o}-item-link`,type:"button",tabIndex:-1},I==="rtl"?i.createElement(nt,null):i.createElement(it,null)),oe=i.createElement("button",{className:`${o}-item-link`,type:"button",tabIndex:-1},I==="rtl"?i.createElement(it,null):i.createElement(nt,null)),le=i.createElement("a",{className:`${o}-item-link`},i.createElement("div",{className:`${o}-item-container`},I==="rtl"?i.createElement(lt,{className:`${o}-item-link-icon`}):i.createElement(ot,{className:`${o}-item-link-icon`}),J)),ee=i.createElement("a",{className:`${o}-item-link`},i.createElement("div",{className:`${o}-item-container`},I==="rtl"?i.createElement(ot,{className:`${o}-item-link-icon`}):i.createElement(lt,{className:`${o}-item-link-icon`}),J));return{prevIcon:Z,nextIcon:oe,jumpPrevIcon:le,jumpNextIcon:ee}},[I,o]),be=$("select",a),Y=W({[`${o}-${t}`]:!!t,[`${o}-mini`]:j,[`${o}-rtl`]:I==="rtl",[`${o}-bordered`]:T.wireframe},l,h,B,g,s),he=Object.assign(Object.assign({},d),w);return n(i.createElement(i.Fragment,null,T.wireframe&&i.createElement(fa,{prefixCls:o}),i.createElement(ca,Object.assign({},Re,_,{style:he,prefixCls:o,selectPrefixCls:be,className:Y,locale:v,pageSizeOptions:Be,showSizeChanger:q,sizeChangerRender:Me}))))},je="wallet_remarks_cache",za=()=>{const[e,t]=i.useState({}),[b,a]=i.useState(!0),[h,B]=i.useState(0),w=i.useRef(!1),P=i.useRef({}),C=i.useCallback(()=>{setTimeout(()=>{try{const l=localStorage.getItem(je);if(l){const d=JSON.parse(l);t(d),console.log(`📝 加载了 ${Object.keys(d).length} 个缓存备注`)}}catch(l){console.error("加载钱包备注缓存失败:",l)}},0)},[]),M=i.useCallback(l=>{const d={};let o=!1;console.log("🔍 钱包配置数据:",l),Object.entries(l).forEach(([n,g])=>{if(console.log(`🔍 处理配置 ${n}:`,g),g.remark){console.log(`📝 发现备注: ${g.wallet_address} -> ${g.remark}`);const s=g.protocol||"pump",y=`${g.wallet_address}@${s}`;d[y]=g.remark,P.current[y]!==g.remark&&(o=!0)}else console.log(`❌ 配置 ${g.wallet_address} 没有备注`)}),Object.keys(P.current).forEach(n=>{d[n]||(o=!0)}),o&&(P.current=d,t(n=>{const g={...n};return Object.entries(d).forEach(([s,y])=>{const[j,D]=s.split("@");g[s]={address:j,protocol:D,remark:y,updatedAt:Date.now(),source:"server"}}),Object.keys(n).forEach(s=>{n[s].source==="server"&&!d[s]&&delete g[s]}),g}),B(n=>n+1),console.log(`📝 同步了 ${Object.keys(d).length} 个服务器备注:`,d))},[]),z=i.useCallback(l=>{"requestIdleCallback"in window?requestIdleCallback(()=>{try{localStorage.setItem(je,JSON.stringify(l))}catch(d){console.error("保存钱包备注缓存失败:",d)}}):setTimeout(()=>{try{localStorage.setItem(je,JSON.stringify(l))}catch(d){console.error("保存钱包备注缓存失败:",d)}},0)},[]),p=i.useCallback(async(l,d,o="pump")=>{const n=d.trim(),g=`${l}@${o}`;t(s=>{const y={...s,[g]:{address:l,protocol:o,remark:n,updatedAt:Date.now(),source:"local"}};return z(y),y}),B(s=>s+1);try{const s=await Ie.getWalletConfigurations(),y=o==="bonk"?`${l}_bonk`:l,j=s[y];if(j){const D={...j,remark:n||null};await Ie.updateWalletConfiguration(D),t(v=>({...v,[g]:{address:l,protocol:o,remark:n,updatedAt:Date.now(),source:"server"}})),console.log(`📝 备注已同步到服务器: ${l}`)}else console.log(`📝 钱包配置不存在，备注仅保存在本地: ${l}`)}catch(s){console.error("同步备注到服务器失败:",s)}},[z]),R=i.useCallback(async(l,d="pump")=>{const o=`${l}@${d}`;t(n=>{const g={...n};return delete g[o],z(g),g});try{const n=await Ie.getWalletConfigurations(),g=d==="bonk"?`${l}_bonk`:l,s=n[g];if(s&&s.remark){const y={...s,remark:null};await Ie.updateWalletConfiguration(y),console.log(`📝 服务器备注已删除: ${l}`)}}catch(n){console.error("删除服务器备注失败:",n)}},[z]),_=i.useCallback((l,d="pump")=>{const o=`${l}@${d}`,n=e[o];return n&&n.remark?n.remark:`${l.slice(0,6)}...${l.slice(-4)}`},[e]),L=i.useCallback((l,d="pump")=>{const o=`${l}@${d}`,n=e[o];return(n==null?void 0:n.remark)||null},[e]),T=i.useCallback((l,d="pump")=>{var n;const o=`${l}@${d}`;return!!((n=e[o])!=null&&n.remark)},[e]),$=i.useCallback(()=>Object.values(e),[e]),I=i.useCallback(()=>{localStorage.removeItem(je),t({}),w.current=!1},[]),K=i.useCallback(()=>{C()},[C]);return i.useEffect(()=>{w.current||(C(),a(!1),w.current=!0)},[C]),{remarks:e,isLoading:b,updateVersion:h,setWalletRemark:p,removeWalletRemark:R,getWalletRemark:_,getWalletRemarkOrNull:L,hasRemark:T,getAllRemarks:$,clearAllRemarks:I,loadRemarks:K,updateServerRemarks:M}};export{xa as P,za as u};
