var x=s=>{throw TypeError(s)};var M=(s,t,e)=>t.has(s)||x("Cannot "+e);var i=(s,t,e)=>(M(s,t,"read from private field"),e?e.call(s):t.get(s)),v=(s,t,e)=>t.has(s)?x("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(s):t.set(s,e),f=(s,t,e,r)=>(M(s,t,"write to private field"),r?r.call(s,e):t.set(s,e),e),m=(s,t,e)=>(M(s,t,"access private method"),e);import{a$ as D,b0 as H,b1 as w,b2 as $,b3 as I,au as A,r as o,b4 as U,b5 as k,I as K,_ as z}from"./index-CW-Whzws.js";var l,c,a,n,h,y,R,S,q=(S=class extends D{constructor(t,e){super();v(this,h);v(this,l);v(this,c);v(this,a);v(this,n);f(this,l,t),this.setOptions(e),this.bindMethods(),m(this,h,y).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){var r;const e=this.options;this.options=i(this,l).defaultMutationOptions(t),H(this.options,e)||i(this,l).getMutationCache().notify({type:"observerOptionsUpdated",mutation:i(this,a),observer:this}),e!=null&&e.mutationKey&&this.options.mutationKey&&w(e.mutationKey)!==w(this.options.mutationKey)?this.reset():((r=i(this,a))==null?void 0:r.state.status)==="pending"&&i(this,a).setOptions(this.options)}onUnsubscribe(){var t;this.hasListeners()||(t=i(this,a))==null||t.removeObserver(this)}onMutationUpdate(t){m(this,h,y).call(this),m(this,h,R).call(this,t)}getCurrentResult(){return i(this,c)}reset(){var t;(t=i(this,a))==null||t.removeObserver(this),f(this,a,void 0),m(this,h,y).call(this),m(this,h,R).call(this)}mutate(t,e){var r;return f(this,n,e),(r=i(this,a))==null||r.removeObserver(this),f(this,a,i(this,l).getMutationCache().build(i(this,l),this.options)),i(this,a).addObserver(this),i(this,a).execute(t)}},l=new WeakMap,c=new WeakMap,a=new WeakMap,n=new WeakMap,h=new WeakSet,y=function(){var e;const t=((e=i(this,a))==null?void 0:e.state)??$();f(this,c,{...t,isPending:t.status==="pending",isSuccess:t.status==="success",isError:t.status==="error",isIdle:t.status==="idle",mutate:this.mutate,reset:this.reset})},R=function(t){I.batch(()=>{var e,r,u,p,d,O,C,E;if(i(this,n)&&this.hasListeners()){const b=i(this,c).variables,g=i(this,c).context;(t==null?void 0:t.type)==="success"?((r=(e=i(this,n)).onSuccess)==null||r.call(e,t.data,b,g),(p=(u=i(this,n)).onSettled)==null||p.call(u,t.data,null,b,g)):(t==null?void 0:t.type)==="error"&&((O=(d=i(this,n)).onError)==null||O.call(d,t.error,b,g),(E=(C=i(this,n)).onSettled)==null||E.call(C,void 0,t.error,b,g))}this.listeners.forEach(b=>{b(i(this,c))})})},S);function T(s,t){const e=A(),[r]=o.useState(()=>new q(e,s));o.useEffect(()=>{r.setOptions(s)},[r,s]);const u=o.useSyncExternalStore(o.useCallback(d=>r.subscribe(I.batchCalls(d)),[r]),()=>r.getCurrentResult(),()=>r.getCurrentResult()),p=o.useCallback((d,O)=>{r.mutate(d,O).catch(U)},[r]);if(u.error&&k(r.options.throwOnError,[u.error]))throw u.error;return{...u,mutate:p,mutateAsync:u.mutate}}var B={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"}}]},name:"delete",theme:"outlined"},L=function(t,e){return o.createElement(K,z({},t,{ref:e,icon:B}))},F=o.forwardRef(L),_={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M909.1 209.3l-56.4 44.1C775.8 155.1 656.2 92 521.9 92 290 92 102.3 279.5 102 511.5 101.7 743.7 289.8 932 521.9 932c181.3 0 335.8-115 394.6-276.1 1.5-4.2-.7-8.9-4.9-10.3l-56.7-19.5a8 8 0 00-10.1 4.8c-1.8 5-3.8 10-5.9 14.9-17.3 41-42.1 77.8-73.7 109.4A344.77 344.77 0 01655.9 829c-42.3 17.9-87.4 27-133.8 27-46.5 0-91.5-9.1-133.8-27A341.5 341.5 0 01279 755.2a342.16 342.16 0 01-73.7-109.4c-17.9-42.4-27-87.4-27-133.9s9.1-91.5 27-133.9c17.3-41 42.1-77.8 73.7-109.4 31.6-31.6 68.4-56.4 109.3-73.8 42.3-17.9 87.4-27 133.8-27 46.5 0 91.5 9.1 133.8 27a341.5 341.5 0 01109.3 73.8c9.9 9.9 19.2 20.4 27.8 31.4l-60.2 47a8 8 0 003 14.1l175.6 43c5 1.2 9.9-2.6 9.9-7.7l.8-180.9c-.1-6.6-7.8-10.3-13-6.2z"}}]},name:"reload",theme:"outlined"},j=function(t,e){return o.createElement(K,z({},t,{ref:e,icon:_}))},G=o.forwardRef(j);export{F as R,G as a,T as u};
