{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/authprovider.tsx", "../../src/components/protectedroute.tsx", "../../src/components/realtimetradelist.tsx", "../../src/components/solpricedisplay.tsx", "../../src/components/layout/mainlayout.tsx", "../../src/components/walletconfig/autosuspendform.tsx", "../../src/components/walletconfig/batchimportmodal.tsx", "../../src/components/walletconfig/followmodeform.tsx", "../../src/components/walletconfig/strategyform.tsx", "../../src/components/walletconfig/templatemodal.tsx", "../../src/components/walletconfig/timeinputwithunit.tsx", "../../src/components/walletconfig/walletconfigtable.tsx", "../../src/components/walletconfig/walletformmodal.tsx", "../../src/components/walletconfig/index.ts", "../../src/hooks/useauth.ts", "../../src/hooks/usesolprice.ts", "../../src/hooks/usewalletremarks.ts", "../../src/hooks/usewallettemplates.ts", "../../src/pages/dashboard.tsx", "../../src/pages/login.tsx", "../../src/pages/logs.tsx", "../../src/pages/realtimetrades.tsx", "../../src/pages/walletconfig.tsx", "../../src/services/api.ts", "../../src/store/tradestore.ts", "../../src/types/api.ts", "../../src/types/index.ts", "../../src/types/template.ts", "../../src/utils/priceutils.ts"], "errors": true, "version": "5.8.3"}