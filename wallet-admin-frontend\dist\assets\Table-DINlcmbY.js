import{w as Et,r as l,D as rt,ae as Bn,x as Se,c as J,t as B,_ as pe,ai as z,b as ve,g as rn,m as on,e as V,f as dt,bt as an,C as ut,J as eo,a2 as to,a0 as Vt,a3 as no,aq as ro,bu as oo,bf as ca,i as ao,G as it,v as gt,ag as Ln,k as ce,aL as lo,bv as mr,I as ot,y as ht,bw as da,o as bt,bx as jt,aX as gr,by as io,aO as _n,bz as Hn,aQ as ua,bA as hr,ah as fa,aD as zn,bB as br,bC as yr,bD as va,aT as Fn,bE as so,bF as pa,aA as ma,bc as ga,bG as ha,s as ba,bH as ue,ao as mt,q as ya,aw as xa,al as Ca,ar as Sa,d as xr,az as wa,bo as co,B as Cr,bI as Ea,bJ as $a,ak as Sr,bK as ka,bL as Na,bn as Ra,L as Ht,l as Ia,aa as Oa,S as Ka}from"./index-CW-Whzws.js";import{p as At,c as Pa}from"./index-BVbup1Oj.js";import{L as uo,R as Ta,m as fo,E as wr,D as Da}from"./index-pOBwrJ9T.js";import{P as Ma}from"./useWalletRemarks-BeWTBa_i.js";import{I as Ba}from"./Input-Cr3didPW.js";function En(e){return e!=null&&e===e.window}const La=e=>{var t,r;if(typeof window>"u")return 0;let n=0;return En(e)?n=e.pageYOffset:e instanceof Document?n=e.documentElement.scrollTop:(e instanceof HTMLElement||e)&&(n=e.scrollTop),e&&!En(e)&&typeof n!="number"&&(n=(r=((t=e.ownerDocument)!==null&&t!==void 0?t:e).documentElement)===null||r===void 0?void 0:r.scrollTop),n};function _a(e,t,r,n){const o=r-t;return e/=n/2,e<1?o/2*e*e*e+t:o/2*((e-=2)*e*e+2)+t}function Ha(e,t={}){const{getContainer:r=()=>window,callback:n,duration:o=450}=t,a=r(),d=La(a),i=Date.now(),c=()=>{const v=Date.now()-i,u=_a(v>o?o:v,d,e,o);En(a)?a.scrollTo(window.pageXOffset,u):a instanceof Document||a.constructor.name==="HTMLDocument"?a.documentElement.scrollTop=u:a.scrollTop=u,v<o?Et(c):typeof n=="function"&&n()};Et(c)}const vo=l.createContext(null),za=vo.Provider,po=l.createContext(null),Fa=po.Provider;var ja=["prefixCls","className","style","checked","disabled","defaultChecked","type","title","onChange"],mo=l.forwardRef(function(e,t){var r=e.prefixCls,n=r===void 0?"rc-checkbox":r,o=e.className,a=e.style,d=e.checked,i=e.disabled,c=e.defaultChecked,s=c===void 0?!1:c,v=e.type,u=v===void 0?"checkbox":v,p=e.title,f=e.onChange,m=rt(e,ja),y=l.useRef(null),g=l.useRef(null),h=Bn(s,{value:d}),C=Se(h,2),x=C[0],w=C[1];l.useImperativeHandle(t,function(){return{focus:function(R){var b;(b=y.current)===null||b===void 0||b.focus(R)},blur:function(){var R;(R=y.current)===null||R===void 0||R.blur()},input:y.current,nativeElement:g.current}});var E=J(n,o,B(B({},"".concat(n,"-checked"),x),"".concat(n,"-disabled"),i)),k=function(R){i||("checked"in e||w(R.target.checked),f==null||f({target:z(z({},e),{},{type:u,checked:R.target.checked}),stopPropagation:function(){R.stopPropagation()},preventDefault:function(){R.preventDefault()},nativeEvent:R.nativeEvent}))};return l.createElement("span",{className:E,title:p,style:a,ref:g},l.createElement("input",pe({},m,{className:"".concat(n,"-input"),ref:y,onChange:k,disabled:i,checked:!!x,type:u})),l.createElement("span",{className:"".concat(n,"-inner")}))});function go(e){const t=ve.useRef(null),r=()=>{Et.cancel(t.current),t.current=null};return[()=>{r(),t.current=Et(()=>{t.current=null})},a=>{t.current&&(a.stopPropagation(),r()),e==null||e(a)}]}const Aa=e=>{const{componentCls:t,antCls:r}=e,n=`${t}-group`;return{[n]:Object.assign(Object.assign({},dt(e)),{display:"inline-block",fontSize:0,[`&${n}-rtl`]:{direction:"rtl"},[`&${n}-block`]:{display:"flex"},[`${r}-badge ${r}-badge-count`]:{zIndex:1},[`> ${r}-badge:not(:first-child) > ${r}-button-wrapper`]:{borderInlineStart:"none"}})}},Wa=e=>{const{componentCls:t,wrapperMarginInlineEnd:r,colorPrimary:n,radioSize:o,motionDurationSlow:a,motionDurationMid:d,motionEaseInOutCirc:i,colorBgContainer:c,colorBorder:s,lineWidth:v,colorBgContainerDisabled:u,colorTextDisabled:p,paddingXS:f,dotColorDisabled:m,lineType:y,radioColor:g,radioBgColor:h,calc:C}=e,x=`${t}-inner`,E=C(o).sub(C(4).mul(2)),k=C(1).mul(o).equal({unit:!0});return{[`${t}-wrapper`]:Object.assign(Object.assign({},dt(e)),{display:"inline-flex",alignItems:"baseline",marginInlineStart:0,marginInlineEnd:r,cursor:"pointer","&:last-child":{marginInlineEnd:0},[`&${t}-wrapper-rtl`]:{direction:"rtl"},"&-disabled":{cursor:"not-allowed",color:e.colorTextDisabled},"&::after":{display:"inline-block",width:0,overflow:"hidden",content:'"\\a0"'},"&-block":{flex:1,justifyContent:"center"},[`${t}-checked::after`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,width:"100%",height:"100%",border:`${V(v)} ${y} ${n}`,borderRadius:"50%",visibility:"hidden",opacity:0,content:'""'},[t]:Object.assign(Object.assign({},dt(e)),{position:"relative",display:"inline-block",outline:"none",cursor:"pointer",alignSelf:"center",borderRadius:"50%"}),[`${t}-wrapper:hover &,
        &:hover ${x}`]:{borderColor:n},[`${t}-input:focus-visible + ${x}`]:Object.assign({},an(e)),[`${t}:hover::after, ${t}-wrapper:hover &::after`]:{visibility:"visible"},[`${t}-inner`]:{"&::after":{boxSizing:"border-box",position:"absolute",insetBlockStart:"50%",insetInlineStart:"50%",display:"block",width:k,height:k,marginBlockStart:C(1).mul(o).div(-2).equal({unit:!0}),marginInlineStart:C(1).mul(o).div(-2).equal({unit:!0}),backgroundColor:g,borderBlockStart:0,borderInlineStart:0,borderRadius:k,transform:"scale(0)",opacity:0,transition:`all ${a} ${i}`,content:'""'},boxSizing:"border-box",position:"relative",insetBlockStart:0,insetInlineStart:0,display:"block",width:k,height:k,backgroundColor:c,borderColor:s,borderStyle:"solid",borderWidth:v,borderRadius:"50%",transition:`all ${d}`},[`${t}-input`]:{position:"absolute",inset:0,zIndex:1,cursor:"pointer",opacity:0},[`${t}-checked`]:{[x]:{borderColor:n,backgroundColor:h,"&::after":{transform:`scale(${e.calc(e.dotSize).div(o).equal()})`,opacity:1,transition:`all ${a} ${i}`}}},[`${t}-disabled`]:{cursor:"not-allowed",[x]:{backgroundColor:u,borderColor:s,cursor:"not-allowed","&::after":{backgroundColor:m}},[`${t}-input`]:{cursor:"not-allowed"},[`${t}-disabled + span`]:{color:p,cursor:"not-allowed"},[`&${t}-checked`]:{[x]:{"&::after":{transform:`scale(${C(E).div(o).equal()})`}}}},[`span${t} + *`]:{paddingInlineStart:f,paddingInlineEnd:f}})}},Va=e=>{const{buttonColor:t,controlHeight:r,componentCls:n,lineWidth:o,lineType:a,colorBorder:d,motionDurationSlow:i,motionDurationMid:c,buttonPaddingInline:s,fontSize:v,buttonBg:u,fontSizeLG:p,controlHeightLG:f,controlHeightSM:m,paddingXS:y,borderRadius:g,borderRadiusSM:h,borderRadiusLG:C,buttonCheckedBg:x,buttonSolidCheckedColor:w,colorTextDisabled:E,colorBgContainerDisabled:k,buttonCheckedBgDisabled:N,buttonCheckedColorDisabled:R,colorPrimary:b,colorPrimaryHover:K,colorPrimaryActive:I,buttonSolidCheckedBg:D,buttonSolidCheckedHoverBg:P,buttonSolidCheckedActiveBg:$,calc:S}=e;return{[`${n}-button-wrapper`]:{position:"relative",display:"inline-block",height:r,margin:0,paddingInline:s,paddingBlock:0,color:t,fontSize:v,lineHeight:V(S(r).sub(S(o).mul(2)).equal()),background:u,border:`${V(o)} ${a} ${d}`,borderBlockStartWidth:S(o).add(.02).equal(),borderInlineStartWidth:0,borderInlineEndWidth:o,cursor:"pointer",transition:[`color ${c}`,`background ${c}`,`box-shadow ${c}`].join(","),a:{color:t},[`> ${n}-button`]:{position:"absolute",insetBlockStart:0,insetInlineStart:0,zIndex:-1,width:"100%",height:"100%"},"&:not(:first-child)":{"&::before":{position:"absolute",insetBlockStart:S(o).mul(-1).equal(),insetInlineStart:S(o).mul(-1).equal(),display:"block",boxSizing:"content-box",width:1,height:"100%",paddingBlock:o,paddingInline:0,backgroundColor:d,transition:`background-color ${i}`,content:'""'}},"&:first-child":{borderInlineStart:`${V(o)} ${a} ${d}`,borderStartStartRadius:g,borderEndStartRadius:g},"&:last-child":{borderStartEndRadius:g,borderEndEndRadius:g},"&:first-child:last-child":{borderRadius:g},[`${n}-group-large &`]:{height:f,fontSize:p,lineHeight:V(S(f).sub(S(o).mul(2)).equal()),"&:first-child":{borderStartStartRadius:C,borderEndStartRadius:C},"&:last-child":{borderStartEndRadius:C,borderEndEndRadius:C}},[`${n}-group-small &`]:{height:m,paddingInline:S(y).sub(o).equal(),paddingBlock:0,lineHeight:V(S(m).sub(S(o).mul(2)).equal()),"&:first-child":{borderStartStartRadius:h,borderEndStartRadius:h},"&:last-child":{borderStartEndRadius:h,borderEndEndRadius:h}},"&:hover":{position:"relative",color:b},"&:has(:focus-visible)":Object.assign({},an(e)),[`${n}-inner, input[type='checkbox'], input[type='radio']`]:{width:0,height:0,opacity:0,pointerEvents:"none"},[`&-checked:not(${n}-button-wrapper-disabled)`]:{zIndex:1,color:b,background:x,borderColor:b,"&::before":{backgroundColor:b},"&:first-child":{borderColor:b},"&:hover":{color:K,borderColor:K,"&::before":{backgroundColor:K}},"&:active":{color:I,borderColor:I,"&::before":{backgroundColor:I}}},[`${n}-group-solid &-checked:not(${n}-button-wrapper-disabled)`]:{color:w,background:D,borderColor:D,"&:hover":{color:w,background:P,borderColor:P},"&:active":{color:w,background:$,borderColor:$}},"&-disabled":{color:E,backgroundColor:k,borderColor:d,cursor:"not-allowed","&:first-child, &:hover":{color:E,backgroundColor:k,borderColor:d}},[`&-disabled${n}-button-wrapper-checked`]:{color:R,backgroundColor:N,borderColor:d,boxShadow:"none"},"&-block":{flex:1,textAlign:"center"}}}},qa=e=>{const{wireframe:t,padding:r,marginXS:n,lineWidth:o,fontSizeLG:a,colorText:d,colorBgContainer:i,colorTextDisabled:c,controlItemBgActiveDisabled:s,colorTextLightSolid:v,colorPrimary:u,colorPrimaryHover:p,colorPrimaryActive:f,colorWhite:m}=e,y=4,g=a,h=t?g-y*2:g-(y+o)*2;return{radioSize:g,dotSize:h,dotColorDisabled:c,buttonSolidCheckedColor:v,buttonSolidCheckedBg:u,buttonSolidCheckedHoverBg:p,buttonSolidCheckedActiveBg:f,buttonBg:i,buttonCheckedBg:i,buttonColor:d,buttonCheckedBgDisabled:s,buttonCheckedColorDisabled:c,buttonPaddingInline:r-o,wrapperMarginInlineEnd:n,radioColor:t?u:m,radioBgColor:t?i:u}},ho=rn("Radio",e=>{const{controlOutline:t,controlOutlineWidth:r}=e,n=`0 0 0 ${V(r)} ${t}`,a=on(e,{radioFocusShadow:n,radioButtonFocusShadow:n});return[Aa(a),Wa(a),Va(a)]},qa,{unitless:{radioSize:!0,dotSize:!0}});var Ga=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Xa=(e,t)=>{var r,n;const o=l.useContext(vo),a=l.useContext(po),{getPrefixCls:d,direction:i,radio:c}=l.useContext(ut),s=l.useRef(null),v=eo(t,s),{isFormItemInput:u}=l.useContext(to),p=O=>{var T,_;(T=e.onChange)===null||T===void 0||T.call(e,O),(_=o==null?void 0:o.onChange)===null||_===void 0||_.call(o,O)},{prefixCls:f,className:m,rootClassName:y,children:g,style:h,title:C}=e,x=Ga(e,["prefixCls","className","rootClassName","children","style","title"]),w=d("radio",f),E=((o==null?void 0:o.optionType)||a)==="button",k=E?`${w}-button`:w,N=Vt(w),[R,b,K]=ho(w,N),I=Object.assign({},x),D=l.useContext(no);o&&(I.name=o.name,I.onChange=p,I.checked=e.value===o.value,I.disabled=(r=I.disabled)!==null&&r!==void 0?r:o.disabled),I.disabled=(n=I.disabled)!==null&&n!==void 0?n:D;const P=J(`${k}-wrapper`,{[`${k}-wrapper-checked`]:I.checked,[`${k}-wrapper-disabled`]:I.disabled,[`${k}-wrapper-rtl`]:i==="rtl",[`${k}-wrapper-in-form-item`]:u,[`${k}-wrapper-block`]:!!(o!=null&&o.block)},c==null?void 0:c.className,m,y,b,K,N),[$,S]=go(I.onClick);return R(l.createElement(ro,{component:"Radio",disabled:I.disabled},l.createElement("label",{className:P,style:Object.assign(Object.assign({},c==null?void 0:c.style),h),onMouseEnter:e.onMouseEnter,onMouseLeave:e.onMouseLeave,title:C,onClick:$},l.createElement(mo,Object.assign({},I,{className:J(I.className,{[oo]:!E}),type:"radio",prefixCls:k,ref:v,onClick:S})),g!==void 0?l.createElement("span",{className:`${k}-label`},g):null)))},tn=l.forwardRef(Xa),Ua=l.forwardRef((e,t)=>{const{getPrefixCls:r,direction:n}=l.useContext(ut),o=ca(),{prefixCls:a,className:d,rootClassName:i,options:c,buttonStyle:s="outline",disabled:v,children:u,size:p,style:f,id:m,optionType:y,name:g=o,defaultValue:h,value:C,block:x=!1,onChange:w,onMouseEnter:E,onMouseLeave:k,onFocus:N,onBlur:R}=e,[b,K]=Bn(h,{value:C}),I=l.useCallback(q=>{const ee=b,be=q.target.value;"value"in e||K(be),be!==ee&&(w==null||w(q))},[b,K,w]),D=r("radio",a),P=`${D}-group`,$=Vt(D),[S,O,T]=ho(D,$);let _=u;c&&c.length>0&&(_=c.map(q=>typeof q=="string"||typeof q=="number"?l.createElement(tn,{key:q.toString(),prefixCls:D,disabled:v,value:q,checked:b===q},q):l.createElement(tn,{key:`radio-group-value-options-${q.value}`,prefixCls:D,disabled:q.disabled||v,value:q.value,checked:b===q.value,title:q.title,style:q.style,className:q.className,id:q.id,required:q.required},q.label)));const M=ao(p),A=J(P,`${P}-${s}`,{[`${P}-${M}`]:M,[`${P}-rtl`]:n==="rtl",[`${P}-block`]:x},d,i,O,T,$),Y=l.useMemo(()=>({onChange:I,value:b,disabled:v,name:g,optionType:y,block:x}),[I,b,v,g,y,x]);return S(l.createElement("div",Object.assign({},At(e,{aria:!0,data:!0}),{className:A,style:f,onMouseEnter:E,onMouseLeave:k,onFocus:N,onBlur:R,id:m,ref:t}),l.createElement(za,{value:Y},_)))}),Ya=l.memo(Ua);var Ja=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Qa=(e,t)=>{const{getPrefixCls:r}=l.useContext(ut),{prefixCls:n}=e,o=Ja(e,["prefixCls"]),a=r("radio",n);return l.createElement(Fa,{value:"button"},l.createElement(tn,Object.assign({prefixCls:a},o,{type:"radio",ref:t})))},Za=l.forwardRef(Qa),qt=tn;qt.Button=Za;qt.Group=Ya;qt.__ANT_RADIO=!0;function Je(e,t){return e[t]}var el=["children"];function bo(e,t){return"".concat(e,"-").concat(t)}function tl(e){return e&&e.type&&e.type.isTreeNode}function Gt(e,t){return e??t}function Pt(e){var t=e||{},r=t.title,n=t._title,o=t.key,a=t.children,d=r||"title";return{title:d,_title:n||[d],key:o||"key",children:a||"children"}}function yo(e){function t(r){var n=lo(r);return n.map(function(o){if(!tl(o))return gt(!o,"Tree/TreeNode can only accept TreeNode as children."),null;var a=o.key,d=o.props,i=d.children,c=rt(d,el),s=z({key:a},c),v=t(i);return v.length&&(s.children=v),s}).filter(function(o){return o})}return t(e)}function bn(e,t,r){var n=Pt(r),o=n._title,a=n.key,d=n.children,i=new Set(t===!0?[]:t),c=[];function s(v){var u=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;return v.map(function(p,f){for(var m=bo(u?u.pos:"0",f),y=Gt(p[a],m),g,h=0;h<o.length;h+=1){var C=o[h];if(p[C]!==void 0){g=p[C];break}}var x=Object.assign(Ln(p,[].concat(ce(o),[a,d])),{title:g,key:y,parent:u,pos:m,children:null,data:p,isStart:[].concat(ce(u?u.isStart:[]),[f===0]),isEnd:[].concat(ce(u?u.isEnd:[]),[f===v.length-1])});return c.push(x),t===!0||i.has(y)?x.children=s(p[d]||[],x):x.children=[],x})}return s(e),c}function nl(e,t,r){var n={};it(r)==="object"?n=r:n={externalGetKey:r},n=n||{};var o=n,a=o.childrenPropName,d=o.externalGetKey,i=o.fieldNames,c=Pt(i),s=c.key,v=c.children,u=a||v,p;d?typeof d=="string"?p=function(y){return y[d]}:typeof d=="function"&&(p=function(y){return d(y)}):p=function(y,g){return Gt(y[s],g)};function f(m,y,g,h){var C=m?m[u]:e,x=m?bo(g.pos,y):"0",w=m?[].concat(ce(h),[m]):[];if(m){var E=p(m,x),k={node:m,index:y,pos:x,key:E,parentPos:g.node?g.pos:null,level:g.level+1,nodes:w};t(k)}C&&C.forEach(function(N,R){f(N,R,{node:m,pos:x,level:g?g.level+1:-1},w)})}f(null)}function jn(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=t.initWrapper,n=t.processEntity,o=t.onProcessFinished,a=t.externalGetKey,d=t.childrenPropName,i=t.fieldNames,c=arguments.length>2?arguments[2]:void 0,s=a||c,v={},u={},p={posEntities:v,keyEntities:u};return r&&(p=r(p)||p),nl(e,function(f){var m=f.node,y=f.index,g=f.pos,h=f.key,C=f.parentPos,x=f.level,w=f.nodes,E={node:m,nodes:w,index:y,key:h,pos:g,level:x},k=Gt(h,g);v[g]=E,u[k]=E,E.parent=v[C],E.parent&&(E.parent.children=E.parent.children||[],E.parent.children.push(E)),n&&n(E,p)},{externalGetKey:s,childrenPropName:d,fieldNames:i}),o&&o(p),p}function zt(e,t){var r=t.expandedKeys,n=t.selectedKeys,o=t.loadedKeys,a=t.loadingKeys,d=t.checkedKeys,i=t.halfCheckedKeys,c=t.dragOverNodeKey,s=t.dropPosition,v=t.keyEntities,u=Je(v,e),p={eventKey:e,expanded:r.indexOf(e)!==-1,selected:n.indexOf(e)!==-1,loaded:o.indexOf(e)!==-1,loading:a.indexOf(e)!==-1,checked:d.indexOf(e)!==-1,halfChecked:i.indexOf(e)!==-1,pos:String(u?u.pos:""),dragOver:c===e&&s===0,dragOverGapTop:c===e&&s===-1,dragOverGapBottom:c===e&&s===1};return p}function Me(e){var t=e.data,r=e.expanded,n=e.selected,o=e.checked,a=e.loaded,d=e.loading,i=e.halfChecked,c=e.dragOver,s=e.dragOverGapTop,v=e.dragOverGapBottom,u=e.pos,p=e.active,f=e.eventKey,m=z(z({},t),{},{expanded:r,selected:n,checked:o,loaded:a,loading:d,halfChecked:i,dragOver:c,dragOverGapTop:s,dragOverGapBottom:v,pos:u,active:p,key:f});return"props"in m||Object.defineProperty(m,"props",{get:function(){return gt(!1,"Second param return from event is node data instead of TreeNode instance. Please read value directly instead of reading from `props`."),e}}),m}function xo(e,t){var r=new Set;return e.forEach(function(n){t.has(n)||r.add(n)}),r}function rl(e){var t=e||{},r=t.disabled,n=t.disableCheckbox,o=t.checkable;return!!(r||n)||o===!1}function ol(e,t,r,n){for(var o=new Set(e),a=new Set,d=0;d<=r;d+=1){var i=t.get(d)||new Set;i.forEach(function(u){var p=u.key,f=u.node,m=u.children,y=m===void 0?[]:m;o.has(p)&&!n(f)&&y.filter(function(g){return!n(g.node)}).forEach(function(g){o.add(g.key)})})}for(var c=new Set,s=r;s>=0;s-=1){var v=t.get(s)||new Set;v.forEach(function(u){var p=u.parent,f=u.node;if(!(n(f)||!u.parent||c.has(u.parent.key))){if(n(u.parent.node)){c.add(p.key);return}var m=!0,y=!1;(p.children||[]).filter(function(g){return!n(g.node)}).forEach(function(g){var h=g.key,C=o.has(h);m&&!C&&(m=!1),!y&&(C||a.has(h))&&(y=!0)}),m&&o.add(p.key),y&&a.add(p.key),c.add(p.key)}})}return{checkedKeys:Array.from(o),halfCheckedKeys:Array.from(xo(a,o))}}function al(e,t,r,n,o){for(var a=new Set(e),d=new Set(t),i=0;i<=n;i+=1){var c=r.get(i)||new Set;c.forEach(function(p){var f=p.key,m=p.node,y=p.children,g=y===void 0?[]:y;!a.has(f)&&!d.has(f)&&!o(m)&&g.filter(function(h){return!o(h.node)}).forEach(function(h){a.delete(h.key)})})}d=new Set;for(var s=new Set,v=n;v>=0;v-=1){var u=r.get(v)||new Set;u.forEach(function(p){var f=p.parent,m=p.node;if(!(o(m)||!p.parent||s.has(p.parent.key))){if(o(p.parent.node)){s.add(f.key);return}var y=!0,g=!1;(f.children||[]).filter(function(h){return!o(h.node)}).forEach(function(h){var C=h.key,x=a.has(C);y&&!x&&(y=!1),!g&&(x||d.has(C))&&(g=!0)}),y||a.delete(f.key),g&&d.add(f.key),s.add(f.key)}})}return{checkedKeys:Array.from(a),halfCheckedKeys:Array.from(xo(d,a))}}function Ot(e,t,r,n){var o=[],a;n?a=n:a=rl;var d=new Set(e.filter(function(v){var u=!!Je(r,v);return u||o.push(v),u})),i=new Map,c=0;Object.keys(r).forEach(function(v){var u=r[v],p=u.level,f=i.get(p);f||(f=new Set,i.set(p,f)),f.add(u),c=Math.max(c,p)}),gt(!o.length,"Tree missing follow keys: ".concat(o.slice(0,100).map(function(v){return"'".concat(v,"'")}).join(", ")));var s;return t===!0?s=ol(d,i,c,a):s=al(d,t.halfCheckedKeys,i,c,a),s}const ll=e=>{const{checkboxCls:t}=e,r=`${t}-wrapper`;return[{[`${t}-group`]:Object.assign(Object.assign({},dt(e)),{display:"inline-flex",flexWrap:"wrap",columnGap:e.marginXS,[`> ${e.antCls}-row`]:{flex:1}}),[r]:Object.assign(Object.assign({},dt(e)),{display:"inline-flex",alignItems:"baseline",cursor:"pointer","&:after":{display:"inline-block",width:0,overflow:"hidden",content:"'\\a0'"},[`& + ${r}`]:{marginInlineStart:0},[`&${r}-in-form-item`]:{'input[type="checkbox"]':{width:14,height:14}}}),[t]:Object.assign(Object.assign({},dt(e)),{position:"relative",whiteSpace:"nowrap",lineHeight:1,cursor:"pointer",borderRadius:e.borderRadiusSM,alignSelf:"center",[`${t}-input`]:{position:"absolute",inset:0,zIndex:1,cursor:"pointer",opacity:0,margin:0,[`&:focus-visible + ${t}-inner`]:Object.assign({},an(e))},[`${t}-inner`]:{boxSizing:"border-box",display:"block",width:e.checkboxSize,height:e.checkboxSize,direction:"ltr",backgroundColor:e.colorBgContainer,border:`${V(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,borderCollapse:"separate",transition:`all ${e.motionDurationSlow}`,"&:after":{boxSizing:"border-box",position:"absolute",top:"50%",insetInlineStart:"25%",display:"table",width:e.calc(e.checkboxSize).div(14).mul(5).equal(),height:e.calc(e.checkboxSize).div(14).mul(8).equal(),border:`${V(e.lineWidthBold)} solid ${e.colorWhite}`,borderTop:0,borderInlineStart:0,transform:"rotate(45deg) scale(0) translate(-50%,-50%)",opacity:0,content:'""',transition:`all ${e.motionDurationFast} ${e.motionEaseInBack}, opacity ${e.motionDurationFast}`}},"& + span":{paddingInlineStart:e.paddingXS,paddingInlineEnd:e.paddingXS}})},{[`
        ${r}:not(${r}-disabled),
        ${t}:not(${t}-disabled)
      `]:{[`&:hover ${t}-inner`]:{borderColor:e.colorPrimary}},[`${r}:not(${r}-disabled)`]:{[`&:hover ${t}-checked:not(${t}-disabled) ${t}-inner`]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"},[`&:hover ${t}-checked:not(${t}-disabled):after`]:{borderColor:e.colorPrimaryHover}}},{[`${t}-checked`]:{[`${t}-inner`]:{backgroundColor:e.colorPrimary,borderColor:e.colorPrimary,"&:after":{opacity:1,transform:"rotate(45deg) scale(1) translate(-50%,-50%)",transition:`all ${e.motionDurationMid} ${e.motionEaseOutBack} ${e.motionDurationFast}`}}},[`
        ${r}-checked:not(${r}-disabled),
        ${t}-checked:not(${t}-disabled)
      `]:{[`&:hover ${t}-inner`]:{backgroundColor:e.colorPrimaryHover,borderColor:"transparent"}}},{[t]:{"&-indeterminate":{"&":{[`${t}-inner`]:{backgroundColor:`${e.colorBgContainer}`,borderColor:`${e.colorBorder}`,"&:after":{top:"50%",insetInlineStart:"50%",width:e.calc(e.fontSizeLG).div(2).equal(),height:e.calc(e.fontSizeLG).div(2).equal(),backgroundColor:e.colorPrimary,border:0,transform:"translate(-50%, -50%) scale(1)",opacity:1,content:'""'}},[`&:hover ${t}-inner`]:{backgroundColor:`${e.colorBgContainer}`,borderColor:`${e.colorPrimary}`}}}}},{[`${r}-disabled`]:{cursor:"not-allowed"},[`${t}-disabled`]:{[`&, ${t}-input`]:{cursor:"not-allowed",pointerEvents:"none"},[`${t}-inner`]:{background:e.colorBgContainerDisabled,borderColor:e.colorBorder,"&:after":{borderColor:e.colorTextDisabled}},"&:after":{display:"none"},"& + span":{color:e.colorTextDisabled},[`&${t}-indeterminate ${t}-inner::after`]:{background:e.colorTextDisabled}}}]};function Co(e,t){const r=on(t,{checkboxCls:`.${e}`,checkboxSize:t.controlInteractiveSize});return[ll(r)]}const So=rn("Checkbox",(e,{prefixCls:t})=>[Co(t,e)]),wo=ve.createContext(null);var il=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const sl=(e,t)=>{var r;const{prefixCls:n,className:o,rootClassName:a,children:d,indeterminate:i=!1,style:c,onMouseEnter:s,onMouseLeave:v,skipGroup:u=!1,disabled:p}=e,f=il(e,["prefixCls","className","rootClassName","children","indeterminate","style","onMouseEnter","onMouseLeave","skipGroup","disabled"]),{getPrefixCls:m,direction:y,checkbox:g}=l.useContext(ut),h=l.useContext(wo),{isFormItemInput:C}=l.useContext(to),x=l.useContext(no),w=(r=(h==null?void 0:h.disabled)||p)!==null&&r!==void 0?r:x,E=l.useRef(f.value),k=l.useRef(null),N=eo(t,k);l.useEffect(()=>{h==null||h.registerValue(f.value)},[]),l.useEffect(()=>{if(!u)return f.value!==E.current&&(h==null||h.cancelValue(E.current),h==null||h.registerValue(f.value),E.current=f.value),()=>h==null?void 0:h.cancelValue(f.value)},[f.value]),l.useEffect(()=>{var _;!((_=k.current)===null||_===void 0)&&_.input&&(k.current.input.indeterminate=i)},[i]);const R=m("checkbox",n),b=Vt(R),[K,I,D]=So(R,b),P=Object.assign({},f);h&&!u&&(P.onChange=(..._)=>{f.onChange&&f.onChange.apply(f,_),h.toggleOption&&h.toggleOption({label:d,value:f.value})},P.name=h.name,P.checked=h.value.includes(f.value));const $=J(`${R}-wrapper`,{[`${R}-rtl`]:y==="rtl",[`${R}-wrapper-checked`]:P.checked,[`${R}-wrapper-disabled`]:w,[`${R}-wrapper-in-form-item`]:C},g==null?void 0:g.className,o,a,D,b,I),S=J({[`${R}-indeterminate`]:i},oo,I),[O,T]=go(P.onClick);return K(l.createElement(ro,{component:"Checkbox",disabled:w},l.createElement("label",{className:$,style:Object.assign(Object.assign({},g==null?void 0:g.style),c),onMouseEnter:s,onMouseLeave:v,onClick:O},l.createElement(mo,Object.assign({},P,{onClick:T,prefixCls:R,className:S,disabled:w,ref:N})),d!=null&&l.createElement("span",{className:`${R}-label`},d))))},Eo=l.forwardRef(sl);var cl=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const dl=l.forwardRef((e,t)=>{const{defaultValue:r,children:n,options:o=[],prefixCls:a,className:d,rootClassName:i,style:c,onChange:s}=e,v=cl(e,["defaultValue","children","options","prefixCls","className","rootClassName","style","onChange"]),{getPrefixCls:u,direction:p}=l.useContext(ut),[f,m]=l.useState(v.value||r||[]),[y,g]=l.useState([]);l.useEffect(()=>{"value"in v&&m(v.value||[])},[v.value]);const h=l.useMemo(()=>o.map(S=>typeof S=="string"||typeof S=="number"?{label:S,value:S}:S),[o]),C=S=>{g(O=>O.filter(T=>T!==S))},x=S=>{g(O=>[].concat(ce(O),[S]))},w=S=>{const O=f.indexOf(S.value),T=ce(f);O===-1?T.push(S.value):T.splice(O,1),"value"in v||m(T),s==null||s(T.filter(_=>y.includes(_)).sort((_,M)=>{const A=h.findIndex(q=>q.value===_),Y=h.findIndex(q=>q.value===M);return A-Y}))},E=u("checkbox",a),k=`${E}-group`,N=Vt(E),[R,b,K]=So(E,N),I=Ln(v,["value","disabled"]),D=o.length?h.map(S=>l.createElement(Eo,{prefixCls:E,key:S.value.toString(),disabled:"disabled"in S?S.disabled:v.disabled,value:S.value,checked:f.includes(S.value),onChange:S.onChange,className:J(`${k}-item`,S.className),style:S.style,title:S.title,id:S.id,required:S.required},S.label)):n,P=l.useMemo(()=>({toggleOption:w,value:f,disabled:v.disabled,name:v.name,registerValue:x,cancelValue:C}),[w,f,v.disabled,v.name,x,C]),$=J(k,{[`${k}-rtl`]:p==="rtl"},d,i,K,N,b);return R(l.createElement("div",Object.assign({className:$,style:c},I,{ref:t}),l.createElement(wo.Provider,{value:P},D)))}),Tt=Eo;Tt.Group=dl;Tt.__ANT_CHECKBOX=!0;function Er(e,t,r,n){var o=mr.unstable_batchedUpdates?function(d){mr.unstable_batchedUpdates(r,d)}:r;return e!=null&&e.addEventListener&&e.addEventListener(t,o,n),{remove:function(){e!=null&&e.removeEventListener&&e.removeEventListener(t,o,n)}}}var ul={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"filled"},fl=function(t,r){return l.createElement(ot,pe({},t,{ref:r,icon:ul}))},vl=l.forwardRef(fl),pl={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35z"}}]},name:"caret-down",theme:"outlined"},ml=function(t,r){return l.createElement(ot,pe({},t,{ref:r,icon:pl}))},gl=l.forwardRef(ml),hl={icon:{tag:"svg",attrs:{viewBox:"0 0 1024 1024",focusable:"false"},children:[{tag:"path",attrs:{d:"M858.9 689L530.5 308.2c-9.4-10.9-27.5-10.9-37 0L165.1 689c-12.2 14.2-1.2 35 18.5 35h656.8c19.7 0 30.7-20.8 18.5-35z"}}]},name:"caret-up",theme:"outlined"},bl=function(t,r){return l.createElement(ot,pe({},t,{ref:r,icon:hl}))},yl=l.forwardRef(bl),xl={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"defs",attrs:{},children:[{tag:"style",attrs:{}}]},{tag:"path",attrs:{d:"M899.1 869.6l-53-305.6H864c14.4 0 26-11.6 26-26V346c0-14.4-11.6-26-26-26H618V138c0-14.4-11.6-26-26-26H432c-14.4 0-26 11.6-26 26v182H160c-14.4 0-26 11.6-26 26v192c0 14.4 11.6 26 26 26h17.9l-53 305.6a25.95 25.95 0 0025.6 30.4h723c1.5 0 3-.1 4.4-.4a25.88 25.88 0 0021.2-30zM204 390h272V182h72v208h272v104H204V390zm468 440V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H416V674c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v156H202.8l45.1-260H776l45.1 260H672z"}}]},name:"clear",theme:"outlined"},Cl=function(t,r){return l.createElement(ot,pe({},t,{ref:r,icon:xl}))},$c=l.forwardRef(Cl),Sl={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M854.6 288.6L639.4 73.4c-6-6-14.1-9.4-22.6-9.4H192c-17.7 0-32 14.3-32 32v832c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V311.3c0-8.5-3.4-16.7-9.4-22.7zM790.2 326H602V137.8L790.2 326zm1.8 562H232V136h302v216a42 42 0 0042 42h216v494z"}}]},name:"file",theme:"outlined"},wl=function(t,r){return l.createElement(ot,pe({},t,{ref:r,icon:Sl}))},$o=l.forwardRef(wl),El={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M349 838c0 17.7 14.2 32 31.8 32h262.4c17.6 0 31.8-14.3 31.8-32V642H349v196zm531.1-684H143.9c-24.5 0-39.8 26.7-27.5 48l221.3 376h348.8l221.3-376c12.1-21.3-3.2-48-27.7-48z"}}]},name:"filter",theme:"filled"},$l=function(t,r){return l.createElement(ot,pe({},t,{ref:r,icon:El}))},kl=l.forwardRef($l),Nl={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M928 444H820V330.4c0-17.7-14.3-32-32-32H473L355.7 186.2a8.15 8.15 0 00-5.5-2.2H96c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h698c13 0 24.8-7.9 29.7-20l134-332c1.5-3.8 2.3-7.9 2.3-12 0-17.7-14.3-32-32-32zM136 256h188.5l119.6 114.4H748V444H238c-13 0-24.8 7.9-29.7 20L136 643.2V256zm635.3 512H159l103.3-256h612.4L771.3 768z"}}]},name:"folder-open",theme:"outlined"},Rl=function(t,r){return l.createElement(ot,pe({},t,{ref:r,icon:Nl}))},Il=l.forwardRef(Rl),Ol={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"}}]},name:"folder",theme:"outlined"},Kl=function(t,r){return l.createElement(ot,pe({},t,{ref:r,icon:Ol}))},Pl=l.forwardRef(Kl),Tl={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M300 276.5a56 56 0 1056-97 56 56 0 00-56 97zm0 284a56 56 0 1056-97 56 56 0 00-56 97zM640 228a56 56 0 10112 0 56 56 0 00-112 0zm0 284a56 56 0 10112 0 56 56 0 00-112 0zM300 844.5a56 56 0 1056-97 56 56 0 00-56 97zM640 796a56 56 0 10112 0 56 56 0 00-112 0z"}}]},name:"holder",theme:"outlined"},Dl=function(t,r){return l.createElement(ot,pe({},t,{ref:r,icon:Tl}))},Ml=l.forwardRef(Dl),Bl={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h368c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"minus-square",theme:"outlined"},Ll=function(t,r){return l.createElement(ot,pe({},t,{ref:r,icon:Bl}))},_l=l.forwardRef(Ll),Hl={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M328 544h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8z"}},{tag:"path",attrs:{d:"M880 112H144c-17.7 0-32 14.3-32 32v736c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V144c0-17.7-14.3-32-32-32zm-40 728H184V184h656v656z"}}]},name:"plus-square",theme:"outlined"},zl=function(t,r){return l.createElement(ot,pe({},t,{ref:r,icon:Hl}))},Fl=l.forwardRef(zl),ct={},Xt="rc-table-internal-hook";function An(e){var t=l.createContext(void 0),r=function(o){var a=o.value,d=o.children,i=l.useRef(a);i.current=a;var c=l.useState(function(){return{getValue:function(){return i.current},listeners:new Set}}),s=Se(c,1),v=s[0];return ht(function(){da.unstable_batchedUpdates(function(){v.listeners.forEach(function(u){u(a)})})},[a]),l.createElement(t.Provider,{value:v},d)};return{Context:t,Provider:r,defaultValue:e}}function ze(e,t){var r=bt(typeof t=="function"?t:function(u){if(t===void 0)return u;if(!Array.isArray(t))return u[t];var p={};return t.forEach(function(f){p[f]=u[f]}),p}),n=l.useContext(e==null?void 0:e.Context),o=n||{},a=o.listeners,d=o.getValue,i=l.useRef();i.current=r(n?d():e==null?void 0:e.defaultValue);var c=l.useState({}),s=Se(c,2),v=s[1];return ht(function(){if(!n)return;function u(p){var f=r(p);jt(i.current,f,!0)||v({})}return a.add(u),function(){a.delete(u)}},[n]),i.current}function jl(){var e=l.createContext(null);function t(){return l.useContext(e)}function r(o,a){var d=gr(o),i=function(s,v){var u=d?{ref:v}:{},p=l.useRef(0),f=l.useRef(s),m=t();return m!==null?l.createElement(o,pe({},s,u)):((!a||a(f.current,s))&&(p.current+=1),f.current=s,l.createElement(e.Provider,{value:p.current},l.createElement(o,pe({},s,u))))};return d?l.forwardRef(i):i}function n(o,a){var d=gr(o),i=function(s,v){var u=d?{ref:v}:{};return t(),l.createElement(o,pe({},s,u))};return d?l.memo(l.forwardRef(i),a):l.memo(i,a)}return{makeImmutable:r,responseImmutable:n,useImmutableMark:t}}var Wn=jl(),ko=Wn.makeImmutable,Dt=Wn.responseImmutable,Al=Wn.useImmutableMark,Xe=An(),No=l.createContext({renderWithProps:!1}),Wl="RC_TABLE_KEY";function Vl(e){return e==null?[]:Array.isArray(e)?e:[e]}function ln(e){var t=[],r={};return e.forEach(function(n){for(var o=n||{},a=o.key,d=o.dataIndex,i=a||Vl(d).join("-")||Wl;r[i];)i="".concat(i,"_next");r[i]=!0,t.push(i)}),t}function $n(e){return e!=null}function ql(e){return typeof e=="number"&&!Number.isNaN(e)}function Gl(e){return e&&it(e)==="object"&&!Array.isArray(e)&&!l.isValidElement(e)}function Xl(e,t,r,n,o,a){var d=l.useContext(No),i=Al(),c=io(function(){if($n(n))return[n];var s=t==null||t===""?[]:Array.isArray(t)?t:[t],v=_n(e,s),u=v,p=void 0;if(o){var f=o(v,e,r);Gl(f)?(u=f.children,p=f.props,d.renderWithProps=!0):u=f}return[u,p]},[i,e,n,t,o,r],function(s,v){if(a){var u=Se(s,2),p=u[1],f=Se(v,2),m=f[1];return a(m,p)}return d.renderWithProps?!0:!jt(s,v,!0)});return c}function Ul(e,t,r,n){var o=e+t-1;return e<=n&&o>=r}function Yl(e,t){return ze(Xe,function(r){var n=Ul(e,t||1,r.hoverStartRow,r.hoverEndRow);return[n,r.onHover]})}var Jl=function(t){var r=t.ellipsis,n=t.rowType,o=t.children,a,d=r===!0?{showTitle:!0}:r;return d&&(d.showTitle||n==="header")&&(typeof o=="string"||typeof o=="number"?a=o.toString():l.isValidElement(o)&&typeof o.props.children=="string"&&(a=o.props.children)),a};function Ql(e){var t,r,n,o,a,d,i,c,s=e.component,v=e.children,u=e.ellipsis,p=e.scope,f=e.prefixCls,m=e.className,y=e.align,g=e.record,h=e.render,C=e.dataIndex,x=e.renderIndex,w=e.shouldCellUpdate,E=e.index,k=e.rowType,N=e.colSpan,R=e.rowSpan,b=e.fixLeft,K=e.fixRight,I=e.firstFixLeft,D=e.lastFixLeft,P=e.firstFixRight,$=e.lastFixRight,S=e.appendNode,O=e.additionalProps,T=O===void 0?{}:O,_=e.isSticky,M="".concat(f,"-cell"),A=ze(Xe,["supportSticky","allColumnsFixedLeft","rowHoverable"]),Y=A.supportSticky,q=A.allColumnsFixedLeft,ee=A.rowHoverable,be=Xl(g,C,x,v,h,w),Ee=Se(be,2),we=Ee[0],Q=Ee[1],Z={},Ce=typeof b=="number"&&Y,me=typeof K=="number"&&Y;Ce&&(Z.position="sticky",Z.left=b),me&&(Z.position="sticky",Z.right=K);var G=(t=(r=(n=Q==null?void 0:Q.colSpan)!==null&&n!==void 0?n:T.colSpan)!==null&&r!==void 0?r:N)!==null&&t!==void 0?t:1,X=(o=(a=(d=Q==null?void 0:Q.rowSpan)!==null&&d!==void 0?d:T.rowSpan)!==null&&a!==void 0?a:R)!==null&&o!==void 0?o:1,F=Yl(E,X),W=Se(F,2),te=W[0],le=W[1],ge=bt(function(ye){var ie;g&&le(E,E+X-1),T==null||(ie=T.onMouseEnter)===null||ie===void 0||ie.call(T,ye)}),ke=bt(function(ye){var ie;g&&le(-1,-1),T==null||(ie=T.onMouseLeave)===null||ie===void 0||ie.call(T,ye)});if(G===0||X===0)return null;var Fe=(i=T.title)!==null&&i!==void 0?i:Jl({rowType:k,ellipsis:u,children:we}),Re=J(M,m,(c={},B(B(B(B(B(B(B(B(B(B(c,"".concat(M,"-fix-left"),Ce&&Y),"".concat(M,"-fix-left-first"),I&&Y),"".concat(M,"-fix-left-last"),D&&Y),"".concat(M,"-fix-left-all"),D&&q&&Y),"".concat(M,"-fix-right"),me&&Y),"".concat(M,"-fix-right-first"),P&&Y),"".concat(M,"-fix-right-last"),$&&Y),"".concat(M,"-ellipsis"),u),"".concat(M,"-with-append"),S),"".concat(M,"-fix-sticky"),(Ce||me)&&_&&Y),B(c,"".concat(M,"-row-hover"),!Q&&te)),T.className,Q==null?void 0:Q.className),L={};y&&(L.textAlign=y);var j=z(z(z(z({},Q==null?void 0:Q.style),Z),L),T.style),re=we;return it(re)==="object"&&!Array.isArray(re)&&!l.isValidElement(re)&&(re=null),u&&(D||P)&&(re=l.createElement("span",{className:"".concat(M,"-content")},re)),l.createElement(s,pe({},Q,T,{className:Re,style:j,title:Fe,scope:p,onMouseEnter:ee?ge:void 0,onMouseLeave:ee?ke:void 0,colSpan:G!==1?G:null,rowSpan:X!==1?X:null}),S,re)}const Mt=l.memo(Ql);function Vn(e,t,r,n,o){var a=r[e]||{},d=r[t]||{},i,c;a.fixed==="left"?i=n.left[o==="rtl"?t:e]:d.fixed==="right"&&(c=n.right[o==="rtl"?e:t]);var s=!1,v=!1,u=!1,p=!1,f=r[t+1],m=r[e-1],y=f&&!f.fixed||m&&!m.fixed||r.every(function(w){return w.fixed==="left"});if(o==="rtl"){if(i!==void 0){var g=m&&m.fixed==="left";p=!g&&y}else if(c!==void 0){var h=f&&f.fixed==="right";u=!h&&y}}else if(i!==void 0){var C=f&&f.fixed==="left";s=!C&&y}else if(c!==void 0){var x=m&&m.fixed==="right";v=!x&&y}return{fixLeft:i,fixRight:c,lastFixLeft:s,firstFixRight:v,lastFixRight:u,firstFixLeft:p,isSticky:n.isSticky}}var Ro=l.createContext({});function Zl(e){var t=e.className,r=e.index,n=e.children,o=e.colSpan,a=o===void 0?1:o,d=e.rowSpan,i=e.align,c=ze(Xe,["prefixCls","direction"]),s=c.prefixCls,v=c.direction,u=l.useContext(Ro),p=u.scrollColumnIndex,f=u.stickyOffsets,m=u.flattenColumns,y=r+a-1,g=y+1===p?a+1:a,h=Vn(r,r+g-1,m,f,v);return l.createElement(Mt,pe({className:t,index:r,component:"td",prefixCls:s,record:null,dataIndex:null,align:i,colSpan:g,rowSpan:d,render:function(){return n}},h))}var ei=["children"];function ti(e){var t=e.children,r=rt(e,ei);return l.createElement("tr",r,t)}function sn(e){var t=e.children;return t}sn.Row=ti;sn.Cell=Zl;function ni(e){var t=e.children,r=e.stickyOffsets,n=e.flattenColumns,o=ze(Xe,"prefixCls"),a=n.length-1,d=n[a],i=l.useMemo(function(){return{stickyOffsets:r,flattenColumns:n,scrollColumnIndex:d!=null&&d.scrollbar?a:null}},[d,n,a,r]);return l.createElement(Ro.Provider,{value:i},l.createElement("tfoot",{className:"".concat(o,"-summary")},t))}const Zt=Dt(ni);var Io=sn;function ri(e){return null}function oi(e){return null}function Oo(e,t,r,n,o,a,d){var i=a(t,d);e.push({record:t,indent:r,index:d,rowKey:i});var c=o==null?void 0:o.has(i);if(t&&Array.isArray(t[n])&&c)for(var s=0;s<t[n].length;s+=1)Oo(e,t[n][s],r+1,n,o,a,s)}function Ko(e,t,r,n){var o=l.useMemo(function(){if(r!=null&&r.size){for(var a=[],d=0;d<(e==null?void 0:e.length);d+=1){var i=e[d];Oo(a,i,0,t,r,n,d)}return a}return e==null?void 0:e.map(function(c,s){return{record:c,indent:0,index:s,rowKey:n(c,s)}})},[e,t,r,n]);return o}function Po(e,t,r,n){var o=ze(Xe,["prefixCls","fixedInfoList","flattenColumns","expandableType","expandRowByClick","onTriggerExpand","rowClassName","expandedRowClassName","indentSize","expandIcon","expandedRowRender","expandIconColumnIndex","expandedKeys","childrenColumnName","rowExpandable","onRow"]),a=o.flattenColumns,d=o.expandableType,i=o.expandedKeys,c=o.childrenColumnName,s=o.onTriggerExpand,v=o.rowExpandable,u=o.onRow,p=o.expandRowByClick,f=o.rowClassName,m=d==="nest",y=d==="row"&&(!v||v(e)),g=y||m,h=i&&i.has(t),C=c&&e&&e[c],x=bt(s),w=u==null?void 0:u(e,r),E=w==null?void 0:w.onClick,k=function(K){p&&g&&s(e,K);for(var I=arguments.length,D=new Array(I>1?I-1:0),P=1;P<I;P++)D[P-1]=arguments[P];E==null||E.apply(void 0,[K].concat(D))},N;typeof f=="string"?N=f:typeof f=="function"&&(N=f(e,r,n));var R=ln(a);return z(z({},o),{},{columnsKey:R,nestExpandable:m,expanded:h,hasNestChildren:C,record:e,onTriggerExpand:x,rowSupportExpand:y,expandable:g,rowProps:z(z({},w),{},{className:J(N,w==null?void 0:w.className),onClick:k})})}function To(e){var t=e.prefixCls,r=e.children,n=e.component,o=e.cellComponent,a=e.className,d=e.expanded,i=e.colSpan,c=e.isEmpty,s=e.stickyOffset,v=s===void 0?0:s,u=ze(Xe,["scrollbarSize","fixHeader","fixColumn","componentWidth","horizonScroll"]),p=u.scrollbarSize,f=u.fixHeader,m=u.fixColumn,y=u.componentWidth,g=u.horizonScroll,h=r;return(c?g&&y:m)&&(h=l.createElement("div",{style:{width:y-v-(f&&!c?p:0),position:"sticky",left:v,overflow:"hidden"},className:"".concat(t,"-expanded-row-fixed")},h)),l.createElement(n,{className:a,style:{display:d?null:"none"}},l.createElement(Mt,{component:o,prefixCls:t,colSpan:i},h))}function ai(e){var t=e.prefixCls,r=e.record,n=e.onExpand,o=e.expanded,a=e.expandable,d="".concat(t,"-row-expand-icon");if(!a)return l.createElement("span",{className:J(d,"".concat(t,"-row-spaced"))});var i=function(s){n(r,s),s.stopPropagation()};return l.createElement("span",{className:J(d,B(B({},"".concat(t,"-row-expanded"),o),"".concat(t,"-row-collapsed"),!o)),onClick:i})}function li(e,t,r){var n=[];function o(a){(a||[]).forEach(function(d,i){n.push(t(d,i)),o(d[r])})}return o(e),n}function Do(e,t,r,n){return typeof e=="string"?e:typeof e=="function"?e(t,r,n):""}function Mo(e,t,r,n,o){var a,d=arguments.length>5&&arguments[5]!==void 0?arguments[5]:[],i=arguments.length>6&&arguments[6]!==void 0?arguments[6]:0,c=e.record,s=e.prefixCls,v=e.columnsKey,u=e.fixedInfoList,p=e.expandIconColumnIndex,f=e.nestExpandable,m=e.indentSize,y=e.expandIcon,g=e.expanded,h=e.hasNestChildren,C=e.onTriggerExpand,x=e.expandable,w=e.expandedKeys,E=v[r],k=u[r],N;r===(p||0)&&f&&(N=l.createElement(l.Fragment,null,l.createElement("span",{style:{paddingLeft:"".concat(m*n,"px")},className:"".concat(s,"-row-indent indent-level-").concat(n)}),y({prefixCls:s,expanded:g,expandable:h,record:c,onExpand:C})));var R=((a=t.onCell)===null||a===void 0?void 0:a.call(t,c,o))||{};if(i){var b=R.rowSpan,K=b===void 0?1:b;if(x&&K&&r<i){for(var I=K,D=o;D<o+K;D+=1){var P=d[D];w.has(P)&&(I+=1)}R.rowSpan=I}}return{key:E,fixedInfo:k,appendCellNode:N,additionalCellProps:R}}function ii(e){var t=e.className,r=e.style,n=e.record,o=e.index,a=e.renderIndex,d=e.rowKey,i=e.rowKeys,c=e.indent,s=c===void 0?0:c,v=e.rowComponent,u=e.cellComponent,p=e.scopeCellComponent,f=e.expandedRowInfo,m=Po(n,d,o,s),y=m.prefixCls,g=m.flattenColumns,h=m.expandedRowClassName,C=m.expandedRowRender,x=m.rowProps,w=m.expanded,E=m.rowSupportExpand,k=l.useRef(!1);k.current||(k.current=w);var N=Do(h,n,o,s),R=l.createElement(v,pe({},x,{"data-row-key":d,className:J(t,"".concat(y,"-row"),"".concat(y,"-row-level-").concat(s),x==null?void 0:x.className,B({},N,s>=1)),style:z(z({},r),x==null?void 0:x.style)}),g.map(function(I,D){var P=I.render,$=I.dataIndex,S=I.className,O=Mo(m,I,D,s,o,i,f==null?void 0:f.offset),T=O.key,_=O.fixedInfo,M=O.appendCellNode,A=O.additionalCellProps;return l.createElement(Mt,pe({className:S,ellipsis:I.ellipsis,align:I.align,scope:I.rowScope,component:I.rowScope?p:u,prefixCls:y,key:T,record:n,index:o,renderIndex:a,dataIndex:$,render:P,shouldCellUpdate:I.shouldCellUpdate},_,{appendNode:M,additionalProps:A}))})),b;if(E&&(k.current||w)){var K=C(n,o,s+1,w);b=l.createElement(To,{expanded:w,className:J("".concat(y,"-expanded-row"),"".concat(y,"-expanded-row-level-").concat(s+1),N),prefixCls:y,component:v,cellComponent:u,colSpan:f?f.colSpan:g.length,stickyOffset:f==null?void 0:f.sticky,isEmpty:!1},K)}return l.createElement(l.Fragment,null,R,b)}const si=Dt(ii);function ci(e){var t=e.columnKey,r=e.onColumnResize,n=l.useRef();return ht(function(){n.current&&r(t,n.current.offsetWidth)},[]),l.createElement(Hn,{data:t},l.createElement("td",{ref:n,style:{padding:0,border:0,height:0}},l.createElement("div",{style:{height:0,overflow:"hidden"}}," ")))}function di(e){var t=e.prefixCls,r=e.columnsKey,n=e.onColumnResize,o=l.useRef(null);return l.createElement("tr",{"aria-hidden":"true",className:"".concat(t,"-measure-row"),style:{height:0,fontSize:0},ref:o},l.createElement(Hn.Collection,{onBatchResize:function(d){ua(o.current)&&d.forEach(function(i){var c=i.data,s=i.size;n(c,s.offsetWidth)})}},r.map(function(a){return l.createElement(ci,{key:a,columnKey:a,onColumnResize:n})})))}function ui(e){var t=e.data,r=e.measureColumnWidth,n=ze(Xe,["prefixCls","getComponent","onColumnResize","flattenColumns","getRowKey","expandedKeys","childrenColumnName","emptyNode","expandedRowOffset","fixedInfoList","colWidths"]),o=n.prefixCls,a=n.getComponent,d=n.onColumnResize,i=n.flattenColumns,c=n.getRowKey,s=n.expandedKeys,v=n.childrenColumnName,u=n.emptyNode,p=n.expandedRowOffset,f=p===void 0?0:p,m=n.colWidths,y=Ko(t,v,s,c),g=l.useMemo(function(){return y.map(function(b){return b.rowKey})},[y]),h=l.useRef({renderWithProps:!1}),C=l.useMemo(function(){for(var b=i.length-f,K=0,I=0;I<f;I+=1)K+=m[I]||0;return{offset:f,colSpan:b,sticky:K}},[i.length,f,m]),x=a(["body","wrapper"],"tbody"),w=a(["body","row"],"tr"),E=a(["body","cell"],"td"),k=a(["body","cell"],"th"),N;t.length?N=y.map(function(b,K){var I=b.record,D=b.indent,P=b.index,$=b.rowKey;return l.createElement(si,{key:$,rowKey:$,rowKeys:g,record:I,index:K,renderIndex:P,rowComponent:w,cellComponent:E,scopeCellComponent:k,indent:D,expandedRowInfo:C})}):N=l.createElement(To,{expanded:!0,className:"".concat(o,"-placeholder"),prefixCls:o,component:w,cellComponent:E,colSpan:i.length,isEmpty:!0},u);var R=ln(i);return l.createElement(No.Provider,{value:h.current},l.createElement(x,{className:"".concat(o,"-tbody")},r&&l.createElement(di,{prefixCls:o,columnsKey:R,onColumnResize:d}),N))}const fi=Dt(ui);var vi=["expandable"],Ft="RC_TABLE_INTERNAL_COL_DEFINE";function pi(e){var t=e.expandable,r=rt(e,vi),n;return"expandable"in e?n=z(z({},r),t):n=r,n.showExpandColumn===!1&&(n.expandIconColumnIndex=-1),n}var mi=["columnType"];function Bo(e){for(var t=e.colWidths,r=e.columns,n=e.columCount,o=ze(Xe,["tableLayout"]),a=o.tableLayout,d=[],i=n||r.length,c=!1,s=i-1;s>=0;s-=1){var v=t[s],u=r&&r[s],p=void 0,f=void 0;if(u&&(p=u[Ft],a==="auto"&&(f=u.minWidth)),v||f||p||c){var m=p||{};m.columnType;var y=rt(m,mi);d.unshift(l.createElement("col",pe({key:s,style:{width:v,minWidth:f}},y))),c=!0}}return l.createElement("colgroup",null,d)}var gi=["className","noData","columns","flattenColumns","colWidths","columCount","stickyOffsets","direction","fixHeader","stickyTopOffset","stickyBottomOffset","stickyClassName","onScroll","maxContentScroll","children"];function hi(e,t){return l.useMemo(function(){for(var r=[],n=0;n<t;n+=1){var o=e[n];if(o!==void 0)r[n]=o;else return null}return r},[e.join("_"),t])}var bi=l.forwardRef(function(e,t){var r=e.className,n=e.noData,o=e.columns,a=e.flattenColumns,d=e.colWidths,i=e.columCount,c=e.stickyOffsets,s=e.direction,v=e.fixHeader,u=e.stickyTopOffset,p=e.stickyBottomOffset,f=e.stickyClassName,m=e.onScroll,y=e.maxContentScroll,g=e.children,h=rt(e,gi),C=ze(Xe,["prefixCls","scrollbarSize","isSticky","getComponent"]),x=C.prefixCls,w=C.scrollbarSize,E=C.isSticky,k=C.getComponent,N=k(["header","table"],"table"),R=E&&!v?0:w,b=l.useRef(null),K=l.useCallback(function(_){hr(t,_),hr(b,_)},[]);l.useEffect(function(){function _(A){var Y=A,q=Y.currentTarget,ee=Y.deltaX;ee&&(m({currentTarget:q,scrollLeft:q.scrollLeft+ee}),A.preventDefault())}var M=b.current;return M==null||M.addEventListener("wheel",_,{passive:!1}),function(){M==null||M.removeEventListener("wheel",_)}},[]);var I=l.useMemo(function(){return a.every(function(_){return _.width})},[a]),D=a[a.length-1],P={fixed:D?D.fixed:null,scrollbar:!0,onHeaderCell:function(){return{className:"".concat(x,"-cell-scrollbar")}}},$=l.useMemo(function(){return R?[].concat(ce(o),[P]):o},[R,o]),S=l.useMemo(function(){return R?[].concat(ce(a),[P]):a},[R,a]),O=l.useMemo(function(){var _=c.right,M=c.left;return z(z({},c),{},{left:s==="rtl"?[].concat(ce(M.map(function(A){return A+R})),[0]):M,right:s==="rtl"?_:[].concat(ce(_.map(function(A){return A+R})),[0]),isSticky:E})},[R,c,E]),T=hi(d,i);return l.createElement("div",{style:z({overflow:"hidden"},E?{top:u,bottom:p}:{}),ref:K,className:J(r,B({},f,!!f))},l.createElement(N,{style:{tableLayout:"fixed",visibility:n||T?null:"hidden"}},(!n||!y||I)&&l.createElement(Bo,{colWidths:T?[].concat(ce(T),[R]):[],columCount:i+1,columns:S}),g(z(z({},h),{},{stickyOffsets:O,columns:$,flattenColumns:S}))))});const $r=l.memo(bi);var yi=function(t){var r=t.cells,n=t.stickyOffsets,o=t.flattenColumns,a=t.rowComponent,d=t.cellComponent,i=t.onHeaderRow,c=t.index,s=ze(Xe,["prefixCls","direction"]),v=s.prefixCls,u=s.direction,p;i&&(p=i(r.map(function(m){return m.column}),c));var f=ln(r.map(function(m){return m.column}));return l.createElement(a,p,r.map(function(m,y){var g=m.column,h=Vn(m.colStart,m.colEnd,o,n,u),C;return g&&g.onHeaderCell&&(C=m.column.onHeaderCell(g)),l.createElement(Mt,pe({},m,{scope:g.title?m.colSpan>1?"colgroup":"col":null,ellipsis:g.ellipsis,align:g.align,component:d,prefixCls:v,key:f[y]},h,{additionalProps:C,rowType:"header"}))}))};function xi(e){var t=[];function r(d,i){var c=arguments.length>2&&arguments[2]!==void 0?arguments[2]:0;t[c]=t[c]||[];var s=i,v=d.filter(Boolean).map(function(u){var p={key:u.key,className:u.className||"",children:u.title,column:u,colStart:s},f=1,m=u.children;return m&&m.length>0&&(f=r(m,s,c+1).reduce(function(y,g){return y+g},0),p.hasSubColumns=!0),"colSpan"in u&&(f=u.colSpan),"rowSpan"in u&&(p.rowSpan=u.rowSpan),p.colSpan=f,p.colEnd=p.colStart+f-1,t[c].push(p),s+=f,f});return v}r(e,0);for(var n=t.length,o=function(i){t[i].forEach(function(c){!("rowSpan"in c)&&!c.hasSubColumns&&(c.rowSpan=n-i)})},a=0;a<n;a+=1)o(a);return t}var Ci=function(t){var r=t.stickyOffsets,n=t.columns,o=t.flattenColumns,a=t.onHeaderRow,d=ze(Xe,["prefixCls","getComponent"]),i=d.prefixCls,c=d.getComponent,s=l.useMemo(function(){return xi(n)},[n]),v=c(["header","wrapper"],"thead"),u=c(["header","row"],"tr"),p=c(["header","cell"],"th");return l.createElement(v,{className:"".concat(i,"-thead")},s.map(function(f,m){var y=l.createElement(yi,{key:m,flattenColumns:o,cells:f,stickyOffsets:r,rowComponent:u,cellComponent:p,onHeaderRow:a,index:m});return y}))};const kr=Dt(Ci);function Nr(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";return typeof t=="number"?t:t.endsWith("%")?e*parseFloat(t)/100:null}function Si(e,t,r){return l.useMemo(function(){if(t&&t>0){var n=0,o=0;e.forEach(function(p){var f=Nr(t,p.width);f?n+=f:o+=1});var a=Math.max(t,r),d=Math.max(a-n,o),i=o,c=d/o,s=0,v=e.map(function(p){var f=z({},p),m=Nr(t,f.width);if(m)f.width=m;else{var y=Math.floor(c);f.width=i===1?d:y,d-=y,i-=1}return s+=f.width,f});if(s<a){var u=a/s;d=a,v.forEach(function(p,f){var m=Math.floor(p.width*u);p.width=f===v.length-1?d:m,d-=m})}return[v,Math.max(s,a)]}return[e,t]},[e,t,r])}var wi=["children"],Ei=["fixed"];function qn(e){return lo(e).filter(function(t){return l.isValidElement(t)}).map(function(t){var r=t.key,n=t.props,o=n.children,a=rt(n,wi),d=z({key:r},a);return o&&(d.children=qn(o)),d})}function Lo(e){return e.filter(function(t){return t&&it(t)==="object"&&!t.hidden}).map(function(t){var r=t.children;return r&&r.length>0?z(z({},t),{},{children:Lo(r)}):t})}function kn(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"key";return e.filter(function(r){return r&&it(r)==="object"}).reduce(function(r,n,o){var a=n.fixed,d=a===!0?"left":a,i="".concat(t,"-").concat(o),c=n.children;return c&&c.length>0?[].concat(ce(r),ce(kn(c,i).map(function(s){return z({fixed:d},s)}))):[].concat(ce(r),[z(z({key:i},n),{},{fixed:d})])},[])}function $i(e){return e.map(function(t){var r=t.fixed,n=rt(t,Ei),o=r;return r==="left"?o="right":r==="right"&&(o="left"),z({fixed:o},n)})}function ki(e,t){var r=e.prefixCls,n=e.columns,o=e.children,a=e.expandable,d=e.expandedKeys,i=e.columnTitle,c=e.getRowKey,s=e.onTriggerExpand,v=e.expandIcon,u=e.rowExpandable,p=e.expandIconColumnIndex,f=e.expandedRowOffset,m=f===void 0?0:f,y=e.direction,g=e.expandRowByClick,h=e.columnWidth,C=e.fixed,x=e.scrollWidth,w=e.clientWidth,E=l.useMemo(function(){var $=n||qn(o)||[];return Lo($.slice())},[n,o]),k=l.useMemo(function(){if(a){var $=E.slice();if(!$.includes(ct)){var S=p||0;S>=0&&(S||C==="left"||!C)&&$.splice(S,0,ct),C==="right"&&$.splice(E.length,0,ct)}var O=$.indexOf(ct);$=$.filter(function(A,Y){return A!==ct||Y===O});var T=E[O],_;C?_=C:_=T?T.fixed:null;var M=B(B(B(B(B(B({},Ft,{className:"".concat(r,"-expand-icon-col"),columnType:"EXPAND_COLUMN"}),"title",i),"fixed",_),"className","".concat(r,"-row-expand-icon-cell")),"width",h),"render",function(Y,q,ee){var be=c(q,ee),Ee=d.has(be),we=u?u(q):!0,Q=v({prefixCls:r,expanded:Ee,expandable:we,record:q,onExpand:s});return g?l.createElement("span",{onClick:function(Ce){return Ce.stopPropagation()}},Q):Q});return $.map(function(A,Y){var q=A===ct?M:A;return Y<m?z(z({},q),{},{fixed:q.fixed||"left"}):q})}return E.filter(function(A){return A!==ct})},[a,E,c,d,v,y,m]),N=l.useMemo(function(){var $=k;return t&&($=t($)),$.length||($=[{render:function(){return null}}]),$},[t,k,y]),R=l.useMemo(function(){return y==="rtl"?$i(kn(N)):kn(N)},[N,y,x]),b=l.useMemo(function(){for(var $=-1,S=R.length-1;S>=0;S-=1){var O=R[S].fixed;if(O==="left"||O===!0){$=S;break}}if($>=0)for(var T=0;T<=$;T+=1){var _=R[T].fixed;if(_!=="left"&&_!==!0)return!0}var M=R.findIndex(function(q){var ee=q.fixed;return ee==="right"});if(M>=0)for(var A=M;A<R.length;A+=1){var Y=R[A].fixed;if(Y!=="right")return!0}return!1},[R]),K=Si(R,x,w),I=Se(K,2),D=I[0],P=I[1];return[N,D,P,b]}function Ni(e,t,r){var n=pi(e),o=n.expandIcon,a=n.expandedRowKeys,d=n.defaultExpandedRowKeys,i=n.defaultExpandAllRows,c=n.expandedRowRender,s=n.onExpand,v=n.onExpandedRowsChange,u=n.childrenColumnName,p=o||ai,f=u||"children",m=l.useMemo(function(){return c?"row":e.expandable&&e.internalHooks===Xt&&e.expandable.__PARENT_RENDER_ICON__||t.some(function(E){return E&&it(E)==="object"&&E[f]})?"nest":!1},[!!c,t]),y=l.useState(function(){return d||(i?li(t,r,f):[])}),g=Se(y,2),h=g[0],C=g[1],x=l.useMemo(function(){return new Set(a||h||[])},[a,h]),w=l.useCallback(function(E){var k=r(E,t.indexOf(E)),N,R=x.has(k);R?(x.delete(k),N=ce(x)):N=[].concat(ce(x),[k]),C(N),s&&s(!R,E),v&&v(N)},[r,x,t,s,v]);return[n,m,x,p,f,w]}function Ri(e,t,r){var n=e.map(function(o,a){return Vn(a,a,e,t,r)});return io(function(){return n},[n],function(o,a){return!jt(o,a)})}function Ii(e){var t=l.useRef(e),r=l.useState({}),n=Se(r,2),o=n[1],a=l.useRef(null),d=l.useRef([]);function i(c){d.current.push(c);var s=Promise.resolve();a.current=s,s.then(function(){if(a.current===s){var v=d.current,u=t.current;d.current=[],v.forEach(function(p){t.current=p(t.current)}),a.current=null,u!==t.current&&o({})}})}return l.useEffect(function(){return function(){a.current=null}},[]),[t.current,i]}function Oi(e){var t=l.useRef(null),r=l.useRef();function n(){window.clearTimeout(r.current)}function o(d){t.current=d,n(),r.current=window.setTimeout(function(){t.current=null,r.current=void 0},100)}function a(){return t.current}return l.useEffect(function(){return n},[]),[o,a]}function Ki(){var e=l.useState(-1),t=Se(e,2),r=t[0],n=t[1],o=l.useState(-1),a=Se(o,2),d=a[0],i=a[1],c=l.useCallback(function(s,v){n(s),i(v)},[]);return[r,d,c]}var Rr=fa()?window:null;function Pi(e,t){var r=it(e)==="object"?e:{},n=r.offsetHeader,o=n===void 0?0:n,a=r.offsetSummary,d=a===void 0?0:a,i=r.offsetScroll,c=i===void 0?0:i,s=r.getContainer,v=s===void 0?function(){return Rr}:s,u=v()||Rr,p=!!e;return l.useMemo(function(){return{isSticky:p,stickyClassName:p?"".concat(t,"-sticky-holder"):"",offsetHeader:o,offsetSummary:d,offsetScroll:c,container:u}},[p,c,o,d,t,u])}function Ti(e,t,r){var n=l.useMemo(function(){var o=t.length,a=function(s,v,u){for(var p=[],f=0,m=s;m!==v;m+=u)p.push(f),t[m].fixed&&(f+=e[m]||0);return p},d=a(0,o,1),i=a(o-1,-1,-1).reverse();return r==="rtl"?{left:i,right:d}:{left:d,right:i}},[e,t,r]);return n}function Ir(e){var t=e.className,r=e.children;return l.createElement("div",{className:t},r)}function Or(e){var t=zn(e),r=t.getBoundingClientRect(),n=document.documentElement;return{left:r.left+(window.pageXOffset||n.scrollLeft)-(n.clientLeft||document.body.clientLeft||0),top:r.top+(window.pageYOffset||n.scrollTop)-(n.clientTop||document.body.clientTop||0)}}var Di=function(t,r){var n,o,a=t.scrollBodyRef,d=t.onScroll,i=t.offsetScroll,c=t.container,s=t.direction,v=ze(Xe,"prefixCls"),u=((n=a.current)===null||n===void 0?void 0:n.scrollWidth)||0,p=((o=a.current)===null||o===void 0?void 0:o.clientWidth)||0,f=u&&p*(p/u),m=l.useRef(),y=Ii({scrollLeft:0,isHiddenScrollBar:!0}),g=Se(y,2),h=g[0],C=g[1],x=l.useRef({delta:0,x:0}),w=l.useState(!1),E=Se(w,2),k=E[0],N=E[1],R=l.useRef(null);l.useEffect(function(){return function(){Et.cancel(R.current)}},[]);var b=function(){N(!1)},K=function(S){S.persist(),x.current.delta=S.pageX-h.scrollLeft,x.current.x=0,N(!0),S.preventDefault()},I=function(S){var O,T=S||((O=window)===null||O===void 0?void 0:O.event),_=T.buttons;if(!k||_===0){k&&N(!1);return}var M=x.current.x+S.pageX-x.current.x-x.current.delta,A=s==="rtl";M=Math.max(A?f-p:0,Math.min(A?0:p-f,M));var Y=!A||Math.abs(M)+Math.abs(f)<p;Y&&(d({scrollLeft:M/p*(u+2)}),x.current.x=S.pageX)},D=function(){Et.cancel(R.current),R.current=Et(function(){if(a.current){var S=Or(a.current).top,O=S+a.current.offsetHeight,T=c===window?document.documentElement.scrollTop+window.innerHeight:Or(c).top+c.clientHeight;O-br()<=T||S>=T-i?C(function(_){return z(z({},_),{},{isHiddenScrollBar:!0})}):C(function(_){return z(z({},_),{},{isHiddenScrollBar:!1})})}})},P=function(S){C(function(O){return z(z({},O),{},{scrollLeft:S/u*p||0})})};return l.useImperativeHandle(r,function(){return{setScrollLeft:P,checkScrollBarVisible:D}}),l.useEffect(function(){var $=Er(document.body,"mouseup",b,!1),S=Er(document.body,"mousemove",I,!1);return D(),function(){$.remove(),S.remove()}},[f,k]),l.useEffect(function(){if(a.current){for(var $=[],S=zn(a.current);S;)$.push(S),S=S.parentElement;return $.forEach(function(O){return O.addEventListener("scroll",D,!1)}),window.addEventListener("resize",D,!1),window.addEventListener("scroll",D,!1),c.addEventListener("scroll",D,!1),function(){$.forEach(function(O){return O.removeEventListener("scroll",D)}),window.removeEventListener("resize",D),window.removeEventListener("scroll",D),c.removeEventListener("scroll",D)}}},[c]),l.useEffect(function(){h.isHiddenScrollBar||C(function($){var S=a.current;return S?z(z({},$),{},{scrollLeft:S.scrollLeft/S.scrollWidth*S.clientWidth}):$})},[h.isHiddenScrollBar]),u<=p||!f||h.isHiddenScrollBar?null:l.createElement("div",{style:{height:br(),width:p,bottom:i},className:"".concat(v,"-sticky-scroll")},l.createElement("div",{onMouseDown:K,ref:m,className:J("".concat(v,"-sticky-scroll-bar"),B({},"".concat(v,"-sticky-scroll-bar-active"),k)),style:{width:"".concat(f,"px"),transform:"translate3d(".concat(h.scrollLeft,"px, 0, 0)")}}))};const Mi=l.forwardRef(Di);var _o="rc-table",Bi=[],Li={};function _i(){return"No Data"}function Hi(e,t){var r=z({rowKey:"key",prefixCls:_o,emptyText:_i},e),n=r.prefixCls,o=r.className,a=r.rowClassName,d=r.style,i=r.data,c=r.rowKey,s=r.scroll,v=r.tableLayout,u=r.direction,p=r.title,f=r.footer,m=r.summary,y=r.caption,g=r.id,h=r.showHeader,C=r.components,x=r.emptyText,w=r.onRow,E=r.onHeaderRow,k=r.onScroll,N=r.internalHooks,R=r.transformColumns,b=r.internalRefs,K=r.tailor,I=r.getContainerWidth,D=r.sticky,P=r.rowHoverable,$=P===void 0?!0:P,S=i||Bi,O=!!S.length,T=N===Xt,_=l.useCallback(function(se,fe){return _n(C,se)||fe},[C]),M=l.useMemo(function(){return typeof c=="function"?c:function(se){var fe=se&&se[c];return fe}},[c]),A=_(["body"]),Y=Ki(),q=Se(Y,3),ee=q[0],be=q[1],Ee=q[2],we=Ni(r,S,M),Q=Se(we,6),Z=Q[0],Ce=Q[1],me=Q[2],G=Q[3],X=Q[4],F=Q[5],W=s==null?void 0:s.x,te=l.useState(0),le=Se(te,2),ge=le[0],ke=le[1],Fe=ki(z(z(z({},r),Z),{},{expandable:!!Z.expandedRowRender,columnTitle:Z.columnTitle,expandedKeys:me,getRowKey:M,onTriggerExpand:F,expandIcon:G,expandIconColumnIndex:Z.expandIconColumnIndex,direction:u,scrollWidth:T&&K&&typeof W=="number"?W:null,clientWidth:ge}),T?R:null),Re=Se(Fe,4),L=Re[0],j=Re[1],re=Re[2],ye=Re[3],ie=re??W,Ne=l.useMemo(function(){return{columns:L,flattenColumns:j}},[L,j]),Pe=l.useRef(),Le=l.useRef(),he=l.useRef(),de=l.useRef();l.useImperativeHandle(t,function(){return{nativeElement:Pe.current,scrollTo:function(fe){var _e;if(he.current instanceof HTMLElement){var nt=fe.index,He=fe.top,It=fe.key;if(ql(He)){var St;(St=he.current)===null||St===void 0||St.scrollTo({top:He})}else{var wt,_t=It??M(S[nt]);(wt=he.current.querySelector('[data-row-key="'.concat(_t,'"]')))===null||wt===void 0||wt.scrollIntoView()}}else(_e=he.current)!==null&&_e!==void 0&&_e.scrollTo&&he.current.scrollTo(fe)}}});var U=l.useRef(),H=l.useState(!1),$e=Se(H,2),Ie=$e[0],ne=$e[1],Te=l.useState(!1),xe=Se(Te,2),De=xe[0],Ue=xe[1],Ve=l.useState(new Map),ft=Se(Ve,2),Oe=ft[0],kt=ft[1],Nt=ln(j),qe=Nt.map(function(se){return Oe.get(se)}),Qe=l.useMemo(function(){return qe},[qe.join("_")]),Ze=Ti(Qe,j,u),Ge=s&&$n(s.y),je=s&&$n(ie)||!!Z.fixed,Ye=je&&j.some(function(se){var fe=se.fixed;return fe}),Ct=l.useRef(),vt=Pi(D,n),at=vt.isSticky,dn=vt.offsetHeader,un=vt.offsetSummary,Ut=vt.offsetScroll,fn=vt.stickyClassName,oe=vt.container,ae=l.useMemo(function(){return m==null?void 0:m(S)},[m,S]),Ke=(Ge||at)&&l.isValidElement(ae)&&ae.type===sn&&ae.props.fixed,Be,Ae,et;Ge&&(Ae={overflowY:O?"scroll":"auto",maxHeight:s.y}),je&&(Be={overflowX:"auto"},Ge||(Ae={overflowY:"hidden"}),et={width:ie===!0?"auto":ie,minWidth:"100%"});var tt=l.useCallback(function(se,fe){kt(function(_e){if(_e.get(se)!==fe){var nt=new Map(_e);return nt.set(se,fe),nt}return _e})},[]),We=Oi(),Zn=Se(We,2),Zo=Zn[0],er=Zn[1];function Yt(se,fe){fe&&(typeof fe=="function"?fe(se):fe.scrollLeft!==se&&(fe.scrollLeft=se,fe.scrollLeft!==se&&setTimeout(function(){fe.scrollLeft=se},0)))}var Rt=bt(function(se){var fe=se.currentTarget,_e=se.scrollLeft,nt=u==="rtl",He=typeof _e=="number"?_e:fe.scrollLeft,It=fe||Li;if(!er()||er()===It){var St;Zo(It),Yt(He,Le.current),Yt(He,he.current),Yt(He,U.current),Yt(He,(St=Ct.current)===null||St===void 0?void 0:St.setScrollLeft)}var wt=fe||Le.current;if(wt){var _t=T&&K&&typeof ie=="number"?ie:wt.scrollWidth,hn=wt.clientWidth;if(_t===hn){ne(!1),Ue(!1);return}nt?(ne(-He<_t-hn),Ue(-He>0)):(ne(He>0),Ue(He<_t-hn))}}),ea=bt(function(se){Rt(se),k==null||k(se)}),tr=function(){if(je&&he.current){var fe;Rt({currentTarget:zn(he.current),scrollLeft:(fe=he.current)===null||fe===void 0?void 0:fe.scrollLeft})}else ne(!1),Ue(!1)},ta=function(fe){var _e,nt=fe.width;(_e=Ct.current)===null||_e===void 0||_e.checkScrollBarVisible();var He=Pe.current?Pe.current.offsetWidth:nt;T&&I&&Pe.current&&(He=I(Pe.current,He)||He),He!==ge&&(tr(),ke(He))},nr=l.useRef(!1);l.useEffect(function(){nr.current&&tr()},[je,i,L.length]),l.useEffect(function(){nr.current=!0},[]);var na=l.useState(0),rr=Se(na,2),Jt=rr[0],or=rr[1],ra=l.useState(!0),ar=Se(ra,2),lr=ar[0],oa=ar[1];ht(function(){(!K||!T)&&(he.current instanceof Element?or(yr(he.current).width):or(yr(de.current).width)),oa(va("position","sticky"))},[]),l.useEffect(function(){T&&b&&(b.body.current=he.current)});var aa=l.useCallback(function(se){return l.createElement(l.Fragment,null,l.createElement(kr,se),Ke==="top"&&l.createElement(Zt,se,ae))},[Ke,ae]),la=l.useCallback(function(se){return l.createElement(Zt,se,ae)},[ae]),ir=_(["table"],"table"),Qt=l.useMemo(function(){return v||(Ye?ie==="max-content"?"auto":"fixed":Ge||at||j.some(function(se){var fe=se.ellipsis;return fe})?"fixed":"auto")},[Ge,Ye,j,v,at]),vn,pn={colWidths:Qe,columCount:j.length,stickyOffsets:Ze,onHeaderRow:E,fixHeader:Ge,scroll:s},sr=l.useMemo(function(){return O?null:typeof x=="function"?x():x},[O,x]),cr=l.createElement(fi,{data:S,measureColumnWidth:Ge||je||at}),dr=l.createElement(Bo,{colWidths:j.map(function(se){var fe=se.width;return fe}),columns:j}),ur=y!=null?l.createElement("caption",{className:"".concat(n,"-caption")},y):void 0,ia=At(r,{data:!0}),fr=At(r,{aria:!0});if(Ge||at){var mn;typeof A=="function"?(mn=A(S,{scrollbarSize:Jt,ref:he,onScroll:Rt}),pn.colWidths=j.map(function(se,fe){var _e=se.width,nt=fe===j.length-1?_e-Jt:_e;return typeof nt=="number"&&!Number.isNaN(nt)?nt:0})):mn=l.createElement("div",{style:z(z({},Be),Ae),onScroll:ea,ref:he,className:J("".concat(n,"-body"))},l.createElement(ir,pe({style:z(z({},et),{},{tableLayout:Qt})},fr),ur,dr,cr,!Ke&&ae&&l.createElement(Zt,{stickyOffsets:Ze,flattenColumns:j},ae)));var vr=z(z(z({noData:!S.length,maxContentScroll:je&&ie==="max-content"},pn),Ne),{},{direction:u,stickyClassName:fn,onScroll:Rt});vn=l.createElement(l.Fragment,null,h!==!1&&l.createElement($r,pe({},vr,{stickyTopOffset:dn,className:"".concat(n,"-header"),ref:Le}),aa),mn,Ke&&Ke!=="top"&&l.createElement($r,pe({},vr,{stickyBottomOffset:un,className:"".concat(n,"-summary"),ref:U}),la),at&&he.current&&he.current instanceof Element&&l.createElement(Mi,{ref:Ct,offsetScroll:Ut,scrollBodyRef:he,onScroll:Rt,container:oe,direction:u}))}else vn=l.createElement("div",{style:z(z({},Be),Ae),className:J("".concat(n,"-content")),onScroll:Rt,ref:he},l.createElement(ir,pe({style:z(z({},et),{},{tableLayout:Qt})},fr),ur,dr,h!==!1&&l.createElement(kr,pe({},pn,Ne)),cr,ae&&l.createElement(Zt,{stickyOffsets:Ze,flattenColumns:j},ae)));var gn=l.createElement("div",pe({className:J(n,o,B(B(B(B(B(B(B(B(B(B({},"".concat(n,"-rtl"),u==="rtl"),"".concat(n,"-ping-left"),Ie),"".concat(n,"-ping-right"),De),"".concat(n,"-layout-fixed"),v==="fixed"),"".concat(n,"-fixed-header"),Ge),"".concat(n,"-fixed-column"),Ye),"".concat(n,"-fixed-column-gapped"),Ye&&ye),"".concat(n,"-scroll-horizontal"),je),"".concat(n,"-has-fix-left"),j[0]&&j[0].fixed),"".concat(n,"-has-fix-right"),j[j.length-1]&&j[j.length-1].fixed==="right")),style:d,id:g,ref:Pe},ia),p&&l.createElement(Ir,{className:"".concat(n,"-title")},p(S)),l.createElement("div",{ref:de,className:"".concat(n,"-container")},vn),f&&l.createElement(Ir,{className:"".concat(n,"-footer")},f(S)));je&&(gn=l.createElement(Hn,{onResize:ta},gn));var pr=Ri(j,Ze,u),sa=l.useMemo(function(){return{scrollX:ie,prefixCls:n,getComponent:_,scrollbarSize:Jt,direction:u,fixedInfoList:pr,isSticky:at,supportSticky:lr,componentWidth:ge,fixHeader:Ge,fixColumn:Ye,horizonScroll:je,tableLayout:Qt,rowClassName:a,expandedRowClassName:Z.expandedRowClassName,expandIcon:G,expandableType:Ce,expandRowByClick:Z.expandRowByClick,expandedRowRender:Z.expandedRowRender,expandedRowOffset:Z.expandedRowOffset,onTriggerExpand:F,expandIconColumnIndex:Z.expandIconColumnIndex,indentSize:Z.indentSize,allColumnsFixedLeft:j.every(function(se){return se.fixed==="left"}),emptyNode:sr,columns:L,flattenColumns:j,onColumnResize:tt,colWidths:Qe,hoverStartRow:ee,hoverEndRow:be,onHover:Ee,rowExpandable:Z.rowExpandable,onRow:w,getRowKey:M,expandedKeys:me,childrenColumnName:X,rowHoverable:$}},[ie,n,_,Jt,u,pr,at,lr,ge,Ge,Ye,je,Qt,a,Z.expandedRowClassName,G,Ce,Z.expandRowByClick,Z.expandedRowRender,Z.expandedRowOffset,F,Z.expandIconColumnIndex,Z.indentSize,sr,L,j,tt,Qe,ee,be,Ee,Z.rowExpandable,w,M,me,X,$]);return l.createElement(Xe.Provider,{value:sa},gn)}var zi=l.forwardRef(Hi);function Ho(e){return ko(zi,e)}var Bt=Ho();Bt.EXPAND_COLUMN=ct;Bt.INTERNAL_HOOKS=Xt;Bt.Column=ri;Bt.ColumnGroup=oi;Bt.Summary=Io;var Gn=An(null),zo=An(null);function Fi(e,t,r){var n=t||1;return r[e+n]-(r[e]||0)}function ji(e){var t=e.rowInfo,r=e.column,n=e.colIndex,o=e.indent,a=e.index,d=e.component,i=e.renderIndex,c=e.record,s=e.style,v=e.className,u=e.inverse,p=e.getHeight,f=r.render,m=r.dataIndex,y=r.className,g=r.width,h=ze(zo,["columnsOffset"]),C=h.columnsOffset,x=Mo(t,r,n,o,a),w=x.key,E=x.fixedInfo,k=x.appendCellNode,N=x.additionalCellProps,R=N.style,b=N.colSpan,K=b===void 0?1:b,I=N.rowSpan,D=I===void 0?1:I,P=n-1,$=Fi(P,K,C),S=K>1?g-$:0,O=z(z(z({},R),s),{},{flex:"0 0 ".concat($,"px"),width:"".concat($,"px"),marginRight:S,pointerEvents:"auto"}),T=l.useMemo(function(){return u?D<=1:K===0||D===0||D>1},[D,K,u]);T?O.visibility="hidden":u&&(O.height=p==null?void 0:p(D));var _=T?function(){return null}:f,M={};return(D===0||K===0)&&(M.rowSpan=1,M.colSpan=1),l.createElement(Mt,pe({className:J(y,v),ellipsis:r.ellipsis,align:r.align,scope:r.rowScope,component:d,prefixCls:t.prefixCls,key:w,record:c,index:a,renderIndex:i,dataIndex:m,render:_,shouldCellUpdate:r.shouldCellUpdate},E,{appendNode:k,additionalProps:z(z({},N),{},{style:O},M)}))}var Ai=["data","index","className","rowKey","style","extra","getHeight"],Wi=l.forwardRef(function(e,t){var r=e.data,n=e.index,o=e.className,a=e.rowKey,d=e.style,i=e.extra,c=e.getHeight,s=rt(e,Ai),v=r.record,u=r.indent,p=r.index,f=ze(Xe,["prefixCls","flattenColumns","fixColumn","componentWidth","scrollX"]),m=f.scrollX,y=f.flattenColumns,g=f.prefixCls,h=f.fixColumn,C=f.componentWidth,x=ze(Gn,["getComponent"]),w=x.getComponent,E=Po(v,a,n,u),k=w(["body","row"],"div"),N=w(["body","cell"],"div"),R=E.rowSupportExpand,b=E.expanded,K=E.rowProps,I=E.expandedRowRender,D=E.expandedRowClassName,P;if(R&&b){var $=I(v,n,u+1,b),S=Do(D,v,n,u),O={};h&&(O={style:B({},"--virtual-width","".concat(C,"px"))});var T="".concat(g,"-expanded-row-cell");P=l.createElement(k,{className:J("".concat(g,"-expanded-row"),"".concat(g,"-expanded-row-level-").concat(u+1),S)},l.createElement(Mt,{component:N,prefixCls:g,className:J(T,B({},"".concat(T,"-fixed"),h)),additionalProps:O},$))}var _=z(z({},d),{},{width:m});i&&(_.position="absolute",_.pointerEvents="none");var M=l.createElement(k,pe({},K,s,{"data-row-key":a,ref:R?null:t,className:J(o,"".concat(g,"-row"),K==null?void 0:K.className,B({},"".concat(g,"-row-extra"),i)),style:z(z({},_),K==null?void 0:K.style)}),y.map(function(A,Y){return l.createElement(ji,{key:Y,component:N,rowInfo:E,column:A,colIndex:Y,indent:u,index:n,renderIndex:p,record:v,inverse:i,getHeight:c})}));return R?l.createElement("div",{ref:t},M,P):M}),Kr=Dt(Wi),Vi=l.forwardRef(function(e,t){var r=e.data,n=e.onScroll,o=ze(Xe,["flattenColumns","onColumnResize","getRowKey","prefixCls","expandedKeys","childrenColumnName","scrollX","direction"]),a=o.flattenColumns,d=o.onColumnResize,i=o.getRowKey,c=o.expandedKeys,s=o.prefixCls,v=o.childrenColumnName,u=o.scrollX,p=o.direction,f=ze(Gn),m=f.sticky,y=f.scrollY,g=f.listItemHeight,h=f.getComponent,C=f.onScroll,x=l.useRef(),w=Ko(r,v,c,i),E=l.useMemo(function(){var P=0;return a.map(function($){var S=$.width,O=$.key;return P+=S,[O,S,P]})},[a]),k=l.useMemo(function(){return E.map(function(P){return P[2]})},[E]);l.useEffect(function(){E.forEach(function(P){var $=Se(P,2),S=$[0],O=$[1];d(S,O)})},[E]),l.useImperativeHandle(t,function(){var P,$={scrollTo:function(O){var T;(T=x.current)===null||T===void 0||T.scrollTo(O)},nativeElement:(P=x.current)===null||P===void 0?void 0:P.nativeElement};return Object.defineProperty($,"scrollLeft",{get:function(){var O;return((O=x.current)===null||O===void 0?void 0:O.getScrollInfo().x)||0},set:function(O){var T;(T=x.current)===null||T===void 0||T.scrollTo({left:O})}}),$});var N=function($,S){var O,T=(O=w[S])===null||O===void 0?void 0:O.record,_=$.onCell;if(_){var M,A=_(T,S);return(M=A==null?void 0:A.rowSpan)!==null&&M!==void 0?M:1}return 1},R=function($){var S=$.start,O=$.end,T=$.getSize,_=$.offsetY;if(O<0)return null;for(var M=a.filter(function(G){return N(G,S)===0}),A=S,Y=function(X){if(M=M.filter(function(F){return N(F,X)===0}),!M.length)return A=X,1},q=S;q>=0&&!Y(q);q-=1);for(var ee=a.filter(function(G){return N(G,O)!==1}),be=O,Ee=function(X){if(ee=ee.filter(function(F){return N(F,X)!==1}),!ee.length)return be=Math.max(X-1,O),1},we=O;we<w.length&&!Ee(we);we+=1);for(var Q=[],Z=function(X){var F=w[X];if(!F)return 1;a.some(function(W){return N(W,X)>1})&&Q.push(X)},Ce=A;Ce<=be;Ce+=1)Z(Ce);var me=Q.map(function(G){var X=w[G],F=i(X.record,G),W=function(ge){var ke=G+ge-1,Fe=i(w[ke].record,ke),Re=T(F,Fe);return Re.bottom-Re.top},te=T(F);return l.createElement(Kr,{key:G,data:X,rowKey:F,index:G,style:{top:-_+te.top},extra:!0,getHeight:W})});return me},b=l.useMemo(function(){return{columnsOffset:k}},[k]),K="".concat(s,"-tbody"),I=h(["body","wrapper"]),D={};return m&&(D.position="sticky",D.bottom=0,it(m)==="object"&&m.offsetScroll&&(D.bottom=m.offsetScroll)),l.createElement(zo.Provider,{value:b},l.createElement(uo,{fullHeight:!1,ref:x,prefixCls:"".concat(K,"-virtual"),styles:{horizontalScrollBar:D},className:K,height:y,itemHeight:g||24,data:w,itemKey:function($){return i($.record)},component:I,scrollWidth:u,direction:p,onVirtualScroll:function($){var S,O=$.x;n({currentTarget:(S=x.current)===null||S===void 0?void 0:S.nativeElement,scrollLeft:O})},onScroll:C,extraRender:R},function(P,$,S){var O=i(P.record,$);return l.createElement(Kr,{data:P,rowKey:O,index:$,style:S.style})}))}),qi=Dt(Vi),Gi=function(t,r){var n=r.ref,o=r.onScroll;return l.createElement(qi,{ref:n,data:t,onScroll:o})};function Xi(e,t){var r=e.data,n=e.columns,o=e.scroll,a=e.sticky,d=e.prefixCls,i=d===void 0?_o:d,c=e.className,s=e.listItemHeight,v=e.components,u=e.onScroll,p=o||{},f=p.x,m=p.y;typeof f!="number"&&(f=1),typeof m!="number"&&(m=500);var y=bt(function(C,x){return _n(v,C)||x}),g=bt(u),h=l.useMemo(function(){return{sticky:a,scrollY:m,listItemHeight:s,getComponent:y,onScroll:g}},[a,m,s,y,g]);return l.createElement(Gn.Provider,{value:h},l.createElement(Bt,pe({},e,{className:J(c,"".concat(i,"-virtual")),scroll:z(z({},o),{},{x:f}),components:z(z({},v),{},{body:r!=null&&r.length?Gi:void 0}),columns:n,internalHooks:Xt,tailor:!0,ref:t})))}var Ui=l.forwardRef(Xi);function Fo(e){return ko(Ui,e)}Fo();const Yi=e=>null,Ji=e=>null;var Xn=l.createContext(null),Qi=l.createContext({}),Zi=function(t){for(var r=t.prefixCls,n=t.level,o=t.isStart,a=t.isEnd,d="".concat(r,"-indent-unit"),i=[],c=0;c<n;c+=1)i.push(l.createElement("span",{key:c,className:J(d,B(B({},"".concat(d,"-start"),o[c]),"".concat(d,"-end"),a[c]))}));return l.createElement("span",{"aria-hidden":"true",className:"".concat(r,"-indent")},i)};const es=l.memo(Zi);var ts=["eventKey","className","style","dragOver","dragOverGapTop","dragOverGapBottom","isLeaf","isStart","isEnd","expanded","selected","checked","halfChecked","loading","domRef","active","data","onMouseMove","selectable"],Pr="open",Tr="close",ns="---",Wt=function(t){var r,n,o,a=t.eventKey,d=t.className,i=t.style,c=t.dragOver,s=t.dragOverGapTop,v=t.dragOverGapBottom,u=t.isLeaf,p=t.isStart,f=t.isEnd,m=t.expanded,y=t.selected,g=t.checked,h=t.halfChecked,C=t.loading,x=t.domRef,w=t.active,E=t.data,k=t.onMouseMove,N=t.selectable,R=rt(t,ts),b=ve.useContext(Xn),K=ve.useContext(Qi),I=ve.useRef(null),D=ve.useState(!1),P=Se(D,2),$=P[0],S=P[1],O=!!(b.disabled||t.disabled||(r=K.nodeDisabled)!==null&&r!==void 0&&r.call(K,E)),T=ve.useMemo(function(){return!b.checkable||t.checkable===!1?!1:b.checkable},[b.checkable,t.checkable]),_=function(H){O||b.onNodeSelect(H,Me(t))},M=function(H){O||!T||t.disableCheckbox||b.onNodeCheck(H,Me(t),!g)},A=ve.useMemo(function(){return typeof N=="boolean"?N:b.selectable},[N,b.selectable]),Y=function(H){b.onNodeClick(H,Me(t)),A?_(H):M(H)},q=function(H){b.onNodeDoubleClick(H,Me(t))},ee=function(H){b.onNodeMouseEnter(H,Me(t))},be=function(H){b.onNodeMouseLeave(H,Me(t))},Ee=function(H){b.onNodeContextMenu(H,Me(t))},we=ve.useMemo(function(){return!!(b.draggable&&(!b.draggable.nodeDraggable||b.draggable.nodeDraggable(E)))},[b.draggable,E]),Q=function(H){H.stopPropagation(),S(!0),b.onNodeDragStart(H,t);try{H.dataTransfer.setData("text/plain","")}catch{}},Z=function(H){H.preventDefault(),H.stopPropagation(),b.onNodeDragEnter(H,t)},Ce=function(H){H.preventDefault(),H.stopPropagation(),b.onNodeDragOver(H,t)},me=function(H){H.stopPropagation(),b.onNodeDragLeave(H,t)},G=function(H){H.stopPropagation(),S(!1),b.onNodeDragEnd(H,t)},X=function(H){H.preventDefault(),H.stopPropagation(),S(!1),b.onNodeDrop(H,t)},F=function(H){C||b.onNodeExpand(H,Me(t))},W=ve.useMemo(function(){var U=Je(b.keyEntities,a)||{},H=U.children;return!!(H||[]).length},[b.keyEntities,a]),te=ve.useMemo(function(){return u===!1?!1:u||!b.loadData&&!W||b.loadData&&t.loaded&&!W},[u,b.loadData,W,t.loaded]);ve.useEffect(function(){C||typeof b.loadData=="function"&&m&&!te&&!t.loaded&&b.onNodeLoad(Me(t))},[C,b.loadData,b.onNodeLoad,m,te,t]);var le=ve.useMemo(function(){var U;return(U=b.draggable)!==null&&U!==void 0&&U.icon?ve.createElement("span",{className:"".concat(b.prefixCls,"-draggable-icon")},b.draggable.icon):null},[b.draggable]),ge=function(H){var $e=t.switcherIcon||b.switcherIcon;return typeof $e=="function"?$e(z(z({},t),{},{isLeaf:H})):$e},ke=function(){if(te){var H=ge(!0);return H!==!1?ve.createElement("span",{className:J("".concat(b.prefixCls,"-switcher"),"".concat(b.prefixCls,"-switcher-noop"))},H):null}var $e=ge(!1);return $e!==!1?ve.createElement("span",{onClick:F,className:J("".concat(b.prefixCls,"-switcher"),"".concat(b.prefixCls,"-switcher_").concat(m?Pr:Tr))},$e):null},Fe=ve.useMemo(function(){if(!T)return null;var U=typeof T!="boolean"?T:null;return ve.createElement("span",{className:J("".concat(b.prefixCls,"-checkbox"),B(B(B({},"".concat(b.prefixCls,"-checkbox-checked"),g),"".concat(b.prefixCls,"-checkbox-indeterminate"),!g&&h),"".concat(b.prefixCls,"-checkbox-disabled"),O||t.disableCheckbox)),onClick:M,role:"checkbox","aria-checked":h?"mixed":g,"aria-disabled":O||t.disableCheckbox,"aria-label":"Select ".concat(typeof t.title=="string"?t.title:"tree node")},U)},[T,g,h,O,t.disableCheckbox,t.title]),Re=ve.useMemo(function(){return te?null:m?Pr:Tr},[te,m]),L=ve.useMemo(function(){return ve.createElement("span",{className:J("".concat(b.prefixCls,"-iconEle"),"".concat(b.prefixCls,"-icon__").concat(Re||"docu"),B({},"".concat(b.prefixCls,"-icon_loading"),C))})},[b.prefixCls,Re,C]),j=ve.useMemo(function(){var U=!!b.draggable,H=!t.disabled&&U&&b.dragOverNodeKey===a;return H?b.dropIndicatorRender({dropPosition:b.dropPosition,dropLevelOffset:b.dropLevelOffset,indent:b.indent,prefixCls:b.prefixCls,direction:b.direction}):null},[b.dropPosition,b.dropLevelOffset,b.indent,b.prefixCls,b.direction,b.draggable,b.dragOverNodeKey,b.dropIndicatorRender]),re=ve.useMemo(function(){var U=t.title,H=U===void 0?ns:U,$e="".concat(b.prefixCls,"-node-content-wrapper"),Ie;if(b.showIcon){var ne=t.icon||b.icon;Ie=ne?ve.createElement("span",{className:J("".concat(b.prefixCls,"-iconEle"),"".concat(b.prefixCls,"-icon__customize"))},typeof ne=="function"?ne(t):ne):L}else b.loadData&&C&&(Ie=L);var Te;return typeof H=="function"?Te=H(E):b.titleRender?Te=b.titleRender(E):Te=H,ve.createElement("span",{ref:I,title:typeof H=="string"?H:"",className:J($e,"".concat($e,"-").concat(Re||"normal"),B({},"".concat(b.prefixCls,"-node-selected"),!O&&(y||$))),onMouseEnter:ee,onMouseLeave:be,onContextMenu:Ee,onClick:Y,onDoubleClick:q},Ie,ve.createElement("span",{className:"".concat(b.prefixCls,"-title")},Te),j)},[b.prefixCls,b.showIcon,t,b.icon,L,b.titleRender,E,Re,ee,be,Ee,Y,q]),ye=At(R,{aria:!0,data:!0}),ie=Je(b.keyEntities,a)||{},Ne=ie.level,Pe=f[f.length-1],Le=!O&&we,he=b.draggingNodeKey===a,de=N!==void 0?{"aria-selected":!!N}:void 0;return ve.createElement("div",pe({ref:x,role:"treeitem","aria-expanded":u?void 0:m,className:J(d,"".concat(b.prefixCls,"-treenode"),(o={},B(B(B(B(B(B(B(B(B(B(o,"".concat(b.prefixCls,"-treenode-disabled"),O),"".concat(b.prefixCls,"-treenode-switcher-").concat(m?"open":"close"),!u),"".concat(b.prefixCls,"-treenode-checkbox-checked"),g),"".concat(b.prefixCls,"-treenode-checkbox-indeterminate"),h),"".concat(b.prefixCls,"-treenode-selected"),y),"".concat(b.prefixCls,"-treenode-loading"),C),"".concat(b.prefixCls,"-treenode-active"),w),"".concat(b.prefixCls,"-treenode-leaf-last"),Pe),"".concat(b.prefixCls,"-treenode-draggable"),we),"dragging",he),B(B(B(B(B(B(B(o,"drop-target",b.dropTargetKey===a),"drop-container",b.dropContainerKey===a),"drag-over",!O&&c),"drag-over-gap-top",!O&&s),"drag-over-gap-bottom",!O&&v),"filter-node",(n=b.filterTreeNode)===null||n===void 0?void 0:n.call(b,Me(t))),"".concat(b.prefixCls,"-treenode-leaf"),te))),style:i,draggable:Le,onDragStart:Le?Q:void 0,onDragEnter:we?Z:void 0,onDragOver:we?Ce:void 0,onDragLeave:we?me:void 0,onDrop:we?X:void 0,onDragEnd:we?G:void 0,onMouseMove:k},de,ye),ve.createElement(es,{prefixCls:b.prefixCls,level:Ne,isStart:p,isEnd:f}),le,ke(),Fe,re)};Wt.isTreeNode=1;function lt(e,t){if(!e)return[];var r=e.slice(),n=r.indexOf(t);return n>=0&&r.splice(n,1),r}function st(e,t){var r=(e||[]).slice();return r.indexOf(t)===-1&&r.push(t),r}function Un(e){return e.split("-")}function rs(e,t){var r=[],n=Je(t,e);function o(){var a=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[];a.forEach(function(d){var i=d.key,c=d.children;r.push(i),o(c)})}return o(n.children),r}function os(e){if(e.parent){var t=Un(e.pos);return Number(t[t.length-1])===e.parent.children.length-1}return!1}function as(e){var t=Un(e.pos);return Number(t[t.length-1])===0}function Dr(e,t,r,n,o,a,d,i,c,s){var v,u=e.clientX,p=e.clientY,f=e.target.getBoundingClientRect(),m=f.top,y=f.height,g=(s==="rtl"?-1:1)*(((o==null?void 0:o.x)||0)-u),h=(g-12)/n,C=c.filter(function(O){var T;return(T=i[O])===null||T===void 0||(T=T.children)===null||T===void 0?void 0:T.length}),x=Je(i,r.eventKey);if(p<m+y/2){var w=d.findIndex(function(O){return O.key===x.key}),E=w<=0?0:w-1,k=d[E].key;x=Je(i,k)}var N=x.key,R=x,b=x.key,K=0,I=0;if(!C.includes(N))for(var D=0;D<h&&os(x);D+=1)x=x.parent,I+=1;var P=t.data,$=x.node,S=!0;return as(x)&&x.level===0&&p<m+y/2&&a({dragNode:P,dropNode:$,dropPosition:-1})&&x.key===r.eventKey?K=-1:(R.children||[]).length&&C.includes(b)?a({dragNode:P,dropNode:$,dropPosition:0})?K=0:S=!1:I===0?h>-1.5?a({dragNode:P,dropNode:$,dropPosition:1})?K=1:S=!1:a({dragNode:P,dropNode:$,dropPosition:0})?K=0:a({dragNode:P,dropNode:$,dropPosition:1})?K=1:S=!1:a({dragNode:P,dropNode:$,dropPosition:1})?K=1:S=!1,{dropPosition:K,dropLevelOffset:I,dropTargetKey:x.key,dropTargetPos:x.pos,dragOverNodeKey:b,dropContainerKey:K===0?null:((v=x.parent)===null||v===void 0?void 0:v.key)||null,dropAllowed:S}}function Mr(e,t){if(e){var r=t.multiple;return r?e.slice():e.length?[e[0]]:e}}function yn(e){if(!e)return null;var t;if(Array.isArray(e))t={checkedKeys:e,halfCheckedKeys:void 0};else if(it(e)==="object")t={checkedKeys:e.checked||void 0,halfCheckedKeys:e.halfChecked||void 0};else return gt(!1,"`checkedKeys` is not an array or an object"),null;return t}function Nn(e,t){var r=new Set;function n(o){if(!r.has(o)){var a=Je(t,o);if(a){r.add(o);var d=a.parent,i=a.node;i.disabled||d&&n(d.key)}}}return(e||[]).forEach(function(o){n(o)}),ce(r)}function ls(e){const[t,r]=l.useState(null);return[l.useCallback((a,d,i)=>{const c=t??a,s=Math.min(c||0,a),v=Math.max(c||0,a),u=d.slice(s,v+1).map(m=>e(m)),p=u.some(m=>!i.has(m)),f=[];return u.forEach(m=>{p?(i.has(m)||f.push(m),i.add(m)):(i.delete(m),f.push(m))}),r(p?v:null),f},[t]),a=>{r(a)}]}const pt={},Rn="SELECT_ALL",In="SELECT_INVERT",On="SELECT_NONE",Br=[],jo=(e,t)=>{let r=[];return(t||[]).forEach(n=>{r.push(n),n&&typeof n=="object"&&e in n&&(r=[].concat(ce(r),ce(jo(e,n[e]))))}),r},is=(e,t)=>{const{preserveSelectedRowKeys:r,selectedRowKeys:n,defaultSelectedRowKeys:o,getCheckboxProps:a,onChange:d,onSelect:i,onSelectAll:c,onSelectInvert:s,onSelectNone:v,onSelectMultiple:u,columnWidth:p,type:f,selections:m,fixed:y,renderCell:g,hideSelectAll:h,checkStrictly:C=!0}=t||{},{prefixCls:x,data:w,pageData:E,getRecordByKey:k,getRowKey:N,expandType:R,childrenColumnName:b,locale:K,getPopupContainer:I}=e,D=Fn(),[P,$]=ls(G=>G),[S,O]=Bn(n||o||Br,{value:n}),T=l.useRef(new Map),_=l.useCallback(G=>{if(r){const X=new Map;G.forEach(F=>{let W=k(F);!W&&T.current.has(F)&&(W=T.current.get(F)),X.set(F,W)}),T.current=X}},[k,r]);l.useEffect(()=>{_(S)},[S]);const M=l.useMemo(()=>jo(b,E),[b,E]),{keyEntities:A}=l.useMemo(()=>{if(C)return{keyEntities:null};let G=w;if(r){const X=new Set(M.map((W,te)=>N(W,te))),F=Array.from(T.current).reduce((W,[te,le])=>X.has(te)?W:W.concat(le),[]);G=[].concat(ce(G),ce(F))}return jn(G,{externalGetKey:N,childrenPropName:b})},[w,N,C,b,r,M]),Y=l.useMemo(()=>{const G=new Map;return M.forEach((X,F)=>{const W=N(X,F),te=(a?a(X):null)||{};G.set(W,te)}),G},[M,N,a]),q=l.useCallback(G=>{const X=N(G);let F;return Y.has(X)?F=Y.get(N(G)):F=a?a(G):void 0,!!(F!=null&&F.disabled)},[Y,N]),[ee,be]=l.useMemo(()=>{if(C)return[S||[],[]];const{checkedKeys:G,halfCheckedKeys:X}=Ot(S,!0,A,q);return[G||[],X]},[S,C,A,q]),Ee=l.useMemo(()=>{const G=f==="radio"?ee.slice(0,1):ee;return new Set(G)},[ee,f]),we=l.useMemo(()=>f==="radio"?new Set:new Set(be),[be,f]);l.useEffect(()=>{t||O(Br)},[!!t]);const Q=l.useCallback((G,X)=>{let F,W;_(G),r?(F=G,W=G.map(te=>T.current.get(te))):(F=[],W=[],G.forEach(te=>{const le=k(te);le!==void 0&&(F.push(te),W.push(le))})),O(F),d==null||d(F,W,{type:X})},[O,k,d,r]),Z=l.useCallback((G,X,F,W)=>{if(i){const te=F.map(le=>k(le));i(k(G),X,te,W)}Q(F,"single")},[i,k,Q]),Ce=l.useMemo(()=>!m||h?null:(m===!0?[Rn,In,On]:m).map(X=>X===Rn?{key:"all",text:K.selectionAll,onSelect(){Q(w.map((F,W)=>N(F,W)).filter(F=>{const W=Y.get(F);return!(W!=null&&W.disabled)||Ee.has(F)}),"all")}}:X===In?{key:"invert",text:K.selectInvert,onSelect(){const F=new Set(Ee);E.forEach((te,le)=>{const ge=N(te,le),ke=Y.get(ge);ke!=null&&ke.disabled||(F.has(ge)?F.delete(ge):F.add(ge))});const W=Array.from(F);s&&(D.deprecated(!1,"onSelectInvert","onChange"),s(W)),Q(W,"invert")}}:X===On?{key:"none",text:K.selectNone,onSelect(){v==null||v(),Q(Array.from(Ee).filter(F=>{const W=Y.get(F);return W==null?void 0:W.disabled}),"none")}}:X).map(X=>Object.assign(Object.assign({},X),{onSelect:(...F)=>{var W,te;(te=X.onSelect)===null||te===void 0||(W=te).call.apply(W,[X].concat(F)),$(null)}})),[m,Ee,E,N,s,Q]);return[l.useCallback(G=>{var X;if(!t)return G.filter(de=>de!==pt);let F=ce(G);const W=new Set(Ee),te=M.map(N).filter(de=>!Y.get(de).disabled),le=te.every(de=>W.has(de)),ge=te.some(de=>W.has(de)),ke=()=>{const de=[];le?te.forEach(H=>{W.delete(H),de.push(H)}):te.forEach(H=>{W.has(H)||(W.add(H),de.push(H))});const U=Array.from(W);c==null||c(!le,U.map(H=>k(H)),de.map(H=>k(H))),Q(U,"all"),$(null)};let Fe,Re;if(f!=="radio"){let de;if(Ce){const ne={getPopupContainer:I,items:Ce.map((Te,xe)=>{const{key:De,text:Ue,onSelect:Ve}=Te;return{key:De??xe,onClick:()=>{Ve==null||Ve(te)},label:Ue}})};de=l.createElement("div",{className:`${x}-selection-extra`},l.createElement(so,{menu:ne,getPopupContainer:I},l.createElement("span",null,l.createElement(Ta,null))))}const U=M.map((ne,Te)=>{const xe=N(ne,Te),De=Y.get(xe)||{};return Object.assign({checked:W.has(xe)},De)}).filter(({disabled:ne})=>ne),H=!!U.length&&U.length===M.length,$e=H&&U.every(({checked:ne})=>ne),Ie=H&&U.some(({checked:ne})=>ne);Re=l.createElement(Tt,{checked:H?$e:!!M.length&&le,indeterminate:H?!$e&&Ie:!le&&ge,onChange:ke,disabled:M.length===0||H,"aria-label":de?"Custom selection":"Select all",skipGroup:!0}),Fe=!h&&l.createElement("div",{className:`${x}-selection`},Re,de)}let L;f==="radio"?L=(de,U,H)=>{const $e=N(U,H),Ie=W.has($e),ne=Y.get($e);return{node:l.createElement(qt,Object.assign({},ne,{checked:Ie,onClick:Te=>{var xe;Te.stopPropagation(),(xe=ne==null?void 0:ne.onClick)===null||xe===void 0||xe.call(ne,Te)},onChange:Te=>{var xe;W.has($e)||Z($e,!0,[$e],Te.nativeEvent),(xe=ne==null?void 0:ne.onChange)===null||xe===void 0||xe.call(ne,Te)}})),checked:Ie}}:L=(de,U,H)=>{var $e;const Ie=N(U,H),ne=W.has(Ie),Te=we.has(Ie),xe=Y.get(Ie);let De;return R==="nest"?De=Te:De=($e=xe==null?void 0:xe.indeterminate)!==null&&$e!==void 0?$e:Te,{node:l.createElement(Tt,Object.assign({},xe,{indeterminate:De,checked:ne,skipGroup:!0,onClick:Ue=>{var Ve;Ue.stopPropagation(),(Ve=xe==null?void 0:xe.onClick)===null||Ve===void 0||Ve.call(xe,Ue)},onChange:Ue=>{var Ve;const{nativeEvent:ft}=Ue,{shiftKey:Oe}=ft,kt=te.findIndex(qe=>qe===Ie),Nt=ee.some(qe=>te.includes(qe));if(Oe&&C&&Nt){const qe=P(kt,te,W),Qe=Array.from(W);u==null||u(!ne,Qe.map(Ze=>k(Ze)),qe.map(Ze=>k(Ze))),Q(Qe,"multiple")}else{const qe=ee;if(C){const Qe=ne?lt(qe,Ie):st(qe,Ie);Z(Ie,!ne,Qe,ft)}else{const Qe=Ot([].concat(ce(qe),[Ie]),!0,A,q),{checkedKeys:Ze,halfCheckedKeys:Ge}=Qe;let je=Ze;if(ne){const Ye=new Set(Ze);Ye.delete(Ie),je=Ot(Array.from(Ye),{halfCheckedKeys:Ge},A,q).checkedKeys}Z(Ie,!ne,je,ft)}}$(ne?null:kt),(Ve=xe==null?void 0:xe.onChange)===null||Ve===void 0||Ve.call(xe,Ue)}})),checked:ne}};const j=(de,U,H)=>{const{node:$e,checked:Ie}=L(de,U,H);return g?g(Ie,U,H,$e):$e};if(!F.includes(pt))if(F.findIndex(de=>{var U;return((U=de[Ft])===null||U===void 0?void 0:U.columnType)==="EXPAND_COLUMN"})===0){const[de,...U]=F;F=[de,pt].concat(ce(U))}else F=[pt].concat(ce(F));const re=F.indexOf(pt);F=F.filter((de,U)=>de!==pt||U===re);const ye=F[re-1],ie=F[re+1];let Ne=y;Ne===void 0&&((ie==null?void 0:ie.fixed)!==void 0?Ne=ie.fixed:(ye==null?void 0:ye.fixed)!==void 0&&(Ne=ye.fixed)),Ne&&ye&&((X=ye[Ft])===null||X===void 0?void 0:X.columnType)==="EXPAND_COLUMN"&&ye.fixed===void 0&&(ye.fixed=Ne);const Pe=J(`${x}-selection-col`,{[`${x}-selection-col-with-dropdown`]:m&&f==="checkbox"}),Le=()=>t!=null&&t.columnTitle?typeof t.columnTitle=="function"?t.columnTitle(Re):t.columnTitle:Fe,he={fixed:Ne,width:p,className:`${x}-selection-column`,title:Le(),render:j,onCell:t.onCell,align:t.align,[Ft]:{className:Pe}};return F.map(de=>de===pt?he:de)},[N,M,t,ee,Ee,we,p,Ce,R,Y,u,Z,q]),Ee]};function ss(e,t){return e._antProxy=e._antProxy||{},Object.keys(t).forEach(r=>{if(!(r in e._antProxy)){const n=e[r];e._antProxy[r]=n,e[r]=t[r]}}),e}function cs(e,t){return l.useImperativeHandle(e,()=>{const r=t(),{nativeElement:n}=r;return typeof Proxy<"u"?new Proxy(n,{get(o,a){return r[a]?r[a]:Reflect.get(o,a)}}):ss(n,r)})}function ds(e){return t=>{const{prefixCls:r,onExpand:n,record:o,expanded:a,expandable:d}=t,i=`${r}-row-expand-icon`;return l.createElement("button",{type:"button",onClick:c=>{n(o,c),c.stopPropagation()},className:J(i,{[`${i}-spaced`]:!d,[`${i}-expanded`]:d&&a,[`${i}-collapsed`]:d&&!a}),"aria-label":a?e.collapse:e.expand,"aria-expanded":a})}}function us(e){return(r,n)=>{const o=r.querySelector(`.${e}-container`);let a=n;if(o){const d=getComputedStyle(o),i=parseInt(d.borderLeftWidth,10),c=parseInt(d.borderRightWidth,10);a=n-i-c}return a}}const yt=(e,t)=>"key"in e&&e.key!==void 0&&e.key!==null?e.key:e.dataIndex?Array.isArray(e.dataIndex)?e.dataIndex.join("."):e.dataIndex:t;function Lt(e,t){return t?`${t}-${e}`:`${e}`}const cn=(e,t)=>typeof e=="function"?e(t):e,fs=(e,t)=>{const r=cn(e,t);return Object.prototype.toString.call(r)==="[object Object]"?"":r};function vs(e){const t=l.useRef(e),r=pa();return[()=>t.current,n=>{t.current=n,r()}]}var ps=function(t){var r=t.dropPosition,n=t.dropLevelOffset,o=t.indent,a={pointerEvents:"none",position:"absolute",right:0,backgroundColor:"red",height:2};switch(r){case-1:a.top=0,a.left=-n*o;break;case 1:a.bottom=0,a.left=-n*o;break;case 0:a.bottom=0,a.left=o;break}return ve.createElement("div",{style:a})};function Ao(e){if(e==null)throw new TypeError("Cannot destructure "+e)}function ms(e,t){var r=l.useState(!1),n=Se(r,2),o=n[0],a=n[1];ht(function(){if(o)return e(),function(){t()}},[o]),ht(function(){return a(!0),function(){a(!1)}},[])}var gs=["className","style","motion","motionNodes","motionType","onMotionStart","onMotionEnd","active","treeNodeRequiredProps"],hs=l.forwardRef(function(e,t){var r=e.className,n=e.style,o=e.motion,a=e.motionNodes,d=e.motionType,i=e.onMotionStart,c=e.onMotionEnd,s=e.active,v=e.treeNodeRequiredProps,u=rt(e,gs),p=l.useState(!0),f=Se(p,2),m=f[0],y=f[1],g=l.useContext(Xn),h=g.prefixCls,C=a&&d!=="hide";ht(function(){a&&C!==m&&y(C)},[a]);var x=function(){a&&i()},w=l.useRef(!1),E=function(){a&&!w.current&&(w.current=!0,c())};ms(x,E);var k=function(R){C===R&&E()};return a?l.createElement(ma,pe({ref:t,visible:m},o,{motionAppear:d==="show",onVisibleChanged:k}),function(N,R){var b=N.className,K=N.style;return l.createElement("div",{ref:R,className:J("".concat(h,"-treenode-motion"),b),style:K},a.map(function(I){var D=Object.assign({},(Ao(I.data),I.data)),P=I.title,$=I.key,S=I.isStart,O=I.isEnd;delete D.children;var T=zt($,v);return l.createElement(Wt,pe({},D,T,{title:P,active:s,data:I.data,key:$,isStart:S,isEnd:O}))}))}):l.createElement(Wt,pe({domRef:t,className:r,style:n},u,{active:s}))});function bs(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:[],r=e.length,n=t.length;if(Math.abs(r-n)!==1)return{add:!1,key:null};function o(a,d){var i=new Map;a.forEach(function(s){i.set(s,!0)});var c=d.filter(function(s){return!i.has(s)});return c.length===1?c[0]:null}return r<n?{add:!0,key:o(e,t)}:{add:!1,key:o(t,e)}}function Lr(e,t,r){var n=e.findIndex(function(i){return i.key===r}),o=e[n+1],a=t.findIndex(function(i){return i.key===r});if(o){var d=t.findIndex(function(i){return i.key===o.key});return t.slice(a+1,d)}return t.slice(a+1)}var ys=["prefixCls","data","selectable","checkable","expandedKeys","selectedKeys","checkedKeys","loadedKeys","loadingKeys","halfCheckedKeys","keyEntities","disabled","dragging","dragOverNodeKey","dropPosition","motion","height","itemHeight","virtual","scrollWidth","focusable","activeItem","focused","tabIndex","onKeyDown","onFocus","onBlur","onActiveChange","onListChangeStart","onListChangeEnd"],_r={width:0,height:0,display:"flex",overflow:"hidden",opacity:0,border:0,padding:0,margin:0},xs=function(){},$t="RC_TREE_MOTION_".concat(Math.random()),Kn={key:$t},Wo={key:$t,level:0,index:0,pos:"0",node:Kn,nodes:[Kn]},Hr={parent:null,children:[],pos:Wo.pos,data:Kn,title:null,key:$t,isStart:[],isEnd:[]};function zr(e,t,r,n){return t===!1||!r?e:e.slice(0,Math.ceil(r/n)+1)}function Fr(e){var t=e.key,r=e.pos;return Gt(t,r)}function Cs(e){for(var t=String(e.data.key),r=e;r.parent;)r=r.parent,t="".concat(r.data.key," > ").concat(t);return t}var Ss=l.forwardRef(function(e,t){var r=e.prefixCls,n=e.data;e.selectable,e.checkable;var o=e.expandedKeys,a=e.selectedKeys,d=e.checkedKeys,i=e.loadedKeys,c=e.loadingKeys,s=e.halfCheckedKeys,v=e.keyEntities,u=e.disabled,p=e.dragging,f=e.dragOverNodeKey,m=e.dropPosition,y=e.motion,g=e.height,h=e.itemHeight,C=e.virtual,x=e.scrollWidth,w=e.focusable,E=e.activeItem,k=e.focused,N=e.tabIndex,R=e.onKeyDown,b=e.onFocus,K=e.onBlur,I=e.onActiveChange,D=e.onListChangeStart,P=e.onListChangeEnd,$=rt(e,ys),S=l.useRef(null),O=l.useRef(null);l.useImperativeHandle(t,function(){return{scrollTo:function(j){S.current.scrollTo(j)},getIndentWidth:function(){return O.current.offsetWidth}}});var T=l.useState(o),_=Se(T,2),M=_[0],A=_[1],Y=l.useState(n),q=Se(Y,2),ee=q[0],be=q[1],Ee=l.useState(n),we=Se(Ee,2),Q=we[0],Z=we[1],Ce=l.useState([]),me=Se(Ce,2),G=me[0],X=me[1],F=l.useState(null),W=Se(F,2),te=W[0],le=W[1],ge=l.useRef(n);ge.current=n;function ke(){var L=ge.current;be(L),Z(L),X([]),le(null),P()}ht(function(){A(o);var L=bs(M,o);if(L.key!==null)if(L.add){var j=ee.findIndex(function(Le){var he=Le.key;return he===L.key}),re=zr(Lr(ee,n,L.key),C,g,h),ye=ee.slice();ye.splice(j+1,0,Hr),Z(ye),X(re),le("show")}else{var ie=n.findIndex(function(Le){var he=Le.key;return he===L.key}),Ne=zr(Lr(n,ee,L.key),C,g,h),Pe=n.slice();Pe.splice(ie+1,0,Hr),Z(Pe),X(Ne),le("hide")}else ee!==n&&(be(n),Z(n))},[o,n]),l.useEffect(function(){p||ke()},[p]);var Fe=y?Q:n,Re={expandedKeys:o,selectedKeys:a,loadedKeys:i,loadingKeys:c,checkedKeys:d,halfCheckedKeys:s,dragOverNodeKey:f,dropPosition:m,keyEntities:v};return l.createElement(l.Fragment,null,k&&E&&l.createElement("span",{style:_r,"aria-live":"assertive"},Cs(E)),l.createElement("div",null,l.createElement("input",{style:_r,disabled:w===!1||u,tabIndex:w!==!1?N:null,onKeyDown:R,onFocus:b,onBlur:K,value:"",onChange:xs,"aria-label":"for screen reader"})),l.createElement("div",{className:"".concat(r,"-treenode"),"aria-hidden":!0,style:{position:"absolute",pointerEvents:"none",visibility:"hidden",height:0,overflow:"hidden",border:0,padding:0}},l.createElement("div",{className:"".concat(r,"-indent")},l.createElement("div",{ref:O,className:"".concat(r,"-indent-unit")}))),l.createElement(uo,pe({},$,{data:Fe,itemKey:Fr,height:g,fullHeight:!1,virtual:C,itemHeight:h,scrollWidth:x,prefixCls:"".concat(r,"-list"),ref:S,role:"tree",onVisibleChange:function(j){j.every(function(re){return Fr(re)!==$t})&&ke()}}),function(L){var j=L.pos,re=Object.assign({},(Ao(L.data),L.data)),ye=L.title,ie=L.key,Ne=L.isStart,Pe=L.isEnd,Le=Gt(ie,j);delete re.key,delete re.children;var he=zt(Le,Re);return l.createElement(hs,pe({},re,he,{title:ye,active:!!E&&ie===E.key,pos:j,data:L.data,isStart:Ne,isEnd:Pe,motion:y,motionNodes:ie===$t?G:null,motionType:te,onMotionStart:D,onMotionEnd:ke,treeNodeRequiredProps:Re,onMouseMove:function(){I(null)}}))}))}),ws=10,Yn=function(e){ga(r,e);var t=ha(r);function r(){var n;ba(this,r);for(var o=arguments.length,a=new Array(o),d=0;d<o;d++)a[d]=arguments[d];return n=t.call.apply(t,[this].concat(a)),B(ue(n),"destroyed",!1),B(ue(n),"delayedDragEnterLogic",void 0),B(ue(n),"loadingRetryTimes",{}),B(ue(n),"state",{keyEntities:{},indent:null,selectedKeys:[],checkedKeys:[],halfCheckedKeys:[],loadedKeys:[],loadingKeys:[],expandedKeys:[],draggingNodeKey:null,dragChildrenKeys:[],dropTargetKey:null,dropPosition:null,dropContainerKey:null,dropLevelOffset:null,dropTargetPos:null,dropAllowed:!0,dragOverNodeKey:null,treeData:[],flattenNodes:[],focused:!1,activeKey:null,listChanging:!1,prevProps:null,fieldNames:Pt()}),B(ue(n),"dragStartMousePosition",null),B(ue(n),"dragNodeProps",null),B(ue(n),"currentMouseOverDroppableNodeKey",null),B(ue(n),"listRef",l.createRef()),B(ue(n),"onNodeDragStart",function(i,c){var s=n.state,v=s.expandedKeys,u=s.keyEntities,p=n.props.onDragStart,f=c.eventKey;n.dragNodeProps=c,n.dragStartMousePosition={x:i.clientX,y:i.clientY};var m=lt(v,f);n.setState({draggingNodeKey:f,dragChildrenKeys:rs(f,u),indent:n.listRef.current.getIndentWidth()}),n.setExpandedKeys(m),window.addEventListener("dragend",n.onWindowDragEnd),p==null||p({event:i,node:Me(c)})}),B(ue(n),"onNodeDragEnter",function(i,c){var s=n.state,v=s.expandedKeys,u=s.keyEntities,p=s.dragChildrenKeys,f=s.flattenNodes,m=s.indent,y=n.props,g=y.onDragEnter,h=y.onExpand,C=y.allowDrop,x=y.direction,w=c.pos,E=c.eventKey;if(n.currentMouseOverDroppableNodeKey!==E&&(n.currentMouseOverDroppableNodeKey=E),!n.dragNodeProps){n.resetDragState();return}var k=Dr(i,n.dragNodeProps,c,m,n.dragStartMousePosition,C,f,u,v,x),N=k.dropPosition,R=k.dropLevelOffset,b=k.dropTargetKey,K=k.dropContainerKey,I=k.dropTargetPos,D=k.dropAllowed,P=k.dragOverNodeKey;if(p.includes(b)||!D){n.resetDragState();return}if(n.delayedDragEnterLogic||(n.delayedDragEnterLogic={}),Object.keys(n.delayedDragEnterLogic).forEach(function($){clearTimeout(n.delayedDragEnterLogic[$])}),n.dragNodeProps.eventKey!==c.eventKey&&(i.persist(),n.delayedDragEnterLogic[w]=window.setTimeout(function(){if(n.state.draggingNodeKey!==null){var $=ce(v),S=Je(u,c.eventKey);S&&(S.children||[]).length&&($=st(v,c.eventKey)),n.props.hasOwnProperty("expandedKeys")||n.setExpandedKeys($),h==null||h($,{node:Me(c),expanded:!0,nativeEvent:i.nativeEvent})}},800)),n.dragNodeProps.eventKey===b&&R===0){n.resetDragState();return}n.setState({dragOverNodeKey:P,dropPosition:N,dropLevelOffset:R,dropTargetKey:b,dropContainerKey:K,dropTargetPos:I,dropAllowed:D}),g==null||g({event:i,node:Me(c),expandedKeys:v})}),B(ue(n),"onNodeDragOver",function(i,c){var s=n.state,v=s.dragChildrenKeys,u=s.flattenNodes,p=s.keyEntities,f=s.expandedKeys,m=s.indent,y=n.props,g=y.onDragOver,h=y.allowDrop,C=y.direction;if(n.dragNodeProps){var x=Dr(i,n.dragNodeProps,c,m,n.dragStartMousePosition,h,u,p,f,C),w=x.dropPosition,E=x.dropLevelOffset,k=x.dropTargetKey,N=x.dropContainerKey,R=x.dropTargetPos,b=x.dropAllowed,K=x.dragOverNodeKey;v.includes(k)||!b||(n.dragNodeProps.eventKey===k&&E===0?n.state.dropPosition===null&&n.state.dropLevelOffset===null&&n.state.dropTargetKey===null&&n.state.dropContainerKey===null&&n.state.dropTargetPos===null&&n.state.dropAllowed===!1&&n.state.dragOverNodeKey===null||n.resetDragState():w===n.state.dropPosition&&E===n.state.dropLevelOffset&&k===n.state.dropTargetKey&&N===n.state.dropContainerKey&&R===n.state.dropTargetPos&&b===n.state.dropAllowed&&K===n.state.dragOverNodeKey||n.setState({dropPosition:w,dropLevelOffset:E,dropTargetKey:k,dropContainerKey:N,dropTargetPos:R,dropAllowed:b,dragOverNodeKey:K}),g==null||g({event:i,node:Me(c)}))}}),B(ue(n),"onNodeDragLeave",function(i,c){n.currentMouseOverDroppableNodeKey===c.eventKey&&!i.currentTarget.contains(i.relatedTarget)&&(n.resetDragState(),n.currentMouseOverDroppableNodeKey=null);var s=n.props.onDragLeave;s==null||s({event:i,node:Me(c)})}),B(ue(n),"onWindowDragEnd",function(i){n.onNodeDragEnd(i,null,!0),window.removeEventListener("dragend",n.onWindowDragEnd)}),B(ue(n),"onNodeDragEnd",function(i,c){var s=n.props.onDragEnd;n.setState({dragOverNodeKey:null}),n.cleanDragState(),s==null||s({event:i,node:Me(c)}),n.dragNodeProps=null,window.removeEventListener("dragend",n.onWindowDragEnd)}),B(ue(n),"onNodeDrop",function(i,c){var s,v=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,u=n.state,p=u.dragChildrenKeys,f=u.dropPosition,m=u.dropTargetKey,y=u.dropTargetPos,g=u.dropAllowed;if(g){var h=n.props.onDrop;if(n.setState({dragOverNodeKey:null}),n.cleanDragState(),m!==null){var C=z(z({},zt(m,n.getTreeNodeRequiredProps())),{},{active:((s=n.getActiveItem())===null||s===void 0?void 0:s.key)===m,data:Je(n.state.keyEntities,m).node}),x=p.includes(m);gt(!x,"Can not drop to dragNode's children node. This is a bug of rc-tree. Please report an issue.");var w=Un(y),E={event:i,node:Me(C),dragNode:n.dragNodeProps?Me(n.dragNodeProps):null,dragNodesKeys:[n.dragNodeProps.eventKey].concat(p),dropToGap:f!==0,dropPosition:f+Number(w[w.length-1])};v||h==null||h(E),n.dragNodeProps=null}}}),B(ue(n),"cleanDragState",function(){var i=n.state.draggingNodeKey;i!==null&&n.setState({draggingNodeKey:null,dropPosition:null,dropContainerKey:null,dropTargetKey:null,dropLevelOffset:null,dropAllowed:!0,dragOverNodeKey:null}),n.dragStartMousePosition=null,n.currentMouseOverDroppableNodeKey=null}),B(ue(n),"triggerExpandActionExpand",function(i,c){var s=n.state,v=s.expandedKeys,u=s.flattenNodes,p=c.expanded,f=c.key,m=c.isLeaf;if(!(m||i.shiftKey||i.metaKey||i.ctrlKey)){var y=u.filter(function(h){return h.key===f})[0],g=Me(z(z({},zt(f,n.getTreeNodeRequiredProps())),{},{data:y.data}));n.setExpandedKeys(p?lt(v,f):st(v,f)),n.onNodeExpand(i,g)}}),B(ue(n),"onNodeClick",function(i,c){var s=n.props,v=s.onClick,u=s.expandAction;u==="click"&&n.triggerExpandActionExpand(i,c),v==null||v(i,c)}),B(ue(n),"onNodeDoubleClick",function(i,c){var s=n.props,v=s.onDoubleClick,u=s.expandAction;u==="doubleClick"&&n.triggerExpandActionExpand(i,c),v==null||v(i,c)}),B(ue(n),"onNodeSelect",function(i,c){var s=n.state.selectedKeys,v=n.state,u=v.keyEntities,p=v.fieldNames,f=n.props,m=f.onSelect,y=f.multiple,g=c.selected,h=c[p.key],C=!g;C?y?s=st(s,h):s=[h]:s=lt(s,h);var x=s.map(function(w){var E=Je(u,w);return E?E.node:null}).filter(Boolean);n.setUncontrolledState({selectedKeys:s}),m==null||m(s,{event:"select",selected:C,node:c,selectedNodes:x,nativeEvent:i.nativeEvent})}),B(ue(n),"onNodeCheck",function(i,c,s){var v=n.state,u=v.keyEntities,p=v.checkedKeys,f=v.halfCheckedKeys,m=n.props,y=m.checkStrictly,g=m.onCheck,h=c.key,C,x={event:"check",node:c,checked:s,nativeEvent:i.nativeEvent};if(y){var w=s?st(p,h):lt(p,h),E=lt(f,h);C={checked:w,halfChecked:E},x.checkedNodes=w.map(function(I){return Je(u,I)}).filter(Boolean).map(function(I){return I.node}),n.setUncontrolledState({checkedKeys:w})}else{var k=Ot([].concat(ce(p),[h]),!0,u),N=k.checkedKeys,R=k.halfCheckedKeys;if(!s){var b=new Set(N);b.delete(h);var K=Ot(Array.from(b),{halfCheckedKeys:R},u);N=K.checkedKeys,R=K.halfCheckedKeys}C=N,x.checkedNodes=[],x.checkedNodesPositions=[],x.halfCheckedKeys=R,N.forEach(function(I){var D=Je(u,I);if(D){var P=D.node,$=D.pos;x.checkedNodes.push(P),x.checkedNodesPositions.push({node:P,pos:$})}}),n.setUncontrolledState({checkedKeys:N},!1,{halfCheckedKeys:R})}g==null||g(C,x)}),B(ue(n),"onNodeLoad",function(i){var c,s=i.key,v=n.state.keyEntities,u=Je(v,s);if(!(u!=null&&(c=u.children)!==null&&c!==void 0&&c.length)){var p=new Promise(function(f,m){n.setState(function(y){var g=y.loadedKeys,h=g===void 0?[]:g,C=y.loadingKeys,x=C===void 0?[]:C,w=n.props,E=w.loadData,k=w.onLoad;if(!E||h.includes(s)||x.includes(s))return null;var N=E(i);return N.then(function(){var R=n.state.loadedKeys,b=st(R,s);k==null||k(b,{event:"load",node:i}),n.setUncontrolledState({loadedKeys:b}),n.setState(function(K){return{loadingKeys:lt(K.loadingKeys,s)}}),f()}).catch(function(R){if(n.setState(function(K){return{loadingKeys:lt(K.loadingKeys,s)}}),n.loadingRetryTimes[s]=(n.loadingRetryTimes[s]||0)+1,n.loadingRetryTimes[s]>=ws){var b=n.state.loadedKeys;gt(!1,"Retry for `loadData` many times but still failed. No more retry."),n.setUncontrolledState({loadedKeys:st(b,s)}),f()}m(R)}),{loadingKeys:st(x,s)}})});return p.catch(function(){}),p}}),B(ue(n),"onNodeMouseEnter",function(i,c){var s=n.props.onMouseEnter;s==null||s({event:i,node:c})}),B(ue(n),"onNodeMouseLeave",function(i,c){var s=n.props.onMouseLeave;s==null||s({event:i,node:c})}),B(ue(n),"onNodeContextMenu",function(i,c){var s=n.props.onRightClick;s&&(i.preventDefault(),s({event:i,node:c}))}),B(ue(n),"onFocus",function(){var i=n.props.onFocus;n.setState({focused:!0});for(var c=arguments.length,s=new Array(c),v=0;v<c;v++)s[v]=arguments[v];i==null||i.apply(void 0,s)}),B(ue(n),"onBlur",function(){var i=n.props.onBlur;n.setState({focused:!1}),n.onActiveChange(null);for(var c=arguments.length,s=new Array(c),v=0;v<c;v++)s[v]=arguments[v];i==null||i.apply(void 0,s)}),B(ue(n),"getTreeNodeRequiredProps",function(){var i=n.state,c=i.expandedKeys,s=i.selectedKeys,v=i.loadedKeys,u=i.loadingKeys,p=i.checkedKeys,f=i.halfCheckedKeys,m=i.dragOverNodeKey,y=i.dropPosition,g=i.keyEntities;return{expandedKeys:c||[],selectedKeys:s||[],loadedKeys:v||[],loadingKeys:u||[],checkedKeys:p||[],halfCheckedKeys:f||[],dragOverNodeKey:m,dropPosition:y,keyEntities:g}}),B(ue(n),"setExpandedKeys",function(i){var c=n.state,s=c.treeData,v=c.fieldNames,u=bn(s,i,v);n.setUncontrolledState({expandedKeys:i,flattenNodes:u},!0)}),B(ue(n),"onNodeExpand",function(i,c){var s=n.state.expandedKeys,v=n.state,u=v.listChanging,p=v.fieldNames,f=n.props,m=f.onExpand,y=f.loadData,g=c.expanded,h=c[p.key];if(!u){var C=s.includes(h),x=!g;if(gt(g&&C||!g&&!C,"Expand state not sync with index check"),s=x?st(s,h):lt(s,h),n.setExpandedKeys(s),m==null||m(s,{node:c,expanded:x,nativeEvent:i.nativeEvent}),x&&y){var w=n.onNodeLoad(c);w&&w.then(function(){var E=bn(n.state.treeData,s,p);n.setUncontrolledState({flattenNodes:E})}).catch(function(){var E=n.state.expandedKeys,k=lt(E,h);n.setExpandedKeys(k)})}}}),B(ue(n),"onListChangeStart",function(){n.setUncontrolledState({listChanging:!0})}),B(ue(n),"onListChangeEnd",function(){setTimeout(function(){n.setUncontrolledState({listChanging:!1})})}),B(ue(n),"onActiveChange",function(i){var c=n.state.activeKey,s=n.props,v=s.onActiveChange,u=s.itemScrollOffset,p=u===void 0?0:u;c!==i&&(n.setState({activeKey:i}),i!==null&&n.scrollTo({key:i,offset:p}),v==null||v(i))}),B(ue(n),"getActiveItem",function(){var i=n.state,c=i.activeKey,s=i.flattenNodes;return c===null?null:s.find(function(v){var u=v.key;return u===c})||null}),B(ue(n),"offsetActiveKey",function(i){var c=n.state,s=c.flattenNodes,v=c.activeKey,u=s.findIndex(function(m){var y=m.key;return y===v});u===-1&&i<0&&(u=s.length),u=(u+i+s.length)%s.length;var p=s[u];if(p){var f=p.key;n.onActiveChange(f)}else n.onActiveChange(null)}),B(ue(n),"onKeyDown",function(i){var c=n.state,s=c.activeKey,v=c.expandedKeys,u=c.checkedKeys,p=c.fieldNames,f=n.props,m=f.onKeyDown,y=f.checkable,g=f.selectable;switch(i.which){case mt.UP:{n.offsetActiveKey(-1),i.preventDefault();break}case mt.DOWN:{n.offsetActiveKey(1),i.preventDefault();break}}var h=n.getActiveItem();if(h&&h.data){var C=n.getTreeNodeRequiredProps(),x=h.data.isLeaf===!1||!!(h.data[p.children]||[]).length,w=Me(z(z({},zt(s,C)),{},{data:h.data,active:!0}));switch(i.which){case mt.LEFT:{x&&v.includes(s)?n.onNodeExpand({},w):h.parent&&n.onActiveChange(h.parent.key),i.preventDefault();break}case mt.RIGHT:{x&&!v.includes(s)?n.onNodeExpand({},w):h.children&&h.children.length&&n.onActiveChange(h.children[0].key),i.preventDefault();break}case mt.ENTER:case mt.SPACE:{y&&!w.disabled&&w.checkable!==!1&&!w.disableCheckbox?n.onNodeCheck({},w,!u.includes(s)):!y&&g&&!w.disabled&&w.selectable!==!1&&n.onNodeSelect({},w);break}}}m==null||m(i)}),B(ue(n),"setUncontrolledState",function(i){var c=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null;if(!n.destroyed){var v=!1,u=!0,p={};Object.keys(i).forEach(function(f){if(n.props.hasOwnProperty(f)){u=!1;return}v=!0,p[f]=i[f]}),v&&(!c||u)&&n.setState(z(z({},p),s))}}),B(ue(n),"scrollTo",function(i){n.listRef.current.scrollTo(i)}),n}return ya(r,[{key:"componentDidMount",value:function(){this.destroyed=!1,this.onUpdated()}},{key:"componentDidUpdate",value:function(){this.onUpdated()}},{key:"onUpdated",value:function(){var o=this.props,a=o.activeKey,d=o.itemScrollOffset,i=d===void 0?0:d;a!==void 0&&a!==this.state.activeKey&&(this.setState({activeKey:a}),a!==null&&this.scrollTo({key:a,offset:i}))}},{key:"componentWillUnmount",value:function(){window.removeEventListener("dragend",this.onWindowDragEnd),this.destroyed=!0}},{key:"resetDragState",value:function(){this.setState({dragOverNodeKey:null,dropPosition:null,dropLevelOffset:null,dropTargetKey:null,dropContainerKey:null,dropTargetPos:null,dropAllowed:!1})}},{key:"render",value:function(){var o=this.state,a=o.focused,d=o.flattenNodes,i=o.keyEntities,c=o.draggingNodeKey,s=o.activeKey,v=o.dropLevelOffset,u=o.dropContainerKey,p=o.dropTargetKey,f=o.dropPosition,m=o.dragOverNodeKey,y=o.indent,g=this.props,h=g.prefixCls,C=g.className,x=g.style,w=g.showLine,E=g.focusable,k=g.tabIndex,N=k===void 0?0:k,R=g.selectable,b=g.showIcon,K=g.icon,I=g.switcherIcon,D=g.draggable,P=g.checkable,$=g.checkStrictly,S=g.disabled,O=g.motion,T=g.loadData,_=g.filterTreeNode,M=g.height,A=g.itemHeight,Y=g.scrollWidth,q=g.virtual,ee=g.titleRender,be=g.dropIndicatorRender,Ee=g.onContextMenu,we=g.onScroll,Q=g.direction,Z=g.rootClassName,Ce=g.rootStyle,me=At(this.props,{aria:!0,data:!0}),G;D&&(it(D)==="object"?G=D:typeof D=="function"?G={nodeDraggable:D}:G={});var X={prefixCls:h,selectable:R,showIcon:b,icon:K,switcherIcon:I,draggable:G,draggingNodeKey:c,checkable:P,checkStrictly:$,disabled:S,keyEntities:i,dropLevelOffset:v,dropContainerKey:u,dropTargetKey:p,dropPosition:f,dragOverNodeKey:m,indent:y,direction:Q,dropIndicatorRender:be,loadData:T,filterTreeNode:_,titleRender:ee,onNodeClick:this.onNodeClick,onNodeDoubleClick:this.onNodeDoubleClick,onNodeExpand:this.onNodeExpand,onNodeSelect:this.onNodeSelect,onNodeCheck:this.onNodeCheck,onNodeLoad:this.onNodeLoad,onNodeMouseEnter:this.onNodeMouseEnter,onNodeMouseLeave:this.onNodeMouseLeave,onNodeContextMenu:this.onNodeContextMenu,onNodeDragStart:this.onNodeDragStart,onNodeDragEnter:this.onNodeDragEnter,onNodeDragOver:this.onNodeDragOver,onNodeDragLeave:this.onNodeDragLeave,onNodeDragEnd:this.onNodeDragEnd,onNodeDrop:this.onNodeDrop};return l.createElement(Xn.Provider,{value:X},l.createElement("div",{className:J(h,C,Z,B(B(B({},"".concat(h,"-show-line"),w),"".concat(h,"-focused"),a),"".concat(h,"-active-focused"),s!==null)),style:Ce},l.createElement(Ss,pe({ref:this.listRef,prefixCls:h,style:x,data:d,disabled:S,selectable:R,checkable:!!P,motion:O,dragging:c!==null,height:M,itemHeight:A,virtual:q,focusable:E,focused:a,tabIndex:N,activeItem:this.getActiveItem(),onFocus:this.onFocus,onBlur:this.onBlur,onKeyDown:this.onKeyDown,onActiveChange:this.onActiveChange,onListChangeStart:this.onListChangeStart,onListChangeEnd:this.onListChangeEnd,onContextMenu:Ee,onScroll:we,scrollWidth:Y},this.getTreeNodeRequiredProps(),me))))}}],[{key:"getDerivedStateFromProps",value:function(o,a){var d=a.prevProps,i={prevProps:o};function c(N){return!d&&o.hasOwnProperty(N)||d&&d[N]!==o[N]}var s,v=a.fieldNames;if(c("fieldNames")&&(v=Pt(o.fieldNames),i.fieldNames=v),c("treeData")?s=o.treeData:c("children")&&(gt(!1,"`children` of Tree is deprecated. Please use `treeData` instead."),s=yo(o.children)),s){i.treeData=s;var u=jn(s,{fieldNames:v});i.keyEntities=z(B({},$t,Wo),u.keyEntities)}var p=i.keyEntities||a.keyEntities;if(c("expandedKeys")||d&&c("autoExpandParent"))i.expandedKeys=o.autoExpandParent||!d&&o.defaultExpandParent?Nn(o.expandedKeys,p):o.expandedKeys;else if(!d&&o.defaultExpandAll){var f=z({},p);delete f[$t];var m=[];Object.keys(f).forEach(function(N){var R=f[N];R.children&&R.children.length&&m.push(R.key)}),i.expandedKeys=m}else!d&&o.defaultExpandedKeys&&(i.expandedKeys=o.autoExpandParent||o.defaultExpandParent?Nn(o.defaultExpandedKeys,p):o.defaultExpandedKeys);if(i.expandedKeys||delete i.expandedKeys,s||i.expandedKeys){var y=bn(s||a.treeData,i.expandedKeys||a.expandedKeys,v);i.flattenNodes=y}if(o.selectable&&(c("selectedKeys")?i.selectedKeys=Mr(o.selectedKeys,o):!d&&o.defaultSelectedKeys&&(i.selectedKeys=Mr(o.defaultSelectedKeys,o))),o.checkable){var g;if(c("checkedKeys")?g=yn(o.checkedKeys)||{}:!d&&o.defaultCheckedKeys?g=yn(o.defaultCheckedKeys)||{}:s&&(g=yn(o.checkedKeys)||{checkedKeys:a.checkedKeys,halfCheckedKeys:a.halfCheckedKeys}),g){var h=g,C=h.checkedKeys,x=C===void 0?[]:C,w=h.halfCheckedKeys,E=w===void 0?[]:w;if(!o.checkStrictly){var k=Ot(x,!0,p);x=k.checkedKeys,E=k.halfCheckedKeys}i.checkedKeys=x,i.halfCheckedKeys=E}}return c("loadedKeys")&&(i.loadedKeys=o.loadedKeys),i}}]),r}(l.Component);B(Yn,"defaultProps",{prefixCls:"rc-tree",showLine:!1,showIcon:!0,selectable:!0,multiple:!1,checkable:!1,disabled:!1,checkStrictly:!1,draggable:!1,defaultExpandParent:!0,autoExpandParent:!1,defaultExpandAll:!1,defaultExpandedKeys:[],defaultCheckedKeys:[],defaultSelectedKeys:[],dropIndicatorRender:ps,allowDrop:function(){return!0},expandAction:!1});B(Yn,"TreeNode",Wt);const Es=({treeCls:e,treeNodeCls:t,directoryNodeSelectedBg:r,directoryNodeSelectedColor:n,motionDurationMid:o,borderRadius:a,controlItemBgHover:d})=>({[`${e}${e}-directory ${t}`]:{[`${e}-node-content-wrapper`]:{position:"static",[`> *:not(${e}-drop-indicator)`]:{position:"relative"},"&:hover":{background:"transparent"},"&:before":{position:"absolute",inset:0,transition:`background-color ${o}`,content:'""',borderRadius:a},"&:hover:before":{background:d}},[`${e}-switcher, ${e}-checkbox, ${e}-draggable-icon`]:{zIndex:1},"&-selected":{[`${e}-switcher, ${e}-draggable-icon`]:{color:n},[`${e}-node-content-wrapper`]:{color:n,background:"transparent","&:before, &:hover:before":{background:r}}}}}),$s=new Ca("ant-tree-node-fx-do-not-use",{"0%":{opacity:0},"100%":{opacity:1}}),ks=(e,t)=>({[`.${e}-switcher-icon`]:{display:"inline-block",fontSize:10,verticalAlign:"baseline",svg:{transition:`transform ${t.motionDurationSlow}`}}}),Ns=(e,t)=>({[`.${e}-drop-indicator`]:{position:"absolute",zIndex:1,height:2,backgroundColor:t.colorPrimary,borderRadius:1,pointerEvents:"none","&:after":{position:"absolute",top:-3,insetInlineStart:-6,width:8,height:8,backgroundColor:"transparent",border:`${V(t.lineWidthBold)} solid ${t.colorPrimary}`,borderRadius:"50%",content:'""'}}}),Rs=(e,t)=>{const{treeCls:r,treeNodeCls:n,treeNodePadding:o,titleHeight:a,indentSize:d,nodeSelectedBg:i,nodeHoverBg:c,colorTextQuaternary:s,controlItemBgActiveDisabled:v}=t;return{[r]:Object.assign(Object.assign({},dt(t)),{background:t.colorBgContainer,borderRadius:t.borderRadius,transition:`background-color ${t.motionDurationSlow}`,"&-rtl":{direction:"rtl"},[`&${r}-rtl ${r}-switcher_close ${r}-switcher-icon svg`]:{transform:"rotate(90deg)"},[`&-focused:not(:hover):not(${r}-active-focused)`]:Object.assign({},an(t)),[`${r}-list-holder-inner`]:{alignItems:"flex-start"},[`&${r}-block-node`]:{[`${r}-list-holder-inner`]:{alignItems:"stretch",[`${r}-node-content-wrapper`]:{flex:"auto"},[`${n}.dragging:after`]:{position:"absolute",inset:0,border:`1px solid ${t.colorPrimary}`,opacity:0,animationName:$s,animationDuration:t.motionDurationSlow,animationPlayState:"running",animationFillMode:"forwards",content:'""',pointerEvents:"none",borderRadius:t.borderRadius}}},[n]:{display:"flex",alignItems:"flex-start",marginBottom:o,lineHeight:V(a),position:"relative","&:before":{content:'""',position:"absolute",zIndex:1,insetInlineStart:0,width:"100%",top:"100%",height:o},[`&-disabled ${r}-node-content-wrapper`]:{color:t.colorTextDisabled,cursor:"not-allowed","&:hover":{background:"transparent"}},[`${r}-checkbox-disabled + ${r}-node-selected,&${n}-disabled${n}-selected ${r}-node-content-wrapper`]:{backgroundColor:v},[`${r}-checkbox-disabled`]:{pointerEvents:"unset"},[`&:not(${n}-disabled)`]:{[`${r}-node-content-wrapper`]:{"&:hover":{color:t.nodeHoverColor}}},[`&-active ${r}-node-content-wrapper`]:{background:t.controlItemBgHover},[`&:not(${n}-disabled).filter-node ${r}-title`]:{color:t.colorPrimary,fontWeight:500},"&-draggable":{cursor:"grab",[`${r}-draggable-icon`]:{flexShrink:0,width:a,textAlign:"center",visibility:"visible",color:s},[`&${n}-disabled ${r}-draggable-icon`]:{visibility:"hidden"}}},[`${r}-indent`]:{alignSelf:"stretch",whiteSpace:"nowrap",userSelect:"none","&-unit":{display:"inline-block",width:d}},[`${r}-draggable-icon`]:{visibility:"hidden"},[`${r}-switcher, ${r}-checkbox`]:{marginInlineEnd:t.calc(t.calc(a).sub(t.controlInteractiveSize)).div(2).equal()},[`${r}-switcher`]:Object.assign(Object.assign({},ks(e,t)),{position:"relative",flex:"none",alignSelf:"stretch",width:a,textAlign:"center",cursor:"pointer",userSelect:"none",transition:`all ${t.motionDurationSlow}`,"&-noop":{cursor:"unset"},"&:before":{pointerEvents:"none",content:'""',width:a,height:a,position:"absolute",left:{_skip_check_:!0,value:0},top:0,borderRadius:t.borderRadius,transition:`all ${t.motionDurationSlow}`},[`&:not(${r}-switcher-noop):hover:before`]:{backgroundColor:t.colorBgTextHover},[`&_close ${r}-switcher-icon svg`]:{transform:"rotate(-90deg)"},"&-loading-icon":{color:t.colorPrimary},"&-leaf-line":{position:"relative",zIndex:1,display:"inline-block",width:"100%",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(a).div(2).equal(),bottom:t.calc(o).mul(-1).equal(),marginInlineStart:-1,borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&:after":{position:"absolute",width:t.calc(t.calc(a).div(2).equal()).mul(.8).equal(),height:t.calc(a).div(2).equal(),borderBottom:`1px solid ${t.colorBorder}`,content:'""'}}}),[`${r}-node-content-wrapper`]:Object.assign(Object.assign({position:"relative",minHeight:a,paddingBlock:0,paddingInline:t.paddingXS,background:"transparent",borderRadius:t.borderRadius,cursor:"pointer",transition:`all ${t.motionDurationMid}, border 0s, line-height 0s, box-shadow 0s`},Ns(e,t)),{"&:hover":{backgroundColor:c},[`&${r}-node-selected`]:{color:t.nodeSelectedColor,backgroundColor:i},[`${r}-iconEle`]:{display:"inline-block",width:a,height:a,textAlign:"center",verticalAlign:"top","&:empty":{display:"none"}}}),[`${r}-unselectable ${r}-node-content-wrapper:hover`]:{backgroundColor:"transparent"},[`${n}.drop-container > [draggable]`]:{boxShadow:`0 0 0 2px ${t.colorPrimary}`},"&-show-line":{[`${r}-indent-unit`]:{position:"relative",height:"100%","&:before":{position:"absolute",top:0,insetInlineEnd:t.calc(a).div(2).equal(),bottom:t.calc(o).mul(-1).equal(),borderInlineEnd:`1px solid ${t.colorBorder}`,content:'""'},"&-end:before":{display:"none"}},[`${r}-switcher`]:{background:"transparent","&-line-icon":{verticalAlign:"-0.15em"}}},[`${n}-leaf-last ${r}-switcher-leaf-line:before`]:{top:"auto !important",bottom:"auto !important",height:`${V(t.calc(a).div(2).equal())} !important`}})}},Is=(e,t,r=!0)=>{const n=`.${e}`,o=`${n}-treenode`,a=t.calc(t.paddingXS).div(2).equal(),d=on(t,{treeCls:n,treeNodeCls:o,treeNodePadding:a});return[Rs(e,d),r&&Es(d)].filter(Boolean)},Os=e=>{const{controlHeightSM:t,controlItemBgHover:r,controlItemBgActive:n}=e,o=t;return{titleHeight:o,indentSize:o,nodeHoverBg:r,nodeHoverColor:e.colorText,nodeSelectedBg:n,nodeSelectedColor:e.colorText}},Ks=e=>{const{colorTextLightSolid:t,colorPrimary:r}=e;return Object.assign(Object.assign({},Os(e)),{directoryNodeSelectedColor:t,directoryNodeSelectedBg:r})},Ps=rn("Tree",(e,{prefixCls:t})=>[{[e.componentCls]:Co(`${t}-checkbox`,e)},Is(t,e),xa(e)],Ks),jr=4;function Ts(e){const{dropPosition:t,dropLevelOffset:r,prefixCls:n,indent:o,direction:a="ltr"}=e,d=a==="ltr"?"left":"right",i=a==="ltr"?"right":"left",c={[d]:-r*o+jr,[i]:0};switch(t){case-1:c.top=-3;break;case 1:c.bottom=-3;break;default:c.bottom=-3,c[d]=o+jr;break}return ve.createElement("div",{style:c,className:`${n}-drop-indicator`})}const Ds=e=>{const{prefixCls:t,switcherIcon:r,treeNodeProps:n,showLine:o,switcherLoadingIcon:a}=e,{isLeaf:d,expanded:i,loading:c}=n;if(c)return l.isValidElement(a)?a:l.createElement(Sa,{className:`${t}-switcher-loading-icon`});let s;if(o&&typeof o=="object"&&(s=o.showLeafIcon),d){if(!o)return null;if(typeof s!="boolean"&&s){const p=typeof s=="function"?s(n):s,f=`${t}-switcher-line-custom-icon`;return l.isValidElement(p)?xr(p,{className:J(p.props.className||"",f)}):p}return s?l.createElement($o,{className:`${t}-switcher-line-icon`}):l.createElement("span",{className:`${t}-switcher-leaf-line`})}const v=`${t}-switcher-icon`,u=typeof r=="function"?r(n):r;return l.isValidElement(u)?xr(u,{className:J(u.props.className||"",v)}):u!==void 0?u:o?i?l.createElement(_l,{className:`${t}-switcher-line-icon`}):l.createElement(Fl,{className:`${t}-switcher-line-icon`}):l.createElement(vl,{className:v})},Vo=ve.forwardRef((e,t)=>{var r;const{getPrefixCls:n,direction:o,virtual:a,tree:d}=ve.useContext(ut),{prefixCls:i,className:c,showIcon:s=!1,showLine:v,switcherIcon:u,switcherLoadingIcon:p,blockNode:f=!1,children:m,checkable:y=!1,selectable:g=!0,draggable:h,motion:C,style:x}=e,w=n("tree",i),E=n(),k=C??Object.assign(Object.assign({},wa(E)),{motionAppear:!1}),N=Object.assign(Object.assign({},e),{checkable:y,selectable:g,showIcon:s,motion:k,blockNode:f,showLine:!!v,dropIndicatorRender:Ts}),[R,b,K]=Ps(w),[,I]=co(),D=I.paddingXS/2+(((r=I.Tree)===null||r===void 0?void 0:r.titleHeight)||I.controlHeightSM),P=ve.useMemo(()=>{if(!h)return!1;let S={};switch(typeof h){case"function":S.nodeDraggable=h;break;case"object":S=Object.assign({},h);break}return S.icon!==!1&&(S.icon=S.icon||ve.createElement(Ml,null)),S},[h]),$=S=>ve.createElement(Ds,{prefixCls:w,switcherIcon:u,switcherLoadingIcon:p,treeNodeProps:S,showLine:v});return R(ve.createElement(Yn,Object.assign({itemHeight:D,ref:t,virtual:a},N,{style:Object.assign(Object.assign({},d==null?void 0:d.style),x),prefixCls:w,className:J({[`${w}-icon-hide`]:!s,[`${w}-block-node`]:f,[`${w}-unselectable`]:!g,[`${w}-rtl`]:o==="rtl"},d==null?void 0:d.className,c,b,K),direction:o,checkable:y&&ve.createElement("span",{className:`${w}-checkbox-inner`}),selectable:g,switcherIcon:$,draggable:P}),m))}),Ar=0,xn=1,Wr=2;function Jn(e,t,r){const{key:n,children:o}=r;function a(d){const i=d[n],c=d[o];t(i,d)!==!1&&Jn(c||[],t,r)}e.forEach(a)}function Ms({treeData:e,expandedKeys:t,startKey:r,endKey:n,fieldNames:o}){const a=[];let d=Ar;if(r&&r===n)return[r];if(!r||!n)return[];function i(c){return c===r||c===n}return Jn(e,c=>{if(d===Wr)return!1;if(i(c)){if(a.push(c),d===Ar)d=xn;else if(d===xn)return d=Wr,!1}else d===xn&&a.push(c);return t.includes(c)},Pt(o)),a}function Cn(e,t,r){const n=ce(t),o=[];return Jn(e,(a,d)=>{const i=n.indexOf(a);return i!==-1&&(o.push(d),n.splice(i,1)),!!n.length},Pt(r)),o}var Vr=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};function Bs(e){const{isLeaf:t,expanded:r}=e;return t?l.createElement($o,null):r?l.createElement(Il,null):l.createElement(Pl,null)}function qr({treeData:e,children:t}){return e||yo(t)}const Ls=(e,t)=>{var{defaultExpandAll:r,defaultExpandParent:n,defaultExpandedKeys:o}=e,a=Vr(e,["defaultExpandAll","defaultExpandParent","defaultExpandedKeys"]);const d=l.useRef(null),i=l.useRef(null),c=()=>{const{keyEntities:R}=jn(qr(a));let b;return r?b=Object.keys(R):n?b=Nn(a.expandedKeys||o||[],R):b=a.expandedKeys||o||[],b},[s,v]=l.useState(a.selectedKeys||a.defaultSelectedKeys||[]),[u,p]=l.useState(()=>c());l.useEffect(()=>{"selectedKeys"in a&&v(a.selectedKeys)},[a.selectedKeys]),l.useEffect(()=>{"expandedKeys"in a&&p(a.expandedKeys)},[a.expandedKeys]);const f=(R,b)=>{var K;return"expandedKeys"in a||p(R),(K=a.onExpand)===null||K===void 0?void 0:K.call(a,R,b)},m=(R,b)=>{var K;const{multiple:I,fieldNames:D}=a,{node:P,nativeEvent:$}=b,{key:S=""}=P,O=qr(a),T=Object.assign(Object.assign({},b),{selected:!0}),_=($==null?void 0:$.ctrlKey)||($==null?void 0:$.metaKey),M=$==null?void 0:$.shiftKey;let A;I&&_?(A=R,d.current=S,i.current=A,T.selectedNodes=Cn(O,A,D)):I&&M?(A=Array.from(new Set([].concat(ce(i.current||[]),ce(Ms({treeData:O,expandedKeys:u,startKey:S,endKey:d.current,fieldNames:D}))))),T.selectedNodes=Cn(O,A,D)):(A=[S],d.current=S,i.current=A,T.selectedNodes=Cn(O,A,D)),(K=a.onSelect)===null||K===void 0||K.call(a,A,T),"selectedKeys"in a||v(A)},{getPrefixCls:y,direction:g}=l.useContext(ut),{prefixCls:h,className:C,showIcon:x=!0,expandAction:w="click"}=a,E=Vr(a,["prefixCls","className","showIcon","expandAction"]),k=y("tree",h),N=J(`${k}-directory`,{[`${k}-directory-rtl`]:g==="rtl"},C);return l.createElement(Vo,Object.assign({icon:Bs,ref:t,blockNode:!0},E,{showIcon:x,expandAction:w,prefixCls:k,className:N,expandedKeys:u,selectedKeys:s,onSelect:m,onExpand:f}))},_s=l.forwardRef(Ls),Qn=Vo;Qn.DirectoryTree=_s;Qn.TreeNode=Wt;const Gr=e=>{const{value:t,filterSearch:r,tablePrefixCls:n,locale:o,onChange:a}=e;return r?l.createElement("div",{className:`${n}-filter-dropdown-search`},l.createElement(Ba,{prefix:l.createElement(Pa,null),placeholder:o.filterSearchPlaceholder,onChange:a,value:t,htmlSize:1,className:`${n}-filter-dropdown-search-input`})):null},Hs=e=>{const{keyCode:t}=e;t===mt.ENTER&&e.stopPropagation()},zs=l.forwardRef((e,t)=>l.createElement("div",{className:e.className,onClick:r=>r.stopPropagation(),onKeyDown:Hs,ref:t},e.children));function Kt(e){let t=[];return(e||[]).forEach(({value:r,children:n})=>{t.push(r),n&&(t=[].concat(ce(t),ce(Kt(n))))}),t}function Fs(e){return e.some(({children:t})=>t)}function qo(e,t){return typeof t=="string"||typeof t=="number"?t==null?void 0:t.toString().toLowerCase().includes(e.trim().toLowerCase()):!1}function Go({filters:e,prefixCls:t,filteredKeys:r,filterMultiple:n,searchValue:o,filterSearch:a}){return e.map((d,i)=>{const c=String(d.value);if(d.children)return{key:c||i,label:d.text,popupClassName:`${t}-dropdown-submenu`,children:Go({filters:d.children,prefixCls:t,filteredKeys:r,filterMultiple:n,searchValue:o,filterSearch:a})};const s=n?Tt:qt,v={key:d.value!==void 0?c:i,label:l.createElement(l.Fragment,null,l.createElement(s,{checked:r.includes(c)}),l.createElement("span",null,d.text))};return o.trim()?typeof a=="function"?a(o,d)?v:null:qo(o,d.text)?v:null:v})}function Sn(e){return e||[]}const js=e=>{var t,r,n,o;const{tablePrefixCls:a,prefixCls:d,column:i,dropdownPrefixCls:c,columnKey:s,filterOnClose:v,filterMultiple:u,filterMode:p="menu",filterSearch:f=!1,filterState:m,triggerFilter:y,locale:g,children:h,getPopupContainer:C,rootClassName:x}=e,{filterResetToDefaultFilteredValue:w,defaultFilteredValue:E,filterDropdownProps:k={},filterDropdownOpen:N,filterDropdownVisible:R,onFilterDropdownVisibleChange:b,onFilterDropdownOpenChange:K}=i,[I,D]=l.useState(!1),P=!!(m&&(!((t=m.filteredKeys)===null||t===void 0)&&t.length||m.forceFiltered)),$=L=>{var j;D(L),(j=k.onOpenChange)===null||j===void 0||j.call(k,L),K==null||K(L),b==null||b(L)},S=(o=(n=(r=k.open)!==null&&r!==void 0?r:N)!==null&&n!==void 0?n:R)!==null&&o!==void 0?o:I,O=m==null?void 0:m.filteredKeys,[T,_]=vs(Sn(O)),M=({selectedKeys:L})=>{_(L)},A=(L,{node:j,checked:re})=>{M(u?{selectedKeys:L}:{selectedKeys:re&&j.key?[j.key]:[]})};l.useEffect(()=>{I&&M({selectedKeys:Sn(O)})},[O]);const[Y,q]=l.useState([]),ee=L=>{q(L)},[be,Ee]=l.useState(""),we=L=>{const{value:j}=L.target;Ee(j)};l.useEffect(()=>{I||Ee("")},[I]);const Q=L=>{const j=L!=null&&L.length?L:null;if(j===null&&(!m||!m.filteredKeys)||jt(j,m==null?void 0:m.filteredKeys,!0))return null;y({column:i,key:s,filteredKeys:j})},Z=()=>{$(!1),Q(T())},Ce=({confirm:L,closeDropdown:j}={confirm:!1,closeDropdown:!1})=>{L&&Q([]),j&&$(!1),Ee(""),_(w?(E||[]).map(re=>String(re)):[])},me=({closeDropdown:L}={closeDropdown:!0})=>{L&&$(!1),Q(T())},G=(L,j)=>{j.source==="trigger"&&(L&&O!==void 0&&_(Sn(O)),$(L),!L&&!i.filterDropdown&&v&&Z())},X=J({[`${c}-menu-without-submenu`]:!Fs(i.filters||[])}),F=L=>{if(L.target.checked){const j=Kt(i==null?void 0:i.filters).map(re=>String(re));_(j)}else _([])},W=({filters:L})=>(L||[]).map((j,re)=>{const ye=String(j.value),ie={title:j.text,key:j.value!==void 0?ye:String(re)};return j.children&&(ie.children=W({filters:j.children})),ie}),te=L=>{var j;return Object.assign(Object.assign({},L),{text:L.title,value:L.key,children:((j=L.children)===null||j===void 0?void 0:j.map(re=>te(re)))||[]})};let le;const{direction:ge,renderEmpty:ke}=l.useContext(ut);if(typeof i.filterDropdown=="function")le=i.filterDropdown({prefixCls:`${c}-custom`,setSelectedKeys:L=>M({selectedKeys:L}),selectedKeys:T(),confirm:me,clearFilters:Ce,filters:i.filters,visible:S,close:()=>{$(!1)}});else if(i.filterDropdown)le=i.filterDropdown;else{const L=T()||[],j=()=>{var ye,ie;const Ne=(ye=ke==null?void 0:ke("Table.filter"))!==null&&ye!==void 0?ye:l.createElement(wr,{image:wr.PRESENTED_IMAGE_SIMPLE,description:g.filterEmptyText,styles:{image:{height:24}},style:{margin:0,padding:"16px 0"}});if((i.filters||[]).length===0)return Ne;if(p==="tree")return l.createElement(l.Fragment,null,l.createElement(Gr,{filterSearch:f,value:be,onChange:we,tablePrefixCls:a,locale:g}),l.createElement("div",{className:`${a}-filter-dropdown-tree`},u?l.createElement(Tt,{checked:L.length===Kt(i.filters).length,indeterminate:L.length>0&&L.length<Kt(i.filters).length,className:`${a}-filter-dropdown-checkall`,onChange:F},(ie=g==null?void 0:g.filterCheckall)!==null&&ie!==void 0?ie:g==null?void 0:g.filterCheckAll):null,l.createElement(Qn,{checkable:!0,selectable:!1,blockNode:!0,multiple:u,checkStrictly:!u,className:`${c}-menu`,onCheck:A,checkedKeys:L,selectedKeys:L,showIcon:!1,treeData:W({filters:i.filters}),autoExpandParent:!0,defaultExpandAll:!0,filterTreeNode:be.trim()?he=>typeof f=="function"?f(be,te(he)):qo(be,he.title):void 0})));const Pe=Go({filters:i.filters||[],filterSearch:f,prefixCls:d,filteredKeys:T(),filterMultiple:u,searchValue:be}),Le=Pe.every(he=>he===null);return l.createElement(l.Fragment,null,l.createElement(Gr,{filterSearch:f,value:be,onChange:we,tablePrefixCls:a,locale:g}),Le?Ne:l.createElement(Ea,{selectable:!0,multiple:u,prefixCls:`${c}-menu`,className:X,onSelect:M,onDeselect:M,selectedKeys:L,getPopupContainer:C,openKeys:Y,onOpenChange:ee,items:Pe}))},re=()=>w?jt((E||[]).map(ye=>String(ye)),L,!0):L.length===0;le=l.createElement(l.Fragment,null,j(),l.createElement("div",{className:`${d}-dropdown-btns`},l.createElement(Cr,{type:"link",size:"small",disabled:re(),onClick:()=>Ce()},g.filterReset),l.createElement(Cr,{type:"primary",size:"small",onClick:Z},g.filterConfirm)))}i.filterDropdown&&(le=l.createElement($a,{selectable:void 0},le)),le=l.createElement(zs,{className:`${d}-dropdown`},le);const Re=fo({trigger:["click"],placement:ge==="rtl"?"bottomLeft":"bottomRight",children:(()=>{let L;return typeof i.filterIcon=="function"?L=i.filterIcon(P):i.filterIcon?L=i.filterIcon:L=l.createElement(kl,null),l.createElement("span",{role:"button",tabIndex:-1,className:J(`${d}-trigger`,{active:P}),onClick:j=>{j.stopPropagation()}},L)})(),getPopupContainer:C},Object.assign(Object.assign({},k),{rootClassName:J(x,k.rootClassName),open:S,onOpenChange:G,popupRender:()=>typeof(k==null?void 0:k.dropdownRender)=="function"?k.dropdownRender(le):le}));return l.createElement("div",{className:`${d}-column`},l.createElement("span",{className:`${a}-column-title`},h),l.createElement(so,Object.assign({},Re)))},Pn=(e,t,r)=>{let n=[];return(e||[]).forEach((o,a)=>{var d;const i=Lt(a,r),c=o.filterDropdown!==void 0;if(o.filters||c||"onFilter"in o)if("filteredValue"in o){let s=o.filteredValue;c||(s=(d=s==null?void 0:s.map(String))!==null&&d!==void 0?d:s),n.push({column:o,key:yt(o,i),filteredKeys:s,forceFiltered:o.filtered})}else n.push({column:o,key:yt(o,i),filteredKeys:t&&o.defaultFilteredValue?o.defaultFilteredValue:void 0,forceFiltered:o.filtered});"children"in o&&(n=[].concat(ce(n),ce(Pn(o.children,t,i))))}),n};function Xo(e,t,r,n,o,a,d,i,c){return r.map((s,v)=>{const u=Lt(v,i),{filterOnClose:p=!0,filterMultiple:f=!0,filterMode:m,filterSearch:y}=s;let g=s;if(g.filters||g.filterDropdown){const h=yt(g,u),C=n.find(({key:x})=>h===x);g=Object.assign(Object.assign({},g),{title:x=>l.createElement(js,{tablePrefixCls:e,prefixCls:`${e}-filter`,dropdownPrefixCls:t,column:g,columnKey:h,filterState:C,filterOnClose:p,filterMultiple:f,filterMode:m,filterSearch:y,triggerFilter:a,locale:o,getPopupContainer:d,rootClassName:c},cn(s.title,x))})}return"children"in g&&(g=Object.assign(Object.assign({},g),{children:Xo(e,t,g.children,n,o,a,d,u,c)})),g})}const Xr=e=>{const t={};return e.forEach(({key:r,filteredKeys:n,column:o})=>{const a=r,{filters:d,filterDropdown:i}=o;if(i)t[a]=n||null;else if(Array.isArray(n)){const c=Kt(d);t[a]=c.filter(s=>n.includes(String(s)))}else t[a]=null}),t},Tn=(e,t,r)=>t.reduce((o,a)=>{const{column:{onFilter:d,filters:i},filteredKeys:c}=a;return d&&c&&c.length?o.map(s=>Object.assign({},s)).filter(s=>c.some(v=>{const u=Kt(i),p=u.findIndex(m=>String(m)===String(v)),f=p!==-1?u[p]:v;return s[r]&&(s[r]=Tn(s[r],t,r)),d(f,s)})):o},e),Uo=e=>e.flatMap(t=>"children"in t?[t].concat(ce(Uo(t.children||[]))):[t]),As=e=>{const{prefixCls:t,dropdownPrefixCls:r,mergedColumns:n,onFilterChange:o,getPopupContainer:a,locale:d,rootClassName:i}=e;Fn();const c=l.useMemo(()=>Uo(n||[]),[n]),[s,v]=l.useState(()=>Pn(c,!0)),u=l.useMemo(()=>{const y=Pn(c,!1);if(y.length===0)return y;let g=!0;if(y.forEach(({filteredKeys:h})=>{h!==void 0&&(g=!1)}),g){const h=(c||[]).map((C,x)=>yt(C,Lt(x)));return s.filter(({key:C})=>h.includes(C)).map(C=>{const x=c[h.findIndex(w=>w===C.key)];return Object.assign(Object.assign({},C),{column:Object.assign(Object.assign({},C.column),x),forceFiltered:x.filtered})})}return y},[c,s]),p=l.useMemo(()=>Xr(u),[u]),f=y=>{const g=u.filter(({key:h})=>h!==y.key);g.push(y),v(g),o(Xr(g),g)};return[y=>Xo(t,r,y,u,d,f,a,void 0,i),u,p]},Ws=(e,t,r)=>{const n=l.useRef({});function o(a){var d;if(!n.current||n.current.data!==e||n.current.childrenColumnName!==t||n.current.getRowKey!==r){let c=function(s){s.forEach((v,u)=>{const p=r(v,u);i.set(p,v),v&&typeof v=="object"&&t in v&&c(v[t]||[])})};const i=new Map;c(e),n.current={data:e,childrenColumnName:t,kvMap:i,getRowKey:r}}return(d=n.current.kvMap)===null||d===void 0?void 0:d.get(a)}return[o]};var Vs=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)t.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r};const Yo=10;function qs(e,t){const r={current:e.current,pageSize:e.pageSize};return Object.keys(t&&typeof t=="object"?t:{}).forEach(o=>{const a=e[o];typeof a!="function"&&(r[o]=a)}),r}function Gs(e,t,r){const n=r&&typeof r=="object"?r:{},{total:o=0}=n,a=Vs(n,["total"]),[d,i]=l.useState(()=>({current:"defaultCurrent"in a?a.defaultCurrent:1,pageSize:"defaultPageSize"in a?a.defaultPageSize:Yo})),c=fo(d,a,{total:o>0?o:e}),s=Math.ceil((o||e)/c.pageSize);c.current>s&&(c.current=s||1);const v=(p,f)=>{i({current:p??1,pageSize:f||c.pageSize})},u=(p,f)=>{var m;r&&((m=r.onChange)===null||m===void 0||m.call(r,p,f)),v(p,f),t(p,f||(c==null?void 0:c.pageSize))};return r===!1?[{},()=>{}]:[Object.assign(Object.assign({},c),{onChange:u}),v]}const en="ascend",wn="descend",nn=e=>typeof e.sorter=="object"&&typeof e.sorter.multiple=="number"?e.sorter.multiple:!1,Ur=e=>typeof e=="function"?e:e&&typeof e=="object"&&e.compare?e.compare:!1,Xs=(e,t)=>t?e[e.indexOf(t)+1]:e[0],Dn=(e,t,r)=>{let n=[];const o=(a,d)=>{n.push({column:a,key:yt(a,d),multiplePriority:nn(a),sortOrder:a.sortOrder})};return(e||[]).forEach((a,d)=>{const i=Lt(d,r);a.children?("sortOrder"in a&&o(a,i),n=[].concat(ce(n),ce(Dn(a.children,t,i)))):a.sorter&&("sortOrder"in a?o(a,i):t&&a.defaultSortOrder&&n.push({column:a,key:yt(a,i),multiplePriority:nn(a),sortOrder:a.defaultSortOrder}))}),n},Jo=(e,t,r,n,o,a,d,i)=>(t||[]).map((s,v)=>{const u=Lt(v,i);let p=s;if(p.sorter){const f=p.sortDirections||o,m=p.showSorterTooltip===void 0?d:p.showSorterTooltip,y=yt(p,u),g=r.find(({key:b})=>b===y),h=g?g.sortOrder:null,C=Xs(f,h);let x;if(s.sortIcon)x=s.sortIcon({sortOrder:h});else{const b=f.includes(en)&&l.createElement(yl,{className:J(`${e}-column-sorter-up`,{active:h===en})}),K=f.includes(wn)&&l.createElement(gl,{className:J(`${e}-column-sorter-down`,{active:h===wn})});x=l.createElement("span",{className:J(`${e}-column-sorter`,{[`${e}-column-sorter-full`]:!!(b&&K)})},l.createElement("span",{className:`${e}-column-sorter-inner`,"aria-hidden":"true"},b,K))}const{cancelSort:w,triggerAsc:E,triggerDesc:k}=a||{};let N=w;C===wn?N=k:C===en&&(N=E);const R=typeof m=="object"?Object.assign({title:N},m):{title:N};p=Object.assign(Object.assign({},p),{className:J(p.className,{[`${e}-column-sort`]:h}),title:b=>{const K=`${e}-column-sorters`,I=l.createElement("span",{className:`${e}-column-title`},cn(s.title,b)),D=l.createElement("div",{className:K},I,x);return m?typeof m!="boolean"&&(m==null?void 0:m.target)==="sorter-icon"?l.createElement("div",{className:`${K} ${e}-column-sorters-tooltip-target-sorter`},I,l.createElement(Sr,Object.assign({},R),x)):l.createElement(Sr,Object.assign({},R),D):D},onHeaderCell:b=>{var K;const I=((K=s.onHeaderCell)===null||K===void 0?void 0:K.call(s,b))||{},D=I.onClick,P=I.onKeyDown;I.onClick=O=>{n({column:s,key:y,sortOrder:C,multiplePriority:nn(s)}),D==null||D(O)},I.onKeyDown=O=>{O.keyCode===mt.ENTER&&(n({column:s,key:y,sortOrder:C,multiplePriority:nn(s)}),P==null||P(O))};const $=fs(s.title,{}),S=$==null?void 0:$.toString();return h&&(I["aria-sort"]=h==="ascend"?"ascending":"descending"),I["aria-label"]=S||"",I.className=J(I.className,`${e}-column-has-sorters`),I.tabIndex=0,s.ellipsis&&(I.title=($??"").toString()),I}})}return"children"in p&&(p=Object.assign(Object.assign({},p),{children:Jo(e,p.children,r,n,o,a,d,u)})),p}),Yr=e=>{const{column:t,sortOrder:r}=e;return{column:t,order:r,field:t.dataIndex,columnKey:t.key}},Jr=e=>{const t=e.filter(({sortOrder:r})=>r).map(Yr);if(t.length===0&&e.length){const r=e.length-1;return Object.assign(Object.assign({},Yr(e[r])),{column:void 0,order:void 0,field:void 0,columnKey:void 0})}return t.length<=1?t[0]||{}:t},Mn=(e,t,r)=>{const n=t.slice().sort((d,i)=>i.multiplePriority-d.multiplePriority),o=e.slice(),a=n.filter(({column:{sorter:d},sortOrder:i})=>Ur(d)&&i);return a.length?o.sort((d,i)=>{for(let c=0;c<a.length;c+=1){const s=a[c],{column:{sorter:v},sortOrder:u}=s,p=Ur(v);if(p&&u){const f=p(d,i,u);if(f!==0)return u===en?f:-f}}return 0}).map(d=>{const i=d[r];return i?Object.assign(Object.assign({},d),{[r]:Mn(i,t,r)}):d}):o},Us=e=>{const{prefixCls:t,mergedColumns:r,sortDirections:n,tableLocale:o,showSorterTooltip:a,onSorterChange:d}=e,[i,c]=l.useState(()=>Dn(r,!0)),s=(y,g)=>{const h=[];return y.forEach((C,x)=>{const w=Lt(x,g);if(h.push(yt(C,w)),Array.isArray(C.children)){const E=s(C.children,w);h.push.apply(h,ce(E))}}),h},v=l.useMemo(()=>{let y=!0;const g=Dn(r,!1);if(!g.length){const w=s(r);return i.filter(({key:E})=>w.includes(E))}const h=[];function C(w){y?h.push(w):h.push(Object.assign(Object.assign({},w),{sortOrder:null}))}let x=null;return g.forEach(w=>{x===null?(C(w),w.sortOrder&&(w.multiplePriority===!1?y=!1:x=!0)):(x&&w.multiplePriority!==!1||(y=!1),C(w))}),h},[r,i]),u=l.useMemo(()=>{var y,g;const h=v.map(({column:C,sortOrder:x})=>({column:C,order:x}));return{sortColumns:h,sortColumn:(y=h[0])===null||y===void 0?void 0:y.column,sortOrder:(g=h[0])===null||g===void 0?void 0:g.order}},[v]),p=y=>{let g;y.multiplePriority===!1||!v.length||v[0].multiplePriority===!1?g=[y]:g=[].concat(ce(v.filter(({key:h})=>h!==y.key)),[y]),c(g),d(Jr(g),g)};return[y=>Jo(t,y,v,p,n,o,a),v,u,()=>Jr(v)]},Qo=(e,t)=>e.map(n=>{const o=Object.assign({},n);return o.title=cn(n.title,t),"children"in o&&(o.children=Qo(o.children,t)),o}),Ys=e=>[l.useCallback(r=>Qo(r,e),[e])],Js=Ho((e,t)=>{const{_renderTimes:r}=e,{_renderTimes:n}=t;return r!==n}),Qs=Fo((e,t)=>{const{_renderTimes:r}=e,{_renderTimes:n}=t;return r!==n}),Zs=e=>{const{componentCls:t,lineWidth:r,lineType:n,tableBorderColor:o,tableHeaderBg:a,tablePaddingVertical:d,tablePaddingHorizontal:i,calc:c}=e,s=`${V(r)} ${n} ${o}`,v=(u,p,f)=>({[`&${t}-${u}`]:{[`> ${t}-container`]:{[`> ${t}-content, > ${t}-body`]:{"\n            > table > tbody > tr > th,\n            > table > tbody > tr > td\n          ":{[`> ${t}-expanded-row-fixed`]:{margin:`${V(c(p).mul(-1).equal())}
              ${V(c(c(f).add(r)).mul(-1).equal())}`}}}}}});return{[`${t}-wrapper`]:{[`${t}${t}-bordered`]:Object.assign(Object.assign(Object.assign({[`> ${t}-title`]:{border:s,borderBottom:0},[`> ${t}-container`]:{borderInlineStart:s,borderTop:s,[`
            > ${t}-content,
            > ${t}-header,
            > ${t}-body,
            > ${t}-summary
          `]:{"> table":{"\n                > thead > tr > th,\n                > thead > tr > td,\n                > tbody > tr > th,\n                > tbody > tr > td,\n                > tfoot > tr > th,\n                > tfoot > tr > td\n              ":{borderInlineEnd:s},"> thead":{"> tr:not(:last-child) > th":{borderBottom:s},"> tr > th::before":{backgroundColor:"transparent !important"}},"\n                > thead > tr,\n                > tbody > tr,\n                > tfoot > tr\n              ":{[`> ${t}-cell-fix-right-first::after`]:{borderInlineEnd:s}},"\n                > tbody > tr > th,\n                > tbody > tr > td\n              ":{[`> ${t}-expanded-row-fixed`]:{margin:`${V(c(d).mul(-1).equal())} ${V(c(c(i).add(r)).mul(-1).equal())}`,"&::after":{position:"absolute",top:0,insetInlineEnd:r,bottom:0,borderInlineEnd:s,content:'""'}}}}}},[`&${t}-scroll-horizontal`]:{[`> ${t}-container > ${t}-body`]:{"> table > tbody":{[`
                > tr${t}-expanded-row,
                > tr${t}-placeholder
              `]:{"> th, > td":{borderInlineEnd:0}}}}}},v("middle",e.tablePaddingVerticalMiddle,e.tablePaddingHorizontalMiddle)),v("small",e.tablePaddingVerticalSmall,e.tablePaddingHorizontalSmall)),{[`> ${t}-footer`]:{border:s,borderTop:0}}),[`${t}-cell`]:{[`${t}-container:first-child`]:{borderTop:0},"&-scrollbar:not([rowspan])":{boxShadow:`0 ${V(r)} 0 ${V(r)} ${a}`}},[`${t}-bordered ${t}-cell-scrollbar`]:{borderInlineEnd:s}}}},ec=e=>{const{componentCls:t}=e;return{[`${t}-wrapper`]:{[`${t}-cell-ellipsis`]:Object.assign(Object.assign({},ka),{wordBreak:"keep-all",[`
          &${t}-cell-fix-left-last,
          &${t}-cell-fix-right-first
        `]:{overflow:"visible",[`${t}-cell-content`]:{display:"block",overflow:"hidden",textOverflow:"ellipsis"}},[`${t}-column-title`]:{overflow:"hidden",textOverflow:"ellipsis",wordBreak:"keep-all"}})}}},tc=e=>{const{componentCls:t}=e;return{[`${t}-wrapper`]:{[`${t}-tbody > tr${t}-placeholder`]:{textAlign:"center",color:e.colorTextDisabled,"\n          &:hover > th,\n          &:hover > td,\n        ":{background:e.colorBgContainer}}}}},nc=e=>{const{componentCls:t,antCls:r,motionDurationSlow:n,lineWidth:o,paddingXS:a,lineType:d,tableBorderColor:i,tableExpandIconBg:c,tableExpandColumnWidth:s,borderRadius:v,tablePaddingVertical:u,tablePaddingHorizontal:p,tableExpandedRowBg:f,paddingXXS:m,expandIconMarginTop:y,expandIconSize:g,expandIconHalfInner:h,expandIconScale:C,calc:x}=e,w=`${V(o)} ${d} ${i}`,E=x(m).sub(o).equal();return{[`${t}-wrapper`]:{[`${t}-expand-icon-col`]:{width:s},[`${t}-row-expand-icon-cell`]:{textAlign:"center",[`${t}-row-expand-icon`]:{display:"inline-flex",float:"none",verticalAlign:"sub"}},[`${t}-row-indent`]:{height:1,float:"left"},[`${t}-row-expand-icon`]:Object.assign(Object.assign({},Na(e)),{position:"relative",float:"left",width:g,height:g,color:"inherit",lineHeight:V(g),background:c,border:w,borderRadius:v,transform:`scale(${C})`,"&:focus, &:hover, &:active":{borderColor:"currentcolor"},"&::before, &::after":{position:"absolute",background:"currentcolor",transition:`transform ${n} ease-out`,content:'""'},"&::before":{top:h,insetInlineEnd:E,insetInlineStart:E,height:o},"&::after":{top:E,bottom:E,insetInlineStart:h,width:o,transform:"rotate(90deg)"},"&-collapsed::before":{transform:"rotate(-180deg)"},"&-collapsed::after":{transform:"rotate(0deg)"},"&-spaced":{"&::before, &::after":{display:"none",content:"none"},background:"transparent",border:0,visibility:"hidden"}}),[`${t}-row-indent + ${t}-row-expand-icon`]:{marginTop:y,marginInlineEnd:a},[`tr${t}-expanded-row`]:{"&, &:hover":{"> th, > td":{background:f}},[`${r}-descriptions-view`]:{display:"flex",table:{flex:"auto",width:"100%"}}},[`${t}-expanded-row-fixed`]:{position:"relative",margin:`${V(x(u).mul(-1).equal())} ${V(x(p).mul(-1).equal())}`,padding:`${V(u)} ${V(p)}`}}}},rc=e=>{const{componentCls:t,antCls:r,iconCls:n,tableFilterDropdownWidth:o,tableFilterDropdownSearchWidth:a,paddingXXS:d,paddingXS:i,colorText:c,lineWidth:s,lineType:v,tableBorderColor:u,headerIconColor:p,fontSizeSM:f,tablePaddingHorizontal:m,borderRadius:y,motionDurationSlow:g,colorIcon:h,colorPrimary:C,tableHeaderFilterActiveBg:x,colorTextDisabled:w,tableFilterDropdownBg:E,tableFilterDropdownHeight:k,controlItemBgHover:N,controlItemBgActive:R,boxShadowSecondary:b,filterDropdownMenuBg:K,calc:I}=e,D=`${r}-dropdown`,P=`${t}-filter-dropdown`,$=`${r}-tree`,S=`${V(s)} ${v} ${u}`;return[{[`${t}-wrapper`]:{[`${t}-filter-column`]:{display:"flex",justifyContent:"space-between"},[`${t}-filter-trigger`]:{position:"relative",display:"flex",alignItems:"center",marginBlock:I(d).mul(-1).equal(),marginInline:`${V(d)} ${V(I(m).div(2).mul(-1).equal())}`,padding:`0 ${V(d)}`,color:p,fontSize:f,borderRadius:y,cursor:"pointer",transition:`all ${g}`,"&:hover":{color:h,background:x},"&.active":{color:C}}}},{[`${r}-dropdown`]:{[P]:Object.assign(Object.assign({},dt(e)),{minWidth:o,backgroundColor:E,borderRadius:y,boxShadow:b,overflow:"hidden",[`${D}-menu`]:{maxHeight:k,overflowX:"hidden",border:0,boxShadow:"none",borderRadius:"unset",backgroundColor:K,"&:empty::after":{display:"block",padding:`${V(i)} 0`,color:w,fontSize:f,textAlign:"center",content:'"Not Found"'}},[`${P}-tree`]:{paddingBlock:`${V(i)} 0`,paddingInline:i,[$]:{padding:0},[`${$}-treenode ${$}-node-content-wrapper:hover`]:{backgroundColor:N},[`${$}-treenode-checkbox-checked ${$}-node-content-wrapper`]:{"&, &:hover":{backgroundColor:R}}},[`${P}-search`]:{padding:i,borderBottom:S,"&-input":{input:{minWidth:a},[n]:{color:w}}},[`${P}-checkall`]:{width:"100%",marginBottom:d,marginInlineStart:d},[`${P}-btns`]:{display:"flex",justifyContent:"space-between",padding:`${V(I(i).sub(s).equal())} ${V(i)}`,overflow:"hidden",borderTop:S}})}},{[`${r}-dropdown ${P}, ${P}-submenu`]:{[`${r}-checkbox-wrapper + span`]:{paddingInlineStart:i,color:c},"> ul":{maxHeight:"calc(100vh - 130px)",overflowX:"hidden",overflowY:"auto"}}}]},oc=e=>{const{componentCls:t,lineWidth:r,colorSplit:n,motionDurationSlow:o,zIndexTableFixed:a,tableBg:d,zIndexTableSticky:i,calc:c}=e,s=n;return{[`${t}-wrapper`]:{[`
        ${t}-cell-fix-left,
        ${t}-cell-fix-right
      `]:{position:"sticky !important",zIndex:a,background:d},[`
        ${t}-cell-fix-left-first::after,
        ${t}-cell-fix-left-last::after
      `]:{position:"absolute",top:0,right:{_skip_check_:!0,value:0},bottom:c(r).mul(-1).equal(),width:30,transform:"translateX(100%)",transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},[`${t}-cell-fix-left-all::after`]:{display:"none"},[`
        ${t}-cell-fix-right-first::after,
        ${t}-cell-fix-right-last::after
      `]:{position:"absolute",top:0,bottom:c(r).mul(-1).equal(),left:{_skip_check_:!0,value:0},width:30,transform:"translateX(-100%)",transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},[`${t}-container`]:{position:"relative","&::before, &::after":{position:"absolute",top:0,bottom:0,zIndex:c(i).add(1).equal({unit:!1}),width:30,transition:`box-shadow ${o}`,content:'""',pointerEvents:"none"},"&::before":{insetInlineStart:0},"&::after":{insetInlineEnd:0}},[`${t}-ping-left`]:{[`&:not(${t}-has-fix-left) ${t}-container::before`]:{boxShadow:`inset 10px 0 8px -8px ${s}`},[`
          ${t}-cell-fix-left-first::after,
          ${t}-cell-fix-left-last::after
        `]:{boxShadow:`inset 10px 0 8px -8px ${s}`},[`${t}-cell-fix-left-last::before`]:{backgroundColor:"transparent !important"}},[`${t}-ping-right`]:{[`&:not(${t}-has-fix-right) ${t}-container::after`]:{boxShadow:`inset -10px 0 8px -8px ${s}`},[`
          ${t}-cell-fix-right-first::after,
          ${t}-cell-fix-right-last::after
        `]:{boxShadow:`inset -10px 0 8px -8px ${s}`}},[`${t}-fixed-column-gapped`]:{[`
        ${t}-cell-fix-left-first::after,
        ${t}-cell-fix-left-last::after,
        ${t}-cell-fix-right-first::after,
        ${t}-cell-fix-right-last::after
      `]:{boxShadow:"none"}}}}},ac=e=>{const{componentCls:t,antCls:r,margin:n}=e;return{[`${t}-wrapper`]:{[`${t}-pagination${r}-pagination`]:{margin:`${V(n)} 0`},[`${t}-pagination`]:{display:"flex",flexWrap:"wrap",rowGap:e.paddingXS,"> *":{flex:"none"},"&-left":{justifyContent:"flex-start"},"&-center":{justifyContent:"center"},"&-right":{justifyContent:"flex-end"}}}}},lc=e=>{const{componentCls:t,tableRadius:r}=e;return{[`${t}-wrapper`]:{[t]:{[`${t}-title, ${t}-header`]:{borderRadius:`${V(r)} ${V(r)} 0 0`},[`${t}-title + ${t}-container`]:{borderStartStartRadius:0,borderStartEndRadius:0,[`${t}-header, table`]:{borderRadius:0},"table > thead > tr:first-child":{"th:first-child, th:last-child, td:first-child, td:last-child":{borderRadius:0}}},"&-container":{borderStartStartRadius:r,borderStartEndRadius:r,"table > thead > tr:first-child":{"> *:first-child":{borderStartStartRadius:r},"> *:last-child":{borderStartEndRadius:r}}},"&-footer":{borderRadius:`0 0 ${V(r)} ${V(r)}`}}}}},ic=e=>{const{componentCls:t}=e;return{[`${t}-wrapper-rtl`]:{direction:"rtl",table:{direction:"rtl"},[`${t}-pagination-left`]:{justifyContent:"flex-end"},[`${t}-pagination-right`]:{justifyContent:"flex-start"},[`${t}-row-expand-icon`]:{float:"right","&::after":{transform:"rotate(-90deg)"},"&-collapsed::before":{transform:"rotate(180deg)"},"&-collapsed::after":{transform:"rotate(0deg)"}},[`${t}-container`]:{"&::before":{insetInlineStart:"unset",insetInlineEnd:0},"&::after":{insetInlineStart:0,insetInlineEnd:"unset"},[`${t}-row-indent`]:{float:"right"}}}}},sc=e=>{const{componentCls:t,antCls:r,iconCls:n,fontSizeIcon:o,padding:a,paddingXS:d,headerIconColor:i,headerIconHoverColor:c,tableSelectionColumnWidth:s,tableSelectedRowBg:v,tableSelectedRowHoverBg:u,tableRowHoverBg:p,tablePaddingHorizontal:f,calc:m}=e;return{[`${t}-wrapper`]:{[`${t}-selection-col`]:{width:s,[`&${t}-selection-col-with-dropdown`]:{width:m(s).add(o).add(m(a).div(4)).equal()}},[`${t}-bordered ${t}-selection-col`]:{width:m(s).add(m(d).mul(2)).equal(),[`&${t}-selection-col-with-dropdown`]:{width:m(s).add(o).add(m(a).div(4)).add(m(d).mul(2)).equal()}},[`
        table tr th${t}-selection-column,
        table tr td${t}-selection-column,
        ${t}-selection-column
      `]:{paddingInlineEnd:e.paddingXS,paddingInlineStart:e.paddingXS,textAlign:"center",[`${r}-radio-wrapper`]:{marginInlineEnd:0}},[`table tr th${t}-selection-column${t}-cell-fix-left`]:{zIndex:m(e.zIndexTableFixed).add(1).equal({unit:!1})},[`table tr th${t}-selection-column::after`]:{backgroundColor:"transparent !important"},[`${t}-selection`]:{position:"relative",display:"inline-flex",flexDirection:"column"},[`${t}-selection-extra`]:{position:"absolute",top:0,zIndex:1,cursor:"pointer",transition:`all ${e.motionDurationSlow}`,marginInlineStart:"100%",paddingInlineStart:V(m(f).div(4).equal()),[n]:{color:i,fontSize:o,verticalAlign:"baseline","&:hover":{color:c}}},[`${t}-tbody`]:{[`${t}-row`]:{[`&${t}-row-selected`]:{[`> ${t}-cell`]:{background:v,"&-row-hover":{background:u}}},[`> ${t}-cell-row-hover`]:{background:p}}}}}},cc=e=>{const{componentCls:t,tableExpandColumnWidth:r,calc:n}=e,o=(a,d,i,c)=>({[`${t}${t}-${a}`]:{fontSize:c,[`
        ${t}-title,
        ${t}-footer,
        ${t}-cell,
        ${t}-thead > tr > th,
        ${t}-tbody > tr > th,
        ${t}-tbody > tr > td,
        tfoot > tr > th,
        tfoot > tr > td
      `]:{padding:`${V(d)} ${V(i)}`},[`${t}-filter-trigger`]:{marginInlineEnd:V(n(i).div(2).mul(-1).equal())},[`${t}-expanded-row-fixed`]:{margin:`${V(n(d).mul(-1).equal())} ${V(n(i).mul(-1).equal())}`},[`${t}-tbody`]:{[`${t}-wrapper:only-child ${t}`]:{marginBlock:V(n(d).mul(-1).equal()),marginInline:`${V(n(r).sub(i).equal())} ${V(n(i).mul(-1).equal())}`}},[`${t}-selection-extra`]:{paddingInlineStart:V(n(i).div(4).equal())}}});return{[`${t}-wrapper`]:Object.assign(Object.assign({},o("middle",e.tablePaddingVerticalMiddle,e.tablePaddingHorizontalMiddle,e.tableFontSizeMiddle)),o("small",e.tablePaddingVerticalSmall,e.tablePaddingHorizontalSmall,e.tableFontSizeSmall))}},dc=e=>{const{componentCls:t,marginXXS:r,fontSizeIcon:n,headerIconColor:o,headerIconHoverColor:a}=e;return{[`${t}-wrapper`]:{[`${t}-thead th${t}-column-has-sorters`]:{outline:"none",cursor:"pointer",transition:`all ${e.motionDurationSlow}, left 0s`,"&:hover":{background:e.tableHeaderSortHoverBg,"&::before":{backgroundColor:"transparent !important"}},"&:focus-visible":{color:e.colorPrimary},[`
          &${t}-cell-fix-left:hover,
          &${t}-cell-fix-right:hover
        `]:{background:e.tableFixedHeaderSortActiveBg}},[`${t}-thead th${t}-column-sort`]:{background:e.tableHeaderSortBg,"&::before":{backgroundColor:"transparent !important"}},[`td${t}-column-sort`]:{background:e.tableBodySortBg},[`${t}-column-title`]:{position:"relative",zIndex:1,flex:1,minWidth:0},[`${t}-column-sorters`]:{display:"flex",flex:"auto",alignItems:"center",justifyContent:"space-between","&::after":{position:"absolute",inset:0,width:"100%",height:"100%",content:'""'}},[`${t}-column-sorters-tooltip-target-sorter`]:{"&::after":{content:"none"}},[`${t}-column-sorter`]:{marginInlineStart:r,color:o,fontSize:0,transition:`color ${e.motionDurationSlow}`,"&-inner":{display:"inline-flex",flexDirection:"column",alignItems:"center"},"&-up, &-down":{fontSize:n,"&.active":{color:e.colorPrimary}},[`${t}-column-sorter-up + ${t}-column-sorter-down`]:{marginTop:"-0.3em"}},[`${t}-column-sorters:hover ${t}-column-sorter`]:{color:a}}}},uc=e=>{const{componentCls:t,opacityLoading:r,tableScrollThumbBg:n,tableScrollThumbBgHover:o,tableScrollThumbSize:a,tableScrollBg:d,zIndexTableSticky:i,stickyScrollBarBorderRadius:c,lineWidth:s,lineType:v,tableBorderColor:u}=e,p=`${V(s)} ${v} ${u}`;return{[`${t}-wrapper`]:{[`${t}-sticky`]:{"&-holder":{position:"sticky",zIndex:i,background:e.colorBgContainer},"&-scroll":{position:"sticky",bottom:0,height:`${V(a)} !important`,zIndex:i,display:"flex",alignItems:"center",background:d,borderTop:p,opacity:r,"&:hover":{transformOrigin:"center bottom"},"&-bar":{height:a,backgroundColor:n,borderRadius:c,transition:`all ${e.motionDurationSlow}, transform 0s`,position:"absolute",bottom:0,"&:hover, &-active":{backgroundColor:o}}}}}}},Qr=e=>{const{componentCls:t,lineWidth:r,tableBorderColor:n,calc:o}=e,a=`${V(r)} ${e.lineType} ${n}`;return{[`${t}-wrapper`]:{[`${t}-summary`]:{position:"relative",zIndex:e.zIndexTableFixed,background:e.tableBg,"> tr":{"> th, > td":{borderBottom:a}}},[`div${t}-summary`]:{boxShadow:`0 ${V(o(r).mul(-1).equal())} 0 ${n}`}}}},fc=e=>{const{componentCls:t,motionDurationMid:r,lineWidth:n,lineType:o,tableBorderColor:a,calc:d}=e,i=`${V(n)} ${o} ${a}`,c=`${t}-expanded-row-cell`;return{[`${t}-wrapper`]:{[`${t}-tbody-virtual`]:{[`${t}-tbody-virtual-holder-inner`]:{[`
            & > ${t}-row, 
            & > div:not(${t}-row) > ${t}-row
          `]:{display:"flex",boxSizing:"border-box",width:"100%"}},[`${t}-cell`]:{borderBottom:i,transition:`background ${r}`},[`${t}-expanded-row`]:{[`${c}${c}-fixed`]:{position:"sticky",insetInlineStart:0,overflow:"hidden",width:`calc(var(--virtual-width) - ${V(n)})`,borderInlineEnd:"none"}}},[`${t}-bordered`]:{[`${t}-tbody-virtual`]:{"&:after":{content:'""',insetInline:0,bottom:0,borderBottom:i,position:"absolute"},[`${t}-cell`]:{borderInlineEnd:i,[`&${t}-cell-fix-right-first:before`]:{content:'""',position:"absolute",insetBlock:0,insetInlineStart:d(n).mul(-1).equal(),borderInlineStart:i}}},[`&${t}-virtual`]:{[`${t}-placeholder ${t}-cell`]:{borderInlineEnd:i,borderBottom:i}}}}}},vc=e=>{const{componentCls:t,fontWeightStrong:r,tablePaddingVertical:n,tablePaddingHorizontal:o,tableExpandColumnWidth:a,lineWidth:d,lineType:i,tableBorderColor:c,tableFontSize:s,tableBg:v,tableRadius:u,tableHeaderTextColor:p,motionDurationMid:f,tableHeaderBg:m,tableHeaderCellSplitColor:y,tableFooterTextColor:g,tableFooterBg:h,calc:C}=e,x=`${V(d)} ${i} ${c}`;return{[`${t}-wrapper`]:Object.assign(Object.assign({clear:"both",maxWidth:"100%"},Ra()),{[t]:Object.assign(Object.assign({},dt(e)),{fontSize:s,background:v,borderRadius:`${V(u)} ${V(u)} 0 0`,scrollbarColor:`${e.tableScrollThumbBg} ${e.tableScrollBg}`}),table:{width:"100%",textAlign:"start",borderRadius:`${V(u)} ${V(u)} 0 0`,borderCollapse:"separate",borderSpacing:0},[`
          ${t}-cell,
          ${t}-thead > tr > th,
          ${t}-tbody > tr > th,
          ${t}-tbody > tr > td,
          tfoot > tr > th,
          tfoot > tr > td
        `]:{position:"relative",padding:`${V(n)} ${V(o)}`,overflowWrap:"break-word"},[`${t}-title`]:{padding:`${V(n)} ${V(o)}`},[`${t}-thead`]:{"\n          > tr > th,\n          > tr > td\n        ":{position:"relative",color:p,fontWeight:r,textAlign:"start",background:m,borderBottom:x,transition:`background ${f} ease`,"&[colspan]:not([colspan='1'])":{textAlign:"center"},[`&:not(:last-child):not(${t}-selection-column):not(${t}-row-expand-icon-cell):not([colspan])::before`]:{position:"absolute",top:"50%",insetInlineEnd:0,width:1,height:"1.6em",backgroundColor:y,transform:"translateY(-50%)",transition:`background-color ${f}`,content:'""'}},"> tr:not(:last-child) > th[colspan]":{borderBottom:0}},[`${t}-tbody`]:{"> tr":{"> th, > td":{transition:`background ${f}, border-color ${f}`,borderBottom:x,[`
              > ${t}-wrapper:only-child,
              > ${t}-expanded-row-fixed > ${t}-wrapper:only-child
            `]:{[t]:{marginBlock:V(C(n).mul(-1).equal()),marginInline:`${V(C(a).sub(o).equal())}
                ${V(C(o).mul(-1).equal())}`,[`${t}-tbody > tr:last-child > td`]:{borderBottomWidth:0,"&:first-child, &:last-child":{borderRadius:0}}}}},"> th":{position:"relative",color:p,fontWeight:r,textAlign:"start",background:m,borderBottom:x,transition:`background ${f} ease`}}},[`${t}-footer`]:{padding:`${V(n)} ${V(o)}`,color:g,background:h}})}},pc=e=>{const{colorFillAlter:t,colorBgContainer:r,colorTextHeading:n,colorFillSecondary:o,colorFillContent:a,controlItemBgActive:d,controlItemBgActiveHover:i,padding:c,paddingSM:s,paddingXS:v,colorBorderSecondary:u,borderRadiusLG:p,controlHeight:f,colorTextPlaceholder:m,fontSize:y,fontSizeSM:g,lineHeight:h,lineWidth:C,colorIcon:x,colorIconHover:w,opacityLoading:E,controlInteractiveSize:k}=e,N=new Ht(o).onBackground(r).toHexString(),R=new Ht(a).onBackground(r).toHexString(),b=new Ht(t).onBackground(r).toHexString(),K=new Ht(x),I=new Ht(w),D=k/2-C,P=D*2+C*3;return{headerBg:b,headerColor:n,headerSortActiveBg:N,headerSortHoverBg:R,bodySortBg:b,rowHoverBg:b,rowSelectedBg:d,rowSelectedHoverBg:i,rowExpandedBg:t,cellPaddingBlock:c,cellPaddingInline:c,cellPaddingBlockMD:s,cellPaddingInlineMD:v,cellPaddingBlockSM:v,cellPaddingInlineSM:v,borderColor:u,headerBorderRadius:p,footerBg:b,footerColor:n,cellFontSize:y,cellFontSizeMD:y,cellFontSizeSM:y,headerSplitColor:u,fixedHeaderSortActiveBg:N,headerFilterHoverBg:a,filterDropdownMenuBg:r,filterDropdownBg:r,expandIconBg:r,selectionColumnWidth:f,stickyScrollBarBg:m,stickyScrollBarBorderRadius:100,expandIconMarginTop:(y*h-C*3)/2-Math.ceil((g*1.4-C*3)/2),headerIconColor:K.clone().setA(K.a*E).toRgbString(),headerIconHoverColor:I.clone().setA(I.a*E).toRgbString(),expandIconHalfInner:D,expandIconSize:P,expandIconScale:k/P}},Zr=2,mc=rn("Table",e=>{const{colorTextHeading:t,colorSplit:r,colorBgContainer:n,controlInteractiveSize:o,headerBg:a,headerColor:d,headerSortActiveBg:i,headerSortHoverBg:c,bodySortBg:s,rowHoverBg:v,rowSelectedBg:u,rowSelectedHoverBg:p,rowExpandedBg:f,cellPaddingBlock:m,cellPaddingInline:y,cellPaddingBlockMD:g,cellPaddingInlineMD:h,cellPaddingBlockSM:C,cellPaddingInlineSM:x,borderColor:w,footerBg:E,footerColor:k,headerBorderRadius:N,cellFontSize:R,cellFontSizeMD:b,cellFontSizeSM:K,headerSplitColor:I,fixedHeaderSortActiveBg:D,headerFilterHoverBg:P,filterDropdownBg:$,expandIconBg:S,selectionColumnWidth:O,stickyScrollBarBg:T,calc:_}=e,M=on(e,{tableFontSize:R,tableBg:n,tableRadius:N,tablePaddingVertical:m,tablePaddingHorizontal:y,tablePaddingVerticalMiddle:g,tablePaddingHorizontalMiddle:h,tablePaddingVerticalSmall:C,tablePaddingHorizontalSmall:x,tableBorderColor:w,tableHeaderTextColor:d,tableHeaderBg:a,tableFooterTextColor:k,tableFooterBg:E,tableHeaderCellSplitColor:I,tableHeaderSortBg:i,tableHeaderSortHoverBg:c,tableBodySortBg:s,tableFixedHeaderSortActiveBg:D,tableHeaderFilterActiveBg:P,tableFilterDropdownBg:$,tableRowHoverBg:v,tableSelectedRowBg:u,tableSelectedRowHoverBg:p,zIndexTableFixed:Zr,zIndexTableSticky:_(Zr).add(1).equal({unit:!1}),tableFontSizeMiddle:b,tableFontSizeSmall:K,tableSelectionColumnWidth:O,tableExpandIconBg:S,tableExpandColumnWidth:_(o).add(_(e.padding).mul(2)).equal(),tableExpandedRowBg:f,tableFilterDropdownWidth:120,tableFilterDropdownHeight:264,tableFilterDropdownSearchWidth:140,tableScrollThumbSize:8,tableScrollThumbBg:T,tableScrollThumbBgHover:t,tableScrollBg:r});return[vc(M),ac(M),Qr(M),dc(M),rc(M),Zs(M),lc(M),nc(M),Qr(M),tc(M),sc(M),oc(M),uc(M),ec(M),cc(M),ic(M),fc(M)]},pc,{unitless:{expandIconScale:!0}}),gc=[],hc=(e,t)=>{var r,n;const{prefixCls:o,className:a,rootClassName:d,style:i,size:c,bordered:s,dropdownPrefixCls:v,dataSource:u,pagination:p,rowSelection:f,rowKey:m="key",rowClassName:y,columns:g,children:h,childrenColumnName:C,onChange:x,getPopupContainer:w,loading:E,expandIcon:k,expandable:N,expandedRowRender:R,expandIconColumnIndex:b,indentSize:K,scroll:I,sortDirections:D,locale:P,showSorterTooltip:$={target:"full-header"},virtual:S}=e;Fn();const O=l.useMemo(()=>g||qn(h),[g,h]),T=l.useMemo(()=>O.some(oe=>oe.responsive),[O]),_=Ia(T),M=l.useMemo(()=>{const oe=new Set(Object.keys(_).filter(ae=>_[ae]));return O.filter(ae=>!ae.responsive||ae.responsive.some(Ke=>oe.has(Ke)))},[O,_]),A=Ln(e,["className","style","columns"]),{locale:Y=Oa,direction:q,table:ee,renderEmpty:be,getPrefixCls:Ee,getPopupContainer:we}=l.useContext(ut),Q=ao(c),Z=Object.assign(Object.assign({},Y.Table),P),Ce=u||gc,me=Ee("table",o),G=Ee("dropdown",v),[,X]=co(),F=Vt(me),[W,te,le]=mc(me,F),ge=Object.assign(Object.assign({childrenColumnName:C,expandIconColumnIndex:b},N),{expandIcon:(r=N==null?void 0:N.expandIcon)!==null&&r!==void 0?r:(n=ee==null?void 0:ee.expandable)===null||n===void 0?void 0:n.expandIcon}),{childrenColumnName:ke="children"}=ge,Fe=l.useMemo(()=>Ce.some(oe=>oe==null?void 0:oe[ke])?"nest":R||N!=null&&N.expandedRowRender?"row":null,[Ce]),Re={body:l.useRef(null)},L=us(me),j=l.useRef(null),re=l.useRef(null);cs(t,()=>Object.assign(Object.assign({},re.current),{nativeElement:j.current}));const ye=l.useMemo(()=>typeof m=="function"?m:oe=>oe==null?void 0:oe[m],[m]),[ie]=Ws(Ce,ke,ye),Ne={},Pe=(oe,ae,Ke=!1)=>{var Be,Ae,et,tt;const We=Object.assign(Object.assign({},Ne),oe);Ke&&((Be=Ne.resetPagination)===null||Be===void 0||Be.call(Ne),!((Ae=We.pagination)===null||Ae===void 0)&&Ae.current&&(We.pagination.current=1),p&&((et=p.onChange)===null||et===void 0||et.call(p,1,(tt=We.pagination)===null||tt===void 0?void 0:tt.pageSize))),I&&I.scrollToFirstRowOnChange!==!1&&Re.body.current&&Ha(0,{getContainer:()=>Re.body.current}),x==null||x(We.pagination,We.filters,We.sorter,{currentDataSource:Tn(Mn(Ce,We.sorterStates,ke),We.filterStates,ke),action:ae})},Le=(oe,ae)=>{Pe({sorter:oe,sorterStates:ae},"sort",!1)},[he,de,U,H]=Us({prefixCls:me,mergedColumns:M,onSorterChange:Le,sortDirections:D||["ascend","descend"],tableLocale:Z,showSorterTooltip:$}),$e=l.useMemo(()=>Mn(Ce,de,ke),[Ce,de]);Ne.sorter=H(),Ne.sorterStates=de;const Ie=(oe,ae)=>{Pe({filters:oe,filterStates:ae},"filter",!0)},[ne,Te,xe]=As({prefixCls:me,locale:Z,dropdownPrefixCls:G,mergedColumns:M,onFilterChange:Ie,getPopupContainer:w||we,rootClassName:J(d,F)}),De=Tn($e,Te,ke);Ne.filters=xe,Ne.filterStates=Te;const Ue=l.useMemo(()=>{const oe={};return Object.keys(xe).forEach(ae=>{xe[ae]!==null&&(oe[ae]=xe[ae])}),Object.assign(Object.assign({},U),{filters:oe})},[U,xe]),[Ve]=Ys(Ue),ft=(oe,ae)=>{Pe({pagination:Object.assign(Object.assign({},Ne.pagination),{current:oe,pageSize:ae})},"paginate")},[Oe,kt]=Gs(De.length,ft,p);Ne.pagination=p===!1?{}:qs(Oe,p),Ne.resetPagination=kt;const Nt=l.useMemo(()=>{if(p===!1||!Oe.pageSize)return De;const{current:oe=1,total:ae,pageSize:Ke=Yo}=Oe;return De.length<ae?De.length>Ke?De.slice((oe-1)*Ke,oe*Ke):De:De.slice((oe-1)*Ke,oe*Ke)},[!!p,De,Oe==null?void 0:Oe.current,Oe==null?void 0:Oe.pageSize,Oe==null?void 0:Oe.total]),[qe,Qe]=is({prefixCls:me,data:De,pageData:Nt,getRowKey:ye,getRecordByKey:ie,expandType:Fe,childrenColumnName:ke,locale:Z,getPopupContainer:w||we},f),Ze=(oe,ae,Ke)=>{let Be;return typeof y=="function"?Be=J(y(oe,ae,Ke)):Be=J(y),J({[`${me}-row-selected`]:Qe.has(ye(oe,ae))},Be)};ge.__PARENT_RENDER_ICON__=ge.expandIcon,ge.expandIcon=ge.expandIcon||k||ds(Z),Fe==="nest"&&ge.expandIconColumnIndex===void 0?ge.expandIconColumnIndex=f?1:0:ge.expandIconColumnIndex>0&&f&&(ge.expandIconColumnIndex-=1),typeof ge.indentSize!="number"&&(ge.indentSize=typeof K=="number"?K:15);const Ge=l.useCallback(oe=>Ve(qe(ne(he(oe)))),[he,ne,qe]);let je,Ye;if(p!==!1&&(Oe!=null&&Oe.total)){let oe;Oe.size?oe=Oe.size:oe=Q==="small"||Q==="middle"?"small":void 0;const ae=Ae=>l.createElement(Ma,Object.assign({},Oe,{className:J(`${me}-pagination ${me}-pagination-${Ae}`,Oe.className),size:oe})),Ke=q==="rtl"?"left":"right",{position:Be}=Oe;if(Be!==null&&Array.isArray(Be)){const Ae=Be.find(We=>We.includes("top")),et=Be.find(We=>We.includes("bottom")),tt=Be.every(We=>`${We}`=="none");!Ae&&!et&&!tt&&(Ye=ae(Ke)),Ae&&(je=ae(Ae.toLowerCase().replace("top",""))),et&&(Ye=ae(et.toLowerCase().replace("bottom","")))}else Ye=ae(Ke)}let Ct;typeof E=="boolean"?Ct={spinning:E}:typeof E=="object"&&(Ct=Object.assign({spinning:!0},E));const vt=J(le,F,`${me}-wrapper`,ee==null?void 0:ee.className,{[`${me}-wrapper-rtl`]:q==="rtl"},a,d,te),at=Object.assign(Object.assign({},ee==null?void 0:ee.style),i),dn=typeof(P==null?void 0:P.emptyText)<"u"?P.emptyText:(be==null?void 0:be("Table"))||l.createElement(Da,{componentName:"Table"}),un=S?Qs:Js,Ut={},fn=l.useMemo(()=>{const{fontSize:oe,lineHeight:ae,lineWidth:Ke,padding:Be,paddingXS:Ae,paddingSM:et}=X,tt=Math.floor(oe*ae);switch(Q){case"middle":return et*2+tt+Ke;case"small":return Ae*2+tt+Ke;default:return Be*2+tt+Ke}},[X,Q]);return S&&(Ut.listItemHeight=fn),W(l.createElement("div",{ref:j,className:vt,style:at},l.createElement(Ka,Object.assign({spinning:!1},Ct),je,l.createElement(un,Object.assign({},Ut,A,{ref:re,columns:M,direction:q,expandable:ge,prefixCls:me,className:J({[`${me}-middle`]:Q==="middle",[`${me}-small`]:Q==="small",[`${me}-bordered`]:s,[`${me}-empty`]:Ce.length===0},le,F,te),data:Nt,rowKey:ye,rowClassName:Ze,emptyText:dn,internalHooks:Xt,internalRefs:Re,transformColumns:Ge,getContainerWidth:L})),Ye)))},bc=l.forwardRef(hc),yc=(e,t)=>{const r=l.useRef(0);return r.current+=1,l.createElement(bc,Object.assign({},e,{ref:t,_renderTimes:r.current}))},xt=l.forwardRef(yc);xt.SELECTION_COLUMN=pt;xt.EXPAND_COLUMN=ct;xt.SELECTION_ALL=Rn;xt.SELECTION_INVERT=In;xt.SELECTION_NONE=On;xt.Column=Yi;xt.ColumnGroup=Ji;xt.Summary=Io;export{xt as F,qt as R,$c as a};
