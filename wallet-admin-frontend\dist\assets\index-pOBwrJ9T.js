var Yn=e=>{throw TypeError(e)};var hn=(e,t,n)=>t.has(e)||Yn("Cannot "+n);var C=(e,t,n)=>(hn(e,t,"read from private field"),n?n.call(e):t.get(e)),We=(e,t,n)=>t.has(e)?Yn("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),Oe=(e,t,n,r)=>(hn(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),_e=(e,t,n)=>(hn(e,t,"access private method"),n);import{a$ as ro,bW as Zn,bX as bt,b0 as Sn,bY as Jt,b4 as Cn,bZ as yn,b_ as Jn,b$ as oo,c0 as ao,c1 as io,c2 as kn,b3 as $r,r as o,b5 as lo,au as so,a9 as cn,b as et,aa as co,c as Te,G as Ft,x as re,ao as U,ai as Y,D as Mt,c3 as uo,J as fo,c4 as vo,t as te,_ as it,c5 as mo,v as ho,c6 as go,k as nt,z as po,be as bo,y as At,ae as wn,bz as Or,w as St,q as So,s as Co,o as er,bw as tr,by as yo,ag as zn,ah as wo,aL as Io,bo as Hn,L as kt,g as Ln,m as Lt,h as Mr,C as un,c7 as nr,c8 as rr,f as dn,c9 as Ro,ca as Eo,cb as xo,cc as $o,bK as In,e as xe,$ as Dr,N as Oo,I as Mo,am as Do,an as Po,ar as To,a1 as No,a4 as Bo,a0 as _o,a2 as zo,i as Ho,a3 as Lo,a5 as Fo,bk as jo,bm as Vo,a7 as Ao,cd as Wo,aN as Pr,ce as Uo,cf as Ko,cg as Xo,b8 as Go,aq as Qo}from"./index-CW-Whzws.js";import{a as Tr,p as en,c as qo}from"./index-BVbup1Oj.js";var ke,le,tn,Qe,_t,Wt,xt,$t,nn,Ut,Kt,zt,Ht,Ot,Xt,he,Zt,Rn,En,xn,$n,On,Mn,Dn,Nr,xr,Yo=(xr=class extends ro{constructor(t,n){super();We(this,he);We(this,ke);We(this,le);We(this,tn);We(this,Qe);We(this,_t);We(this,Wt);We(this,xt);We(this,$t);We(this,nn);We(this,Ut);We(this,Kt);We(this,zt);We(this,Ht);We(this,Ot);We(this,Xt,new Set);this.options=n,Oe(this,ke,t),Oe(this,$t,null),Oe(this,xt,Zn()),this.options.experimental_prefetchInRender||C(this,xt).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(n)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(C(this,le).addObserver(this),or(C(this,le),this.options)?_e(this,he,Zt).call(this):this.updateResult(),_e(this,he,$n).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return Pn(C(this,le),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return Pn(C(this,le),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,_e(this,he,On).call(this),_e(this,he,Mn).call(this),C(this,le).removeObserver(this)}setOptions(t){const n=this.options,r=C(this,le);if(this.options=C(this,ke).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof bt(this.options.enabled,C(this,le))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");_e(this,he,Dn).call(this),C(this,le).setOptions(this.options),n._defaulted&&!Sn(this.options,n)&&C(this,ke).getQueryCache().notify({type:"observerOptionsUpdated",query:C(this,le),observer:this});const a=this.hasListeners();a&&ar(C(this,le),r,this.options,n)&&_e(this,he,Zt).call(this),this.updateResult(),a&&(C(this,le)!==r||bt(this.options.enabled,C(this,le))!==bt(n.enabled,C(this,le))||Jt(this.options.staleTime,C(this,le))!==Jt(n.staleTime,C(this,le)))&&_e(this,he,Rn).call(this);const i=_e(this,he,En).call(this);a&&(C(this,le)!==r||bt(this.options.enabled,C(this,le))!==bt(n.enabled,C(this,le))||i!==C(this,Ot))&&_e(this,he,xn).call(this,i)}getOptimisticResult(t){const n=C(this,ke).getQueryCache().build(C(this,ke),t),r=this.createResult(n,t);return Jo(this,r)&&(Oe(this,Qe,r),Oe(this,Wt,this.options),Oe(this,_t,C(this,le).state)),r}getCurrentResult(){return C(this,Qe)}trackResult(t,n){return new Proxy(t,{get:(r,a)=>(this.trackProp(a),n==null||n(a),Reflect.get(r,a))})}trackProp(t){C(this,Xt).add(t)}getCurrentQuery(){return C(this,le)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const n=C(this,ke).defaultQueryOptions(t),r=C(this,ke).getQueryCache().build(C(this,ke),n);return r.fetch().then(()=>this.createResult(r,n))}fetch(t){return _e(this,he,Zt).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),C(this,Qe)))}createResult(t,n){var F;const r=C(this,le),a=this.options,i=C(this,Qe),l=C(this,_t),s=C(this,Wt),f=t!==r?t.state:C(this,tn),{state:v}=t;let d={...v},p=!1,u;if(n._optimisticResults){const j=this.hasListeners(),W=!j&&or(t,n),V=j&&ar(t,r,n,a);(W||V)&&(d={...d,...io(v.data,t.options)}),n._optimisticResults==="isRestoring"&&(d.fetchStatus="idle")}let{error:h,errorUpdatedAt:m,status:g}=d;u=d.data;let b=!1;if(n.placeholderData!==void 0&&u===void 0&&g==="pending"){let j;i!=null&&i.isPlaceholderData&&n.placeholderData===(s==null?void 0:s.placeholderData)?(j=i.data,b=!0):j=typeof n.placeholderData=="function"?n.placeholderData((F=C(this,Kt))==null?void 0:F.state.data,C(this,Kt)):n.placeholderData,j!==void 0&&(g="success",u=kn(i==null?void 0:i.data,j,n),p=!0)}if(n.select&&u!==void 0&&!b)if(i&&u===(l==null?void 0:l.data)&&n.select===C(this,nn))u=C(this,Ut);else try{Oe(this,nn,n.select),u=n.select(u),u=kn(i==null?void 0:i.data,u,n),Oe(this,Ut,u),Oe(this,$t,null)}catch(j){Oe(this,$t,j)}C(this,$t)&&(h=C(this,$t),u=C(this,Ut),m=Date.now(),g="error");const S=d.fetchStatus==="fetching",R=g==="pending",w=g==="error",z=R&&S,H=u!==void 0,_={status:g,fetchStatus:d.fetchStatus,isPending:R,isSuccess:g==="success",isError:w,isInitialLoading:z,isLoading:z,data:u,dataUpdatedAt:d.dataUpdatedAt,error:h,errorUpdatedAt:m,failureCount:d.fetchFailureCount,failureReason:d.fetchFailureReason,errorUpdateCount:d.errorUpdateCount,isFetched:d.dataUpdateCount>0||d.errorUpdateCount>0,isFetchedAfterMount:d.dataUpdateCount>f.dataUpdateCount||d.errorUpdateCount>f.errorUpdateCount,isFetching:S,isRefetching:S&&!R,isLoadingError:w&&!H,isPaused:d.fetchStatus==="paused",isPlaceholderData:p,isRefetchError:w&&H,isStale:Fn(t,n),refetch:this.refetch,promise:C(this,xt)};if(this.options.experimental_prefetchInRender){const j=Q=>{_.status==="error"?Q.reject(_.error):_.data!==void 0&&Q.resolve(_.data)},W=()=>{const Q=Oe(this,xt,_.promise=Zn());j(Q)},V=C(this,xt);switch(V.status){case"pending":t.queryHash===r.queryHash&&j(V);break;case"fulfilled":(_.status==="error"||_.data!==V.value)&&W();break;case"rejected":(_.status!=="error"||_.error!==V.reason)&&W();break}}return _}updateResult(){const t=C(this,Qe),n=this.createResult(C(this,le),this.options);if(Oe(this,_t,C(this,le).state),Oe(this,Wt,this.options),C(this,_t).data!==void 0&&Oe(this,Kt,C(this,le)),Sn(n,t))return;Oe(this,Qe,n);const r=()=>{if(!t)return!0;const{notifyOnChangeProps:a}=this.options,i=typeof a=="function"?a():a;if(i==="all"||!i&&!C(this,Xt).size)return!0;const l=new Set(i??C(this,Xt));return this.options.throwOnError&&l.add("error"),Object.keys(C(this,Qe)).some(s=>{const c=s;return C(this,Qe)[c]!==t[c]&&l.has(c)})};_e(this,he,Nr).call(this,{listeners:r()})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&_e(this,he,$n).call(this)}},ke=new WeakMap,le=new WeakMap,tn=new WeakMap,Qe=new WeakMap,_t=new WeakMap,Wt=new WeakMap,xt=new WeakMap,$t=new WeakMap,nn=new WeakMap,Ut=new WeakMap,Kt=new WeakMap,zt=new WeakMap,Ht=new WeakMap,Ot=new WeakMap,Xt=new WeakMap,he=new WeakSet,Zt=function(t){_e(this,he,Dn).call(this);let n=C(this,le).fetch(this.options,t);return t!=null&&t.throwOnError||(n=n.catch(Cn)),n},Rn=function(){_e(this,he,On).call(this);const t=Jt(this.options.staleTime,C(this,le));if(yn||C(this,Qe).isStale||!Jn(t))return;const r=oo(C(this,Qe).dataUpdatedAt,t)+1;Oe(this,zt,setTimeout(()=>{C(this,Qe).isStale||this.updateResult()},r))},En=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(C(this,le)):this.options.refetchInterval)??!1},xn=function(t){_e(this,he,Mn).call(this),Oe(this,Ot,t),!(yn||bt(this.options.enabled,C(this,le))===!1||!Jn(C(this,Ot))||C(this,Ot)===0)&&Oe(this,Ht,setInterval(()=>{(this.options.refetchIntervalInBackground||ao.isFocused())&&_e(this,he,Zt).call(this)},C(this,Ot)))},$n=function(){_e(this,he,Rn).call(this),_e(this,he,xn).call(this,_e(this,he,En).call(this))},On=function(){C(this,zt)&&(clearTimeout(C(this,zt)),Oe(this,zt,void 0))},Mn=function(){C(this,Ht)&&(clearInterval(C(this,Ht)),Oe(this,Ht,void 0))},Dn=function(){const t=C(this,ke).getQueryCache().build(C(this,ke),this.options);if(t===C(this,le))return;const n=C(this,le);Oe(this,le,t),Oe(this,tn,t.state),this.hasListeners()&&(n==null||n.removeObserver(this),t.addObserver(this))},Nr=function(t){$r.batch(()=>{t.listeners&&this.listeners.forEach(n=>{n(C(this,Qe))}),C(this,ke).getQueryCache().notify({query:C(this,le),type:"observerResultsUpdated"})})},xr);function Zo(e,t){return bt(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function or(e,t){return Zo(e,t)||e.state.data!==void 0&&Pn(e,t,t.refetchOnMount)}function Pn(e,t,n){if(bt(t.enabled,e)!==!1&&Jt(t.staleTime,e)!=="static"){const r=typeof n=="function"?n(e):n;return r==="always"||r!==!1&&Fn(e,t)}return!1}function ar(e,t,n,r){return(e!==t||bt(r.enabled,e)===!1)&&(!n.suspense||e.state.status!=="error")&&Fn(e,n)}function Fn(e,t){return bt(t.enabled,e)!==!1&&e.isStaleByTime(Jt(t.staleTime,e))}function Jo(e,t){return!Sn(e.getCurrentResult(),t)}var Br=o.createContext(!1),ko=()=>o.useContext(Br);Br.Provider;function ea(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var ta=o.createContext(ea()),na=()=>o.useContext(ta),ra=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},oa=e=>{o.useEffect(()=>{e.clearReset()},[e])},aa=({result:e,errorResetBoundary:t,throwOnError:n,query:r,suspense:a})=>e.isError&&!t.isReset()&&!e.isFetching&&r&&(a&&e.data===void 0||lo(n,[e.error,r])),ia=e=>{if(e.suspense){const t=r=>r==="static"?r:Math.max(r??1e3,1e3),n=e.staleTime;e.staleTime=typeof n=="function"?(...r)=>t(n(...r)):t(n),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3))}},la=(e,t)=>e.isLoading&&e.isFetching&&!t,sa=(e,t)=>(e==null?void 0:e.suspense)&&t.isPending,ir=(e,t,n)=>t.fetchOptimistic(e).catch(()=>{n.clearReset()});function ca(e,t,n){var d,p,u,h,m;const r=ko(),a=na(),i=so(),l=i.defaultQueryOptions(e);(p=(d=i.getDefaultOptions().queries)==null?void 0:d._experimental_beforeQuery)==null||p.call(d,l),l._optimisticResults=r?"isRestoring":"optimistic",ia(l),ra(l,a),oa(a);const s=!i.getQueryCache().get(l.queryHash),[c]=o.useState(()=>new t(i,l)),f=c.getOptimisticResult(l),v=!r&&e.subscribed!==!1;if(o.useSyncExternalStore(o.useCallback(g=>{const b=v?c.subscribe($r.batchCalls(g)):Cn;return c.updateResult(),b},[c,v]),()=>c.getCurrentResult(),()=>c.getCurrentResult()),o.useEffect(()=>{c.setOptions(l)},[l,c]),sa(l,f))throw ir(l,c,a);if(aa({result:f,errorResetBoundary:a,throwOnError:l.throwOnError,query:i.getQueryCache().get(l.queryHash),suspense:l.suspense}))throw f.error;if((h=(u=i.getDefaultOptions().queries)==null?void 0:u._experimental_afterQuery)==null||h.call(u,l,f),l.experimental_prefetchInRender&&!yn&&la(f,r)){const g=s?ir(l,c,a):(m=i.getQueryCache().get(l.queryHash))==null?void 0:m.promise;g==null||g.catch(Cn).finally(()=>{c.updateResult()})}return l.notifyOnChangeProps?f:c.trackResult(f)}function vl(e,t){return ca(e,Yo)}function lr(...e){const t={};return e.forEach(n=>{n&&Object.keys(n).forEach(r=>{n[r]!==void 0&&(t[r]=n[r])})}),t}function sr(e){if(e)return{closable:e.closable,closeIcon:e.closeIcon}}function cr(e){const{closable:t,closeIcon:n}=e||{};return et.useMemo(()=>{if(!t&&(t===!1||n===!1||n===null))return!1;if(t===void 0&&n===void 0)return null;let r={closeIcon:typeof n!="boolean"&&n!==null?n:void 0};return t&&typeof t=="object"&&(r=Object.assign(Object.assign({},r),t)),r},[t,n])}const ua={};function da(e,t,n=ua){const r=cr(e),a=cr(t),[i]=cn("global",co.global),l=typeof r!="boolean"?!!(r!=null&&r.disabled):!1,s=et.useMemo(()=>Object.assign({closeIcon:et.createElement(Tr,null)},n),[n]),c=et.useMemo(()=>r===!1?!1:r?lr(s,a,r):a===!1?!1:a?lr(s,a):s.closable?s:!1,[r,a,s]);return et.useMemo(()=>{if(c===!1)return[!1,null,l,{}];const{closeIconRender:f}=s,{closeIcon:v}=c;let d=v;const p=en(c,!0);return d!=null&&(f&&(d=f(v)),d=et.isValidElement(d)?et.cloneElement(d,Object.assign({"aria-label":i.close},p)):et.createElement("span",Object.assign({"aria-label":i.close},p),d)),[!0,d,l,p]},[c,s])}var fn=function(t){var n=t.className,r=t.customizeIcon,a=t.customizeIconProps,i=t.children,l=t.onMouseDown,s=t.onClick,c=typeof r=="function"?r(a):r;return o.createElement("span",{className:n,onMouseDown:function(v){v.preventDefault(),l==null||l(v)},style:{userSelect:"none",WebkitUserSelect:"none"},unselectable:"on",onClick:s,"aria-hidden":!0},c!==void 0?c:o.createElement("span",{className:Te(n.split(/\s+/).map(function(f){return"".concat(f,"-icon")}))},i))},fa=function(t,n,r,a,i){var l=arguments.length>5&&arguments[5]!==void 0?arguments[5]:!1,s=arguments.length>6?arguments[6]:void 0,c=arguments.length>7?arguments[7]:void 0,f=et.useMemo(function(){if(Ft(a)==="object")return a.clearIcon;if(i)return i},[a,i]),v=et.useMemo(function(){return!!(!l&&a&&(r.length||s)&&!(c==="combobox"&&s===""))},[a,l,r.length,s,c]);return{allowClear:v,clearIcon:et.createElement(fn,{className:"".concat(t,"-clear"),onMouseDown:n,customizeIcon:f},"×")}},_r=o.createContext(null);function va(){return o.useContext(_r)}function ma(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:10,t=o.useState(!1),n=re(t,2),r=n[0],a=n[1],i=o.useRef(null),l=function(){window.clearTimeout(i.current)};o.useEffect(function(){return l},[]);var s=function(f,v){l(),i.current=window.setTimeout(function(){a(f),v&&v()},e)};return[r,s,l]}function zr(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:250,t=o.useRef(null),n=o.useRef(null);o.useEffect(function(){return function(){window.clearTimeout(n.current)}},[]);function r(a){(a||t.current===null)&&(t.current=a),window.clearTimeout(n.current),n.current=window.setTimeout(function(){t.current=null},e)}return[function(){return t.current},r]}function ha(e,t,n,r){var a=o.useRef(null);a.current={open:t,triggerOpen:n,customizedTrigger:r},o.useEffect(function(){function i(l){var s;if(!((s=a.current)!==null&&s!==void 0&&s.customizedTrigger)){var c=l.target;c.shadowRoot&&l.composed&&(c=l.composedPath()[0]||c),a.current.open&&e().filter(function(f){return f}).every(function(f){return!f.contains(c)&&f!==c})&&a.current.triggerOpen(!1)}}return window.addEventListener("mousedown",i),function(){return window.removeEventListener("mousedown",i)}},[])}function ga(e){return e&&![U.ESC,U.SHIFT,U.BACKSPACE,U.TAB,U.WIN_KEY,U.ALT,U.META,U.WIN_KEY_RIGHT,U.CTRL,U.SEMICOLON,U.EQUALS,U.CAPS_LOCK,U.CONTEXT_MENU,U.F1,U.F2,U.F3,U.F4,U.F5,U.F6,U.F7,U.F8,U.F9,U.F10,U.F11,U.F12].includes(e)}function pa(e,t,n){var r=Y(Y({},e),t);return Object.keys(t).forEach(function(a){var i=t[a];typeof i=="function"&&(r[a]=function(){for(var l,s=arguments.length,c=new Array(s),f=0;f<s;f++)c[f]=arguments[f];return i.apply(void 0,c),(l=e[a])===null||l===void 0?void 0:l.call.apply(l,[e].concat(c))})}),r}var ba=["prefixCls","id","inputElement","autoFocus","autoComplete","editable","activeDescendantId","value","open","attrs"],Sa=function(t,n){var r=t.prefixCls,a=t.id,i=t.inputElement,l=t.autoFocus,s=t.autoComplete,c=t.editable,f=t.activeDescendantId,v=t.value,d=t.open,p=t.attrs,u=Mt(t,ba),h=i||o.createElement("input",null),m=h,g=m.ref,b=m.props;return uo(!("maxLength"in h.props)),h=o.cloneElement(h,Y(Y(Y({type:"search"},pa(u,b)),{},{id:a,ref:fo(n,g),autoComplete:s||"off",autoFocus:l,className:Te("".concat(r,"-selection-search-input"),b==null?void 0:b.className),role:"combobox","aria-expanded":d||!1,"aria-haspopup":"listbox","aria-owns":"".concat(a,"_list"),"aria-autocomplete":"list","aria-controls":"".concat(a,"_list"),"aria-activedescendant":d?f:void 0},p),{},{value:c?v:"",readOnly:!c,unselectable:c?null:"on",style:Y(Y({},b.style),{},{opacity:c?null:0})})),h},Hr=o.forwardRef(Sa);function Lr(e){return Array.isArray(e)?e:e!==void 0?[e]:[]}var Ca=typeof window<"u"&&window.document&&window.document.documentElement,ya=Ca;function wa(e){return e!=null}function Ia(e){return!e&&e!==0}function ur(e){return["string","number"].includes(Ft(e))}function Fr(e){var t=void 0;return e&&(ur(e.title)?t=e.title.toString():ur(e.label)&&(t=e.label.toString())),t}function Ra(e,t){ya?o.useLayoutEffect(e,t):o.useEffect(e,t)}function Ea(e){var t;return(t=e.key)!==null&&t!==void 0?t:e.value}var dr=function(t){t.preventDefault(),t.stopPropagation()},xa=function(t){var n=t.id,r=t.prefixCls,a=t.values,i=t.open,l=t.searchValue,s=t.autoClearSearchValue,c=t.inputRef,f=t.placeholder,v=t.disabled,d=t.mode,p=t.showSearch,u=t.autoFocus,h=t.autoComplete,m=t.activeDescendantId,g=t.tabIndex,b=t.removeIcon,S=t.maxTagCount,R=t.maxTagTextLength,w=t.maxTagPlaceholder,z=w===void 0?function(q){return"+ ".concat(q.length," ...")}:w,H=t.tagRender,I=t.onToggleOpen,_=t.onRemove,F=t.onInputChange,j=t.onInputPaste,W=t.onInputKeyDown,V=t.onInputMouseDown,Q=t.onInputCompositionStart,de=t.onInputCompositionEnd,ne=t.onInputBlur,J=o.useRef(null),se=o.useState(0),K=re(se,2),oe=K[0],B=K[1],O=o.useState(!1),Z=re(O,2),ae=Z[0],pe=Z[1],fe="".concat(r,"-selection"),Ne=i||d==="multiple"&&s===!1||d==="tags"?l:"",be=d==="tags"||d==="multiple"&&s===!1||p&&(i||ae);Ra(function(){B(J.current.scrollWidth)},[Ne]);var Pe=function(E,A,ce,X,ie){return o.createElement("span",{title:Fr(E),className:Te("".concat(fe,"-item"),te({},"".concat(fe,"-item-disabled"),ce))},o.createElement("span",{className:"".concat(fe,"-item-content")},A),X&&o.createElement(fn,{className:"".concat(fe,"-item-remove"),onMouseDown:dr,onClick:ie,customizeIcon:b},"×"))},we=function(E,A,ce,X,ie,me){var je=function(ze){dr(ze),I(!i)};return o.createElement("span",{onMouseDown:je},H({label:A,value:E,disabled:ce,closable:X,onClose:ie,isMaxTag:!!me}))},Ie=function(E){var A=E.disabled,ce=E.label,X=E.value,ie=!v&&!A,me=ce;if(typeof R=="number"&&(typeof ce=="string"||typeof ce=="number")){var je=String(me);je.length>R&&(me="".concat(je.slice(0,R),"..."))}var Me=function(ge){ge&&ge.stopPropagation(),_(E)};return typeof H=="function"?we(X,me,A,ie,Me):Pe(E,me,A,ie,Me)},P=function(E){if(!a.length)return null;var A=typeof z=="function"?z(E):z;return typeof H=="function"?we(void 0,A,!1,!1,void 0,!0):Pe({title:A},A,!1)},y=o.createElement("div",{className:"".concat(fe,"-search"),style:{width:oe},onFocus:function(){pe(!0)},onBlur:function(){pe(!1)}},o.createElement(Hr,{ref:c,open:i,prefixCls:r,id:n,inputElement:null,disabled:v,autoFocus:u,autoComplete:h,editable:be,activeDescendantId:m,value:Ne,onKeyDown:W,onMouseDown:V,onChange:F,onPaste:j,onCompositionStart:Q,onCompositionEnd:de,onBlur:ne,tabIndex:g,attrs:en(t,!0)}),o.createElement("span",{ref:J,className:"".concat(fe,"-search-mirror"),"aria-hidden":!0},Ne," ")),M=o.createElement(vo,{prefixCls:"".concat(fe,"-overflow"),data:a,renderItem:Ie,renderRest:P,suffix:y,itemKey:Ea,maxCount:S});return o.createElement("span",{className:"".concat(fe,"-wrap")},M,!a.length&&!Ne&&o.createElement("span",{className:"".concat(fe,"-placeholder")},f))},$a=function(t){var n=t.inputElement,r=t.prefixCls,a=t.id,i=t.inputRef,l=t.disabled,s=t.autoFocus,c=t.autoComplete,f=t.activeDescendantId,v=t.mode,d=t.open,p=t.values,u=t.placeholder,h=t.tabIndex,m=t.showSearch,g=t.searchValue,b=t.activeValue,S=t.maxLength,R=t.onInputKeyDown,w=t.onInputMouseDown,z=t.onInputChange,H=t.onInputPaste,I=t.onInputCompositionStart,_=t.onInputCompositionEnd,F=t.onInputBlur,j=t.title,W=o.useState(!1),V=re(W,2),Q=V[0],de=V[1],ne=v==="combobox",J=ne||m,se=p[0],K=g||"";ne&&b&&!Q&&(K=b),o.useEffect(function(){ne&&de(!1)},[ne,b]);var oe=v!=="combobox"&&!d&&!m?!1:!!K,B=j===void 0?Fr(se):j,O=o.useMemo(function(){return se?null:o.createElement("span",{className:"".concat(r,"-selection-placeholder"),style:oe?{visibility:"hidden"}:void 0},u)},[se,oe,u,r]);return o.createElement("span",{className:"".concat(r,"-selection-wrap")},o.createElement("span",{className:"".concat(r,"-selection-search")},o.createElement(Hr,{ref:i,prefixCls:r,id:a,open:d,inputElement:n,disabled:l,autoFocus:s,autoComplete:c,editable:J,activeDescendantId:f,value:K,onKeyDown:R,onMouseDown:w,onChange:function(ae){de(!0),z(ae)},onPaste:H,onCompositionStart:I,onCompositionEnd:_,onBlur:F,tabIndex:h,attrs:en(t,!0),maxLength:ne?S:void 0})),!ne&&se?o.createElement("span",{className:"".concat(r,"-selection-item"),title:B,style:oe?{visibility:"hidden"}:void 0},se.label):null,O)},Oa=function(t,n){var r=o.useRef(null),a=o.useRef(!1),i=t.prefixCls,l=t.open,s=t.mode,c=t.showSearch,f=t.tokenWithEnter,v=t.disabled,d=t.prefix,p=t.autoClearSearchValue,u=t.onSearch,h=t.onSearchSubmit,m=t.onToggleOpen,g=t.onInputKeyDown,b=t.onInputBlur,S=t.domRef;o.useImperativeHandle(n,function(){return{focus:function(B){r.current.focus(B)},blur:function(){r.current.blur()}}});var R=zr(0),w=re(R,2),z=w[0],H=w[1],I=function(B){var O=B.which,Z=r.current instanceof HTMLTextAreaElement;!Z&&l&&(O===U.UP||O===U.DOWN)&&B.preventDefault(),g&&g(B),O===U.ENTER&&s==="tags"&&!a.current&&!l&&(h==null||h(B.target.value)),!(Z&&!l&&~[U.UP,U.DOWN,U.LEFT,U.RIGHT].indexOf(O))&&ga(O)&&m(!0)},_=function(){H(!0)},F=o.useRef(null),j=function(B){u(B,!0,a.current)!==!1&&m(!0)},W=function(){a.current=!0},V=function(B){a.current=!1,s!=="combobox"&&j(B.target.value)},Q=function(B){var O=B.target.value;if(f&&F.current&&/[\r\n]/.test(F.current)){var Z=F.current.replace(/[\r\n]+$/,"").replace(/\r\n/g," ").replace(/[\r\n]/g," ");O=O.replace(Z,F.current)}F.current=null,j(O)},de=function(B){var O=B.clipboardData,Z=O==null?void 0:O.getData("text");F.current=Z||""},ne=function(B){var O=B.target;if(O!==r.current){var Z=document.body.style.msTouchAction!==void 0;Z?setTimeout(function(){r.current.focus()}):r.current.focus()}},J=function(B){var O=z();B.target!==r.current&&!O&&!(s==="combobox"&&v)&&B.preventDefault(),(s!=="combobox"&&(!c||!O)||!l)&&(l&&p!==!1&&u("",!0,!1),m())},se={inputRef:r,onInputKeyDown:I,onInputMouseDown:_,onInputChange:Q,onInputPaste:de,onInputCompositionStart:W,onInputCompositionEnd:V,onInputBlur:b},K=s==="multiple"||s==="tags"?o.createElement(xa,it({},t,se)):o.createElement($a,it({},t,se));return o.createElement("div",{ref:S,className:"".concat(i,"-selector"),onClick:ne,onMouseDown:J},d&&o.createElement("div",{className:"".concat(i,"-prefix")},d),K)},Ma=o.forwardRef(Oa),Da=["prefixCls","disabled","visible","children","popupElement","animation","transitionName","dropdownStyle","dropdownClassName","direction","placement","builtinPlacements","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","getPopupContainer","empty","getTriggerDOMNode","onPopupVisibleChange","onPopupMouseEnter"],Pa=function(t){var n=t===!0?0:1;return{bottomLeft:{points:["tl","bl"],offset:[0,4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"},bottomRight:{points:["tr","br"],offset:[0,4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"},topLeft:{points:["bl","tl"],offset:[0,-4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"},topRight:{points:["br","tr"],offset:[0,-4],overflow:{adjustX:n,adjustY:1},htmlRegion:"scroll"}}},Ta=function(t,n){var r=t.prefixCls;t.disabled;var a=t.visible,i=t.children,l=t.popupElement,s=t.animation,c=t.transitionName,f=t.dropdownStyle,v=t.dropdownClassName,d=t.direction,p=d===void 0?"ltr":d,u=t.placement,h=t.builtinPlacements,m=t.dropdownMatchSelectWidth,g=t.dropdownRender,b=t.dropdownAlign,S=t.getPopupContainer,R=t.empty,w=t.getTriggerDOMNode,z=t.onPopupVisibleChange,H=t.onPopupMouseEnter,I=Mt(t,Da),_="".concat(r,"-dropdown"),F=l;g&&(F=g(l));var j=o.useMemo(function(){return h||Pa(m)},[h,m]),W=s?"".concat(_,"-").concat(s):c,V=typeof m=="number",Q=o.useMemo(function(){return V?null:m===!1?"minWidth":"width"},[m,V]),de=f;V&&(de=Y(Y({},de),{},{width:m}));var ne=o.useRef(null);return o.useImperativeHandle(n,function(){return{getPopupElement:function(){var se;return(se=ne.current)===null||se===void 0?void 0:se.popupElement}}}),o.createElement(mo,it({},I,{showAction:z?["click"]:[],hideAction:z?["click"]:[],popupPlacement:u||(p==="rtl"?"bottomRight":"bottomLeft"),builtinPlacements:j,prefixCls:_,popupTransitionName:W,popup:o.createElement("div",{onMouseEnter:H},F),ref:ne,stretch:Q,popupAlign:b,popupVisible:a,getPopupContainer:S,popupClassName:Te(v,te({},"".concat(_,"-empty"),R)),popupStyle:de,getTriggerDOMNode:w,onPopupVisibleChange:z}),i)},Na=o.forwardRef(Ta);function fr(e,t){var n=e.key,r;return"value"in e&&(r=e.value),n??(r!==void 0?r:"rc-index-key-".concat(t))}function Tn(e){return typeof e<"u"&&!Number.isNaN(e)}function jr(e,t){var n=e||{},r=n.label,a=n.value,i=n.options,l=n.groupLabel,s=r||(t?"children":"label");return{label:s,value:a||"value",options:i||"options",groupLabel:l||s}}function Ba(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=t.fieldNames,r=t.childrenAsData,a=[],i=jr(n,!1),l=i.label,s=i.value,c=i.options,f=i.groupLabel;function v(d,p){Array.isArray(d)&&d.forEach(function(u){if(p||!(c in u)){var h=u[s];a.push({key:fr(u,a.length),groupOption:p,data:u,label:u[l],value:h})}else{var m=u[f];m===void 0&&r&&(m=u.label),a.push({key:fr(u,a.length),group:!0,data:u,label:m}),v(u[c],!0)}})}return v(e,!1),a}function Nn(e){var t=Y({},e);return"props"in t||Object.defineProperty(t,"props",{get:function(){return ho(!1,"Return type is option instead of Option instance. Please read value directly instead of reading from `props`."),t}}),t}var _a=function(t,n,r){if(!n||!n.length)return null;var a=!1,i=function s(c,f){var v=go(f),d=v[0],p=v.slice(1);if(!d)return[c];var u=c.split(d);return a=a||u.length>1,u.reduce(function(h,m){return[].concat(nt(h),nt(s(m,p)))},[]).filter(Boolean)},l=i(t,n);return a?typeof r<"u"?l.slice(0,r):l:null},jn=o.createContext(null);function za(e){var t=e.visible,n=e.values;if(!t)return null;var r=50;return o.createElement("span",{"aria-live":"polite",style:{width:0,height:0,position:"absolute",overflow:"hidden",opacity:0}},"".concat(n.slice(0,r).map(function(a){var i=a.label,l=a.value;return["number","string"].includes(Ft(i))?i:l}).join(", ")),n.length>r?", ...":null)}var Ha=["id","prefixCls","className","showSearch","tagRender","direction","omitDomProps","displayValues","onDisplayValuesChange","emptyOptions","notFoundContent","onClear","mode","disabled","loading","getInputElement","getRawInputElement","open","defaultOpen","onDropdownVisibleChange","activeValue","onActiveValueChange","activeDescendantId","searchValue","autoClearSearchValue","onSearch","onSearchSplit","tokenSeparators","allowClear","prefix","suffixIcon","clearIcon","OptionList","animation","transitionName","dropdownStyle","dropdownClassName","dropdownMatchSelectWidth","dropdownRender","dropdownAlign","placement","builtinPlacements","getPopupContainer","showAction","onFocus","onBlur","onKeyUp","onKeyDown","onMouseDown"],La=["value","onChange","removeIcon","placeholder","autoFocus","maxTagCount","maxTagTextLength","maxTagPlaceholder","choiceTransitionName","onInputKeyDown","onPopupScroll","tabIndex"],Bn=function(t){return t==="tags"||t==="multiple"},Fa=o.forwardRef(function(e,t){var n,r=e.id,a=e.prefixCls,i=e.className,l=e.showSearch,s=e.tagRender,c=e.direction,f=e.omitDomProps,v=e.displayValues,d=e.onDisplayValuesChange,p=e.emptyOptions,u=e.notFoundContent,h=u===void 0?"Not Found":u,m=e.onClear,g=e.mode,b=e.disabled,S=e.loading,R=e.getInputElement,w=e.getRawInputElement,z=e.open,H=e.defaultOpen,I=e.onDropdownVisibleChange,_=e.activeValue,F=e.onActiveValueChange,j=e.activeDescendantId,W=e.searchValue,V=e.autoClearSearchValue,Q=e.onSearch,de=e.onSearchSplit,ne=e.tokenSeparators,J=e.allowClear,se=e.prefix,K=e.suffixIcon,oe=e.clearIcon,B=e.OptionList,O=e.animation,Z=e.transitionName,ae=e.dropdownStyle,pe=e.dropdownClassName,fe=e.dropdownMatchSelectWidth,Ne=e.dropdownRender,be=e.dropdownAlign,Pe=e.placement,we=e.builtinPlacements,Ie=e.getPopupContainer,P=e.showAction,y=P===void 0?[]:P,M=e.onFocus,q=e.onBlur,E=e.onKeyUp,A=e.onKeyDown,ce=e.onMouseDown,X=Mt(e,Ha),ie=Bn(g),me=(l!==void 0?l:ie)||g==="combobox",je=Y({},X);La.forEach(function(ue){delete je[ue]}),f==null||f.forEach(function(ue){delete je[ue]});var Me=o.useState(!1),ze=re(Me,2),ge=ze[0],Xe=ze[1];o.useEffect(function(){Xe(po())},[]);var tt=o.useRef(null),Se=o.useRef(null),De=o.useRef(null),Ce=o.useRef(null),Re=o.useRef(null),He=o.useRef(!1),ft=ma(),Ae=re(ft,3),Ge=Ae[0],$e=Ae[1],Dt=Ae[2];o.useImperativeHandle(t,function(){var ue,G;return{focus:(ue=Ce.current)===null||ue===void 0?void 0:ue.focus,blur:(G=Ce.current)===null||G===void 0?void 0:G.blur,scrollTo:function(Je){var Fe;return(Fe=Re.current)===null||Fe===void 0?void 0:Fe.scrollTo(Je)},nativeElement:tt.current||Se.current}});var Ue=o.useMemo(function(){var ue;if(g!=="combobox")return W;var G=(ue=v[0])===null||ue===void 0?void 0:ue.value;return typeof G=="string"||typeof G=="number"?String(G):""},[W,g,v]),Ct=g==="combobox"&&typeof R=="function"&&R()||null,Ve=typeof w=="function"&&w(),Pt=bo(Se,Ve==null||(n=Ve.props)===null||n===void 0?void 0:n.ref),lt=o.useState(!1),vt=re(lt,2),yt=vt[0],Rt=vt[1];At(function(){Rt(!0)},[]);var st=wn(!1,{defaultValue:H,value:z}),qe=re(st,2),mt=qe[0],ht=qe[1],Ee=yt?mt:!1,gt=!h&&p;(b||gt&&Ee&&g==="combobox")&&(Ee=!1);var rt=gt?!1:Ee,x=o.useCallback(function(ue){var G=ue!==void 0?ue:!Ee;b||(ht(G),Ee!==G&&(I==null||I(G)))},[b,Ee,ht,I]),L=o.useMemo(function(){return(ne||[]).some(function(ue){return[`
`,`\r
`].includes(ue)})},[ne]),N=o.useContext(jn)||{},D=N.maxCount,ee=N.rawValues,ye=function(G,Ze,Je){if(!(ie&&Tn(D)&&(ee==null?void 0:ee.size)>=D)){var Fe=!0,Ke=G;F==null||F(null);var It=_a(G,ne,Tn(D)?D-ee.size:void 0),pt=Je?null:It;return g!=="combobox"&&pt&&(Ke="",de==null||de(pt),x(!1),Fe=!1),Q&&Ue!==Ke&&Q(Ke,{source:Ze?"typing":"effect"}),Fe}},ot=function(G){!G||!G.trim()||Q(G,{source:"submit"})};o.useEffect(function(){!Ee&&!ie&&g!=="combobox"&&ye("",!1,!1)},[Ee]),o.useEffect(function(){mt&&b&&ht(!1),b&&!He.current&&$e(!1)},[b]);var Ye=zr(),ct=re(Ye,2),Be=ct[0],ut=ct[1],Et=o.useRef(!1),Gt=function(G){var Ze=Be(),Je=G.key,Fe=Je==="Enter";if(Fe&&(g!=="combobox"&&G.preventDefault(),Ee||x(!0)),ut(!!Ue),Je==="Backspace"&&!Ze&&ie&&!Ue&&v.length){for(var Ke=nt(v),It=null,pt=Ke.length-1;pt>=0;pt-=1){var Tt=Ke[pt];if(!Tt.disabled){Ke.splice(pt,1),It=Tt;break}}It&&d(Ke,{type:"remove",values:[It]})}for(var Vt=arguments.length,Nt=new Array(Vt>1?Vt-1:0),an=1;an<Vt;an++)Nt[an-1]=arguments[an];if(Ee&&(!Fe||!Et.current)){var ln;Fe&&(Et.current=!0),(ln=Re.current)===null||ln===void 0||ln.onKeyDown.apply(ln,[G].concat(Nt))}A==null||A.apply(void 0,[G].concat(Nt))},on=function(G){for(var Ze=arguments.length,Je=new Array(Ze>1?Ze-1:0),Fe=1;Fe<Ze;Fe++)Je[Fe-1]=arguments[Fe];if(Ee){var Ke;(Ke=Re.current)===null||Ke===void 0||Ke.onKeyUp.apply(Ke,[G].concat(Je))}G.key==="Enter"&&(Et.current=!1),E==null||E.apply(void 0,[G].concat(Je))},dt=function(G){var Ze=v.filter(function(Je){return Je!==G});d(Ze,{type:"remove",values:[G]})},at=function(){Et.current=!1},T=o.useRef(!1),$=function(){$e(!0),b||(M&&!T.current&&M.apply(void 0,arguments),y.includes("focus")&&x(!0)),T.current=!0},k=function(){He.current=!0,$e(!1,function(){T.current=!1,He.current=!1,x(!1)}),!b&&(Ue&&(g==="tags"?Q(Ue,{source:"submit"}):g==="multiple"&&Q("",{source:"blur"})),q&&q.apply(void 0,arguments))},ve=[];o.useEffect(function(){return function(){ve.forEach(function(ue){return clearTimeout(ue)}),ve.splice(0,ve.length)}},[]);var Le=function(G){var Ze,Je=G.target,Fe=(Ze=De.current)===null||Ze===void 0?void 0:Ze.getPopupElement();if(Fe&&Fe.contains(Je)){var Ke=setTimeout(function(){var Vt=ve.indexOf(Ke);if(Vt!==-1&&ve.splice(Vt,1),Dt(),!ge&&!Fe.contains(document.activeElement)){var Nt;(Nt=Ce.current)===null||Nt===void 0||Nt.focus()}});ve.push(Ke)}for(var It=arguments.length,pt=new Array(It>1?It-1:0),Tt=1;Tt<It;Tt++)pt[Tt-1]=arguments[Tt];ce==null||ce.apply(void 0,[G].concat(pt))},jt=o.useState({}),wt=re(jt,2),Qt=wt[1];function vn(){Qt({})}var qt;Ve&&(qt=function(G){x(G)}),ha(function(){var ue;return[tt.current,(ue=De.current)===null||ue===void 0?void 0:ue.getPopupElement()]},rt,x,!!Ve);var Yt=o.useMemo(function(){return Y(Y({},e),{},{notFoundContent:h,open:Ee,triggerOpen:rt,id:r,showSearch:me,multiple:ie,toggleOpen:x})},[e,h,rt,Ee,r,me,ie,x]),Xn=!!K||S,Gn;Xn&&(Gn=o.createElement(fn,{className:Te("".concat(a,"-arrow"),te({},"".concat(a,"-arrow-loading"),S)),customizeIcon:K,customizeIconProps:{loading:S,searchValue:Ue,open:Ee,focused:Ge,showSearch:me}}));var Jr=function(){var G;m==null||m(),(G=Ce.current)===null||G===void 0||G.focus(),d([],{type:"clear",values:v}),ye("",!1,!1)},Qn=fa(a,Jr,v,J,oe,b,Ue,g),kr=Qn.allowClear,eo=Qn.clearIcon,to=o.createElement(B,{ref:Re}),no=Te(a,i,te(te(te(te(te(te(te(te(te(te({},"".concat(a,"-focused"),Ge),"".concat(a,"-multiple"),ie),"".concat(a,"-single"),!ie),"".concat(a,"-allow-clear"),J),"".concat(a,"-show-arrow"),Xn),"".concat(a,"-disabled"),b),"".concat(a,"-loading"),S),"".concat(a,"-open"),Ee),"".concat(a,"-customize-input"),Ct),"".concat(a,"-show-search"),me)),qn=o.createElement(Na,{ref:De,disabled:b,prefixCls:a,visible:rt,popupElement:to,animation:O,transitionName:Z,dropdownStyle:ae,dropdownClassName:pe,direction:c,dropdownMatchSelectWidth:fe,dropdownRender:Ne,dropdownAlign:be,placement:Pe,builtinPlacements:we,getPopupContainer:Ie,empty:p,getTriggerDOMNode:function(G){return Se.current||G},onPopupVisibleChange:qt,onPopupMouseEnter:vn},Ve?o.cloneElement(Ve,{ref:Pt}):o.createElement(Ma,it({},e,{domRef:Se,prefixCls:a,inputElement:Ct,ref:Ce,id:r,prefix:se,showSearch:me,autoClearSearchValue:V,mode:g,activeDescendantId:j,tagRender:s,values:v,open:Ee,onToggleOpen:x,activeValue:_,searchValue:Ue,onSearch:ye,onSearchSubmit:ot,onRemove:dt,tokenWithEnter:L,onInputBlur:at}))),mn;return Ve?mn=qn:mn=o.createElement("div",it({className:no},je,{ref:tt,onMouseDown:Le,onKeyDown:Gt,onKeyUp:on,onFocus:$,onBlur:k}),o.createElement(za,{visible:Ge&&!Ee,values:v}),qn,Gn,kr&&eo),o.createElement(_r.Provider,{value:Yt},mn)}),Vn=function(){return null};Vn.isSelectOptGroup=!0;var An=function(){return null};An.isSelectOption=!0;var Vr=o.forwardRef(function(e,t){var n=e.height,r=e.offsetY,a=e.offsetX,i=e.children,l=e.prefixCls,s=e.onInnerResize,c=e.innerProps,f=e.rtl,v=e.extra,d={},p={display:"flex",flexDirection:"column"};return r!==void 0&&(d={height:n,position:"relative",overflow:"hidden"},p=Y(Y({},p),{},te(te(te(te(te({transform:"translateY(".concat(r,"px)")},f?"marginRight":"marginLeft",-a),"position","absolute"),"left",0),"right",0),"top",0))),o.createElement("div",{style:d},o.createElement(Or,{onResize:function(h){var m=h.offsetHeight;m&&s&&s()}},o.createElement("div",it({style:p,className:Te(te({},"".concat(l,"-holder-inner"),l)),ref:t},c),i,v)))});Vr.displayName="Filler";function ja(e){var t=e.children,n=e.setRef,r=o.useCallback(function(a){n(a)},[]);return o.cloneElement(t,{ref:r})}function Va(e,t,n,r,a,i,l,s){var c=s.getKey;return e.slice(t,n+1).map(function(f,v){var d=t+v,p=l(f,d,{style:{width:r},offsetX:a}),u=c(f);return o.createElement(ja,{key:u,setRef:function(m){return i(f,m)}},p)})}function Aa(e,t,n){var r=e.length,a=t.length,i,l;if(r===0&&a===0)return null;r<a?(i=e,l=t):(i=t,l=e);var s={__EMPTY_ITEM__:!0};function c(h){return h!==void 0?n(h):s}for(var f=null,v=Math.abs(r-a)!==1,d=0;d<l.length;d+=1){var p=c(i[d]),u=c(l[d]);if(p!==u){f=d,v=v||p!==c(l[d+1]);break}}return f===null?null:{index:f,multiple:v}}function Wa(e,t,n){var r=o.useState(e),a=re(r,2),i=a[0],l=a[1],s=o.useState(null),c=re(s,2),f=c[0],v=c[1];return o.useEffect(function(){var d=Aa(i||[],e||[],t);(d==null?void 0:d.index)!==void 0&&v(e[d.index]),l(e)},[e]),[f]}var vr=(typeof navigator>"u"?"undefined":Ft(navigator))==="object"&&/Firefox/i.test(navigator.userAgent);const Ar=function(e,t,n,r){var a=o.useRef(!1),i=o.useRef(null);function l(){clearTimeout(i.current),a.current=!0,i.current=setTimeout(function(){a.current=!1},50)}var s=o.useRef({top:e,bottom:t,left:n,right:r});return s.current.top=e,s.current.bottom=t,s.current.left=n,s.current.right=r,function(c,f){var v=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,d=c?f<0&&s.current.left||f>0&&s.current.right:f<0&&s.current.top||f>0&&s.current.bottom;return v&&d?(clearTimeout(i.current),a.current=!1):(!d||a.current)&&l(),!a.current&&d}};function Ua(e,t,n,r,a,i,l){var s=o.useRef(0),c=o.useRef(null),f=o.useRef(null),v=o.useRef(!1),d=Ar(t,n,r,a);function p(S,R){if(St.cancel(c.current),!d(!1,R)){var w=S;if(!w._virtualHandled)w._virtualHandled=!0;else return;s.current+=R,f.current=R,vr||w.preventDefault(),c.current=St(function(){var z=v.current?10:1;l(s.current*z,!1),s.current=0})}}function u(S,R){l(R,!0),vr||S.preventDefault()}var h=o.useRef(null),m=o.useRef(null);function g(S){if(e){St.cancel(m.current),m.current=St(function(){h.current=null},2);var R=S.deltaX,w=S.deltaY,z=S.shiftKey,H=R,I=w;(h.current==="sx"||!h.current&&z&&w&&!R)&&(H=w,I=0,h.current="sx");var _=Math.abs(H),F=Math.abs(I);h.current===null&&(h.current=i&&_>F?"x":"y"),h.current==="y"?p(S,I):u(S,H)}}function b(S){e&&(v.current=S.detail===f.current)}return[g,b]}function Ka(e,t,n,r){var a=o.useMemo(function(){return[new Map,[]]},[e,n.id,r]),i=re(a,2),l=i[0],s=i[1],c=function(v){var d=arguments.length>1&&arguments[1]!==void 0?arguments[1]:v,p=l.get(v),u=l.get(d);if(p===void 0||u===void 0)for(var h=e.length,m=s.length;m<h;m+=1){var g,b=e[m],S=t(b);l.set(S,m);var R=(g=n.get(S))!==null&&g!==void 0?g:r;if(s[m]=(s[m-1]||0)+R,S===v&&(p=m),S===d&&(u=m),p!==void 0&&u!==void 0)break}return{top:s[p-1]||0,bottom:s[u]}};return c}var Xa=function(){function e(){Co(this,e),te(this,"maps",void 0),te(this,"id",0),te(this,"diffRecords",new Map),this.maps=Object.create(null)}return So(e,[{key:"set",value:function(n,r){this.diffRecords.set(n,this.maps[n]),this.maps[n]=r,this.id+=1}},{key:"get",value:function(n){return this.maps[n]}},{key:"resetRecord",value:function(){this.diffRecords.clear()}},{key:"getRecord",value:function(){return this.diffRecords}}]),e}();function mr(e){var t=parseFloat(e);return isNaN(t)?0:t}function Ga(e,t,n){var r=o.useState(0),a=re(r,2),i=a[0],l=a[1],s=o.useRef(new Map),c=o.useRef(new Xa),f=o.useRef(0);function v(){f.current+=1}function d(){var u=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!1;v();var h=function(){var b=!1;s.current.forEach(function(S,R){if(S&&S.offsetParent){var w=S.offsetHeight,z=getComputedStyle(S),H=z.marginTop,I=z.marginBottom,_=mr(H),F=mr(I),j=w+_+F;c.current.get(R)!==j&&(c.current.set(R,j),b=!0)}}),b&&l(function(S){return S+1})};if(u)h();else{f.current+=1;var m=f.current;Promise.resolve().then(function(){m===f.current&&h()})}}function p(u,h){var m=e(u);s.current.get(m),h?(s.current.set(m,h),d()):s.current.delete(m)}return o.useEffect(function(){return v},[]),[p,d,c.current,i]}var hr=14/15;function Qa(e,t,n){var r=o.useRef(!1),a=o.useRef(0),i=o.useRef(0),l=o.useRef(null),s=o.useRef(null),c,f=function(u){if(r.current){var h=Math.ceil(u.touches[0].pageX),m=Math.ceil(u.touches[0].pageY),g=a.current-h,b=i.current-m,S=Math.abs(g)>Math.abs(b);S?a.current=h:i.current=m;var R=n(S,S?g:b,!1,u);R&&u.preventDefault(),clearInterval(s.current),R&&(s.current=setInterval(function(){S?g*=hr:b*=hr;var w=Math.floor(S?g:b);(!n(S,w,!0)||Math.abs(w)<=.1)&&clearInterval(s.current)},16))}},v=function(){r.current=!1,c()},d=function(u){c(),u.touches.length===1&&!r.current&&(r.current=!0,a.current=Math.ceil(u.touches[0].pageX),i.current=Math.ceil(u.touches[0].pageY),l.current=u.target,l.current.addEventListener("touchmove",f,{passive:!1}),l.current.addEventListener("touchend",v,{passive:!0}))};c=function(){l.current&&(l.current.removeEventListener("touchmove",f),l.current.removeEventListener("touchend",v))},At(function(){return e&&t.current.addEventListener("touchstart",d,{passive:!0}),function(){var p;(p=t.current)===null||p===void 0||p.removeEventListener("touchstart",d),c(),clearInterval(s.current)}},[e])}function gr(e){return Math.floor(Math.pow(e,.5))}function _n(e,t){var n="touches"in e?e.touches[0]:e;return n[t?"pageX":"pageY"]-window[t?"scrollX":"scrollY"]}function qa(e,t,n){o.useEffect(function(){var r=t.current;if(e&&r){var a=!1,i,l,s=function(){St.cancel(i)},c=function p(){s(),i=St(function(){n(l),p()})},f=function(u){if(!(u.target.draggable||u.button!==0)){var h=u;h._virtualHandled||(h._virtualHandled=!0,a=!0)}},v=function(){a=!1,s()},d=function(u){if(a){var h=_n(u,!1),m=r.getBoundingClientRect(),g=m.top,b=m.bottom;if(h<=g){var S=g-h;l=-gr(S),c()}else if(h>=b){var R=h-b;l=gr(R),c()}else s()}};return r.addEventListener("mousedown",f),r.ownerDocument.addEventListener("mouseup",v),r.ownerDocument.addEventListener("mousemove",d),function(){r.removeEventListener("mousedown",f),r.ownerDocument.removeEventListener("mouseup",v),r.ownerDocument.removeEventListener("mousemove",d),s()}}},[e])}var Ya=10;function Za(e,t,n,r,a,i,l,s){var c=o.useRef(),f=o.useState(null),v=re(f,2),d=v[0],p=v[1];return At(function(){if(d&&d.times<Ya){if(!e.current){p(function(oe){return Y({},oe)});return}i();var u=d.targetAlign,h=d.originAlign,m=d.index,g=d.offset,b=e.current.clientHeight,S=!1,R=u,w=null;if(b){for(var z=u||h,H=0,I=0,_=0,F=Math.min(t.length-1,m),j=0;j<=F;j+=1){var W=a(t[j]);I=H;var V=n.get(W);_=I+(V===void 0?r:V),H=_}for(var Q=z==="top"?g:b-g,de=F;de>=0;de-=1){var ne=a(t[de]),J=n.get(ne);if(J===void 0){S=!0;break}if(Q-=J,Q<=0)break}switch(z){case"top":w=I-g;break;case"bottom":w=_-b+g;break;default:{var se=e.current.scrollTop,K=se+b;I<se?R="top":_>K&&(R="bottom")}}w!==null&&l(w),w!==d.lastTop&&(S=!0)}S&&p(Y(Y({},d),{},{times:d.times+1,targetAlign:R,lastTop:w}))}},[d,e.current]),function(u){if(u==null){s();return}if(St.cancel(c.current),typeof u=="number")l(u);else if(u&&Ft(u)==="object"){var h,m=u.align;"index"in u?h=u.index:h=t.findIndex(function(S){return a(S)===u.key});var g=u.offset,b=g===void 0?0:g;p({times:0,index:h,offset:b,originAlign:m})}}}var pr=o.forwardRef(function(e,t){var n=e.prefixCls,r=e.rtl,a=e.scrollOffset,i=e.scrollRange,l=e.onStartMove,s=e.onStopMove,c=e.onScroll,f=e.horizontal,v=e.spinSize,d=e.containerSize,p=e.style,u=e.thumbStyle,h=e.showScrollBar,m=o.useState(!1),g=re(m,2),b=g[0],S=g[1],R=o.useState(null),w=re(R,2),z=w[0],H=w[1],I=o.useState(null),_=re(I,2),F=_[0],j=_[1],W=!r,V=o.useRef(),Q=o.useRef(),de=o.useState(h),ne=re(de,2),J=ne[0],se=ne[1],K=o.useRef(),oe=function(){h===!0||h===!1||(clearTimeout(K.current),se(!0),K.current=setTimeout(function(){se(!1)},3e3))},B=i-d||0,O=d-v||0,Z=o.useMemo(function(){if(a===0||B===0)return 0;var P=a/B;return P*O},[a,B,O]),ae=function(y){y.stopPropagation(),y.preventDefault()},pe=o.useRef({top:Z,dragging:b,pageY:z,startTop:F});pe.current={top:Z,dragging:b,pageY:z,startTop:F};var fe=function(y){S(!0),H(_n(y,f)),j(pe.current.top),l(),y.stopPropagation(),y.preventDefault()};o.useEffect(function(){var P=function(E){E.preventDefault()},y=V.current,M=Q.current;return y.addEventListener("touchstart",P,{passive:!1}),M.addEventListener("touchstart",fe,{passive:!1}),function(){y.removeEventListener("touchstart",P),M.removeEventListener("touchstart",fe)}},[]);var Ne=o.useRef();Ne.current=B;var be=o.useRef();be.current=O,o.useEffect(function(){if(b){var P,y=function(E){var A=pe.current,ce=A.dragging,X=A.pageY,ie=A.startTop;St.cancel(P);var me=V.current.getBoundingClientRect(),je=d/(f?me.width:me.height);if(ce){var Me=(_n(E,f)-X)*je,ze=ie;!W&&f?ze-=Me:ze+=Me;var ge=Ne.current,Xe=be.current,tt=Xe?ze/Xe:0,Se=Math.ceil(tt*ge);Se=Math.max(Se,0),Se=Math.min(Se,ge),P=St(function(){c(Se,f)})}},M=function(){S(!1),s()};return window.addEventListener("mousemove",y,{passive:!0}),window.addEventListener("touchmove",y,{passive:!0}),window.addEventListener("mouseup",M,{passive:!0}),window.addEventListener("touchend",M,{passive:!0}),function(){window.removeEventListener("mousemove",y),window.removeEventListener("touchmove",y),window.removeEventListener("mouseup",M),window.removeEventListener("touchend",M),St.cancel(P)}}},[b]),o.useEffect(function(){return oe(),function(){clearTimeout(K.current)}},[a]),o.useImperativeHandle(t,function(){return{delayHidden:oe}});var Pe="".concat(n,"-scrollbar"),we={position:"absolute",visibility:J?null:"hidden"},Ie={position:"absolute",borderRadius:99,background:"var(--rc-virtual-list-scrollbar-bg, rgba(0, 0, 0, 0.5))",cursor:"pointer",userSelect:"none"};return f?(Object.assign(we,{height:8,left:0,right:0,bottom:0}),Object.assign(Ie,te({height:"100%",width:v},W?"left":"right",Z))):(Object.assign(we,te({width:8,top:0,bottom:0},W?"right":"left",0)),Object.assign(Ie,{width:"100%",height:v,top:Z})),o.createElement("div",{ref:V,className:Te(Pe,te(te(te({},"".concat(Pe,"-horizontal"),f),"".concat(Pe,"-vertical"),!f),"".concat(Pe,"-visible"),J)),style:Y(Y({},we),p),onMouseDown:ae,onMouseMove:oe},o.createElement("div",{ref:Q,className:Te("".concat(Pe,"-thumb"),te({},"".concat(Pe,"-thumb-moving"),b)),style:Y(Y({},Ie),u),onMouseDown:fe}))}),Ja=20;function br(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=e/t*e;return isNaN(n)&&(n=0),n=Math.max(n,Ja),Math.floor(n)}var ka=["prefixCls","className","height","itemHeight","fullHeight","style","data","children","itemKey","virtual","direction","scrollWidth","component","onScroll","onVirtualScroll","onVisibleChange","innerProps","extraRender","styles","showScrollBar"],ei=[],ti={overflowY:"auto",overflowAnchor:"none"};function ni(e,t){var n=e.prefixCls,r=n===void 0?"rc-virtual-list":n,a=e.className,i=e.height,l=e.itemHeight,s=e.fullHeight,c=s===void 0?!0:s,f=e.style,v=e.data,d=e.children,p=e.itemKey,u=e.virtual,h=e.direction,m=e.scrollWidth,g=e.component,b=g===void 0?"div":g,S=e.onScroll,R=e.onVirtualScroll,w=e.onVisibleChange,z=e.innerProps,H=e.extraRender,I=e.styles,_=e.showScrollBar,F=_===void 0?"optional":_,j=Mt(e,ka),W=o.useCallback(function(T){return typeof p=="function"?p(T):T==null?void 0:T[p]},[p]),V=Ga(W),Q=re(V,4),de=Q[0],ne=Q[1],J=Q[2],se=Q[3],K=!!(u!==!1&&i&&l),oe=o.useMemo(function(){return Object.values(J.maps).reduce(function(T,$){return T+$},0)},[J.id,J.maps]),B=K&&v&&(Math.max(l*v.length,oe)>i||!!m),O=h==="rtl",Z=Te(r,te({},"".concat(r,"-rtl"),O),a),ae=v||ei,pe=o.useRef(),fe=o.useRef(),Ne=o.useRef(),be=o.useState(0),Pe=re(be,2),we=Pe[0],Ie=Pe[1],P=o.useState(0),y=re(P,2),M=y[0],q=y[1],E=o.useState(!1),A=re(E,2),ce=A[0],X=A[1],ie=function(){X(!0)},me=function(){X(!1)},je={getKey:W};function Me(T){Ie(function($){var k;typeof T=="function"?k=T($):k=T;var ve=Rt(k);return pe.current.scrollTop=ve,ve})}var ze=o.useRef({start:0,end:ae.length}),ge=o.useRef(),Xe=Wa(ae,W),tt=re(Xe,1),Se=tt[0];ge.current=Se;var De=o.useMemo(function(){if(!K)return{scrollHeight:void 0,start:0,end:ae.length-1,offset:void 0};if(!B){var T;return{scrollHeight:((T=fe.current)===null||T===void 0?void 0:T.offsetHeight)||0,start:0,end:ae.length-1,offset:void 0}}for(var $=0,k,ve,Le,jt=ae.length,wt=0;wt<jt;wt+=1){var Qt=ae[wt],vn=W(Qt),qt=J.get(vn),Yt=$+(qt===void 0?l:qt);Yt>=we&&k===void 0&&(k=wt,ve=$),Yt>we+i&&Le===void 0&&(Le=wt),$=Yt}return k===void 0&&(k=0,ve=0,Le=Math.ceil(i/l)),Le===void 0&&(Le=ae.length-1),Le=Math.min(Le+1,ae.length-1),{scrollHeight:$,start:k,end:Le,offset:ve}},[B,K,we,ae,se,i]),Ce=De.scrollHeight,Re=De.start,He=De.end,ft=De.offset;ze.current.start=Re,ze.current.end=He,o.useLayoutEffect(function(){var T=J.getRecord();if(T.size===1){var $=Array.from(T.keys())[0],k=T.get($),ve=ae[Re];if(ve&&k===void 0){var Le=W(ve);if(Le===$){var jt=J.get($),wt=jt-l;Me(function(Qt){return Qt+wt})}}}J.resetRecord()},[Ce]);var Ae=o.useState({width:0,height:i}),Ge=re(Ae,2),$e=Ge[0],Dt=Ge[1],Ue=function($){Dt({width:$.offsetWidth,height:$.offsetHeight})},Ct=o.useRef(),Ve=o.useRef(),Pt=o.useMemo(function(){return br($e.width,m)},[$e.width,m]),lt=o.useMemo(function(){return br($e.height,Ce)},[$e.height,Ce]),vt=Ce-i,yt=o.useRef(vt);yt.current=vt;function Rt(T){var $=T;return Number.isNaN(yt.current)||($=Math.min($,yt.current)),$=Math.max($,0),$}var st=we<=0,qe=we>=vt,mt=M<=0,ht=M>=m,Ee=Ar(st,qe,mt,ht),gt=function(){return{x:O?-M:M,y:we}},rt=o.useRef(gt()),x=er(function(T){if(R){var $=Y(Y({},gt()),T);(rt.current.x!==$.x||rt.current.y!==$.y)&&(R($),rt.current=$)}});function L(T,$){var k=T;$?(tr.flushSync(function(){q(k)}),x()):Me(k)}function N(T){var $=T.currentTarget.scrollTop;$!==we&&Me($),S==null||S(T),x()}var D=function($){var k=$,ve=m?m-$e.width:0;return k=Math.max(k,0),k=Math.min(k,ve),k},ee=er(function(T,$){$?(tr.flushSync(function(){q(function(k){var ve=k+(O?-T:T);return D(ve)})}),x()):Me(function(k){var ve=k+T;return ve})}),ye=Ua(K,st,qe,mt,ht,!!m,ee),ot=re(ye,2),Ye=ot[0],ct=ot[1];Qa(K,pe,function(T,$,k,ve){var Le=ve;return Ee(T,$,k)?!1:!Le||!Le._virtualHandled?(Le&&(Le._virtualHandled=!0),Ye({preventDefault:function(){},deltaX:T?$:0,deltaY:T?0:$}),!0):!1}),qa(B,pe,function(T){Me(function($){return $+T})}),At(function(){function T(k){var ve=st&&k.detail<0,Le=qe&&k.detail>0;K&&!ve&&!Le&&k.preventDefault()}var $=pe.current;return $.addEventListener("wheel",Ye,{passive:!1}),$.addEventListener("DOMMouseScroll",ct,{passive:!0}),$.addEventListener("MozMousePixelScroll",T,{passive:!1}),function(){$.removeEventListener("wheel",Ye),$.removeEventListener("DOMMouseScroll",ct),$.removeEventListener("MozMousePixelScroll",T)}},[K,st,qe]),At(function(){if(m){var T=D(M);q(T),x({x:T})}},[$e.width,m]);var Be=function(){var $,k;($=Ct.current)===null||$===void 0||$.delayHidden(),(k=Ve.current)===null||k===void 0||k.delayHidden()},ut=Za(pe,ae,J,l,W,function(){return ne(!0)},Me,Be);o.useImperativeHandle(t,function(){return{nativeElement:Ne.current,getScrollInfo:gt,scrollTo:function($){function k(ve){return ve&&Ft(ve)==="object"&&("left"in ve||"top"in ve)}k($)?($.left!==void 0&&q(D($.left)),ut($.top)):ut($)}}}),At(function(){if(w){var T=ae.slice(Re,He+1);w(T,ae)}},[Re,He,ae]);var Et=Ka(ae,W,J,l),Gt=H==null?void 0:H({start:Re,end:He,virtual:B,offsetX:M,offsetY:ft,rtl:O,getSize:Et}),on=Va(ae,Re,He,m,M,de,d,je),dt=null;i&&(dt=Y(te({},c?"height":"maxHeight",i),ti),K&&(dt.overflowY="hidden",m&&(dt.overflowX="hidden"),ce&&(dt.pointerEvents="none")));var at={};return O&&(at.dir="rtl"),o.createElement("div",it({ref:Ne,style:Y(Y({},f),{},{position:"relative"}),className:Z},at,j),o.createElement(Or,{onResize:Ue},o.createElement(b,{className:"".concat(r,"-holder"),style:dt,ref:pe,onScroll:N,onMouseEnter:Be},o.createElement(Vr,{prefixCls:r,height:Ce,offsetX:M,offsetY:ft,scrollWidth:m,onInnerResize:ne,ref:fe,innerProps:z,rtl:O,extra:Gt},on))),B&&Ce>i&&o.createElement(pr,{ref:Ct,prefixCls:r,scrollOffset:we,scrollRange:Ce,rtl:O,onScroll:L,onStartMove:ie,onStopMove:me,spinSize:lt,containerSize:$e.height,style:I==null?void 0:I.verticalScrollBar,thumbStyle:I==null?void 0:I.verticalScrollBarThumb,showScrollBar:F}),B&&m>$e.width&&o.createElement(pr,{ref:Ve,prefixCls:r,scrollOffset:M,scrollRange:m,rtl:O,onScroll:L,onStartMove:ie,onStopMove:me,spinSize:Pt,containerSize:$e.width,horizontal:!0,style:I==null?void 0:I.horizontalScrollBar,thumbStyle:I==null?void 0:I.horizontalScrollBarThumb,showScrollBar:F}))}var Wr=o.forwardRef(ni);Wr.displayName="List";function ri(){return/(mac\sos|macintosh)/i.test(navigator.appVersion)}var oi=["disabled","title","children","style","className"];function Sr(e){return typeof e=="string"||typeof e=="number"}var ai=function(t,n){var r=va(),a=r.prefixCls,i=r.id,l=r.open,s=r.multiple,c=r.mode,f=r.searchValue,v=r.toggleOpen,d=r.notFoundContent,p=r.onPopupScroll,u=o.useContext(jn),h=u.maxCount,m=u.flattenOptions,g=u.onActiveValue,b=u.defaultActiveFirstOption,S=u.onSelect,R=u.menuItemSelectedIcon,w=u.rawValues,z=u.fieldNames,H=u.virtual,I=u.direction,_=u.listHeight,F=u.listItemHeight,j=u.optionRender,W="".concat(a,"-item"),V=yo(function(){return m},[l,m],function(P,y){return y[0]&&P[1]!==y[1]}),Q=o.useRef(null),de=o.useMemo(function(){return s&&Tn(h)&&(w==null?void 0:w.size)>=h},[s,h,w==null?void 0:w.size]),ne=function(y){y.preventDefault()},J=function(y){var M;(M=Q.current)===null||M===void 0||M.scrollTo(typeof y=="number"?{index:y}:y)},se=o.useCallback(function(P){return c==="combobox"?!1:w.has(P)},[c,nt(w).toString(),w.size]),K=function(y){for(var M=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,q=V.length,E=0;E<q;E+=1){var A=(y+E*M+q)%q,ce=V[A]||{},X=ce.group,ie=ce.data;if(!X&&!(ie!=null&&ie.disabled)&&(se(ie.value)||!de))return A}return-1},oe=o.useState(function(){return K(0)}),B=re(oe,2),O=B[0],Z=B[1],ae=function(y){var M=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;Z(y);var q={source:M?"keyboard":"mouse"},E=V[y];if(!E){g(null,-1,q);return}g(E.value,y,q)};o.useEffect(function(){ae(b!==!1?K(0):-1)},[V.length,f]);var pe=o.useCallback(function(P){return c==="combobox"?String(P).toLowerCase()===f.toLowerCase():w.has(P)},[c,f,nt(w).toString(),w.size]);o.useEffect(function(){var P=setTimeout(function(){if(!s&&l&&w.size===1){var M=Array.from(w)[0],q=V.findIndex(function(E){var A=E.data;return f?String(A.value).startsWith(f):A.value===M});q!==-1&&(ae(q),J(q))}});if(l){var y;(y=Q.current)===null||y===void 0||y.scrollTo(void 0)}return function(){return clearTimeout(P)}},[l,f]);var fe=function(y){y!==void 0&&S(y,{selected:!w.has(y)}),s||v(!1)};if(o.useImperativeHandle(n,function(){return{onKeyDown:function(y){var M=y.which,q=y.ctrlKey;switch(M){case U.N:case U.P:case U.UP:case U.DOWN:{var E=0;if(M===U.UP?E=-1:M===U.DOWN?E=1:ri()&&q&&(M===U.N?E=1:M===U.P&&(E=-1)),E!==0){var A=K(O+E,E);J(A),ae(A,!0)}break}case U.TAB:case U.ENTER:{var ce,X=V[O];X&&!(X!=null&&(ce=X.data)!==null&&ce!==void 0&&ce.disabled)&&!de?fe(X.value):fe(void 0),l&&y.preventDefault();break}case U.ESC:v(!1),l&&y.stopPropagation()}},onKeyUp:function(){},scrollTo:function(y){J(y)}}}),V.length===0)return o.createElement("div",{role:"listbox",id:"".concat(i,"_list"),className:"".concat(W,"-empty"),onMouseDown:ne},d);var Ne=Object.keys(z).map(function(P){return z[P]}),be=function(y){return y.label};function Pe(P,y){var M=P.group;return{role:M?"presentation":"option",id:"".concat(i,"_list_").concat(y)}}var we=function(y){var M=V[y];if(!M)return null;var q=M.data||{},E=q.value,A=M.group,ce=en(q,!0),X=be(M);return M?o.createElement("div",it({"aria-label":typeof X=="string"&&!A?X:null},ce,{key:y},Pe(M,y),{"aria-selected":pe(E)}),E):null},Ie={role:"listbox",id:"".concat(i,"_list")};return o.createElement(o.Fragment,null,H&&o.createElement("div",it({},Ie,{style:{height:0,width:0,overflow:"hidden"}}),we(O-1),we(O),we(O+1)),o.createElement(Wr,{itemKey:"key",ref:Q,data:V,height:_,itemHeight:F,fullHeight:!1,onMouseDown:ne,onScroll:p,virtual:H,direction:I,innerProps:H?null:Ie},function(P,y){var M=P.group,q=P.groupOption,E=P.data,A=P.label,ce=P.value,X=E.key;if(M){var ie,me=(ie=E.title)!==null&&ie!==void 0?ie:Sr(A)?A.toString():void 0;return o.createElement("div",{className:Te(W,"".concat(W,"-group"),E.className),title:me},A!==void 0?A:X)}var je=E.disabled,Me=E.title;E.children;var ze=E.style,ge=E.className,Xe=Mt(E,oi),tt=zn(Xe,Ne),Se=se(ce),De=je||!Se&&de,Ce="".concat(W,"-option"),Re=Te(W,Ce,ge,te(te(te(te({},"".concat(Ce,"-grouped"),q),"".concat(Ce,"-active"),O===y&&!De),"".concat(Ce,"-disabled"),De),"".concat(Ce,"-selected"),Se)),He=be(P),ft=!R||typeof R=="function"||Se,Ae=typeof He=="number"?He:He||ce,Ge=Sr(Ae)?Ae.toString():void 0;return Me!==void 0&&(Ge=Me),o.createElement("div",it({},en(tt),H?{}:Pe(P,y),{"aria-selected":pe(ce),className:Re,title:Ge,onMouseMove:function(){O===y||De||ae(y)},onClick:function(){De||fe(ce)},style:ze}),o.createElement("div",{className:"".concat(Ce,"-content")},typeof j=="function"?j(P,{index:y}):Ae),o.isValidElement(R)||Se,ft&&o.createElement(fn,{className:"".concat(W,"-option-state"),customizeIcon:R,customizeIconProps:{value:ce,disabled:De,isSelected:Se}},Se?"✓":null))}))},ii=o.forwardRef(ai);const li=function(e,t){var n=o.useRef({values:new Map,options:new Map}),r=o.useMemo(function(){var i=n.current,l=i.values,s=i.options,c=e.map(function(d){if(d.label===void 0){var p;return Y(Y({},d),{},{label:(p=l.get(d.value))===null||p===void 0?void 0:p.label})}return d}),f=new Map,v=new Map;return c.forEach(function(d){f.set(d.value,d),v.set(d.value,t.get(d.value)||s.get(d.value))}),n.current.values=f,n.current.options=v,c},[e,t]),a=o.useCallback(function(i){return t.get(i)||n.current.options.get(i)},[t]);return[r,a]};function gn(e,t){return Lr(e).join("").toUpperCase().includes(t)}const si=function(e,t,n,r,a){return o.useMemo(function(){if(!n||r===!1)return e;var i=t.options,l=t.label,s=t.value,c=[],f=typeof r=="function",v=n.toUpperCase(),d=f?r:function(u,h){return a?gn(h[a],v):h[i]?gn(h[l!=="children"?l:"label"],v):gn(h[s],v)},p=f?function(u){return Nn(u)}:function(u){return u};return e.forEach(function(u){if(u[i]){var h=d(n,p(u));if(h)c.push(u);else{var m=u[i].filter(function(g){return d(n,p(g))});m.length&&c.push(Y(Y({},u),{},te({},i,m)))}return}d(n,p(u))&&c.push(u)}),c},[e,r,a,n,t])};var Cr=0,ci=wo();function ui(){var e;return ci?(e=Cr,Cr+=1):e="TEST_OR_SSR",e}function di(e){var t=o.useState(),n=re(t,2),r=n[0],a=n[1];return o.useEffect(function(){a("rc_select_".concat(ui()))},[]),e||r}var fi=["children","value"],vi=["children"];function mi(e){var t=e,n=t.key,r=t.props,a=r.children,i=r.value,l=Mt(r,fi);return Y({key:n,value:i!==void 0?i:n,children:a},l)}function Ur(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;return Io(e).map(function(n,r){if(!o.isValidElement(n)||!n.type)return null;var a=n,i=a.type.isSelectOptGroup,l=a.key,s=a.props,c=s.children,f=Mt(s,vi);return t||!i?mi(n):Y(Y({key:"__RC_SELECT_GRP__".concat(l===null?r:l,"__"),label:l},f),{},{options:Ur(c)})}).filter(function(n){return n})}var hi=function(t,n,r,a,i){return o.useMemo(function(){var l=t,s=!t;s&&(l=Ur(n));var c=new Map,f=new Map,v=function(u,h,m){m&&typeof m=="string"&&u.set(h[m],h)},d=function p(u){for(var h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,m=0;m<u.length;m+=1){var g=u[m];!g[r.options]||h?(c.set(g[r.value],g),v(f,g,r.label),v(f,g,a),v(f,g,i)):p(g[r.options],!0)}};return d(l),{options:l,valueOptions:c,labelOptions:f}},[t,n,r,a,i])};function yr(e){var t=o.useRef();t.current=e;var n=o.useCallback(function(){return t.current.apply(t,arguments)},[]);return n}var gi=["id","mode","prefixCls","backfill","fieldNames","inputValue","searchValue","onSearch","autoClearSearchValue","onSelect","onDeselect","dropdownMatchSelectWidth","filterOption","filterSort","optionFilterProp","optionLabelProp","options","optionRender","children","defaultActiveFirstOption","menuItemSelectedIcon","virtual","direction","listHeight","listItemHeight","labelRender","value","defaultValue","labelInValue","onChange","maxCount"],pi=["inputValue"];function bi(e){return!e||Ft(e)!=="object"}var Si=o.forwardRef(function(e,t){var n=e.id,r=e.mode,a=e.prefixCls,i=a===void 0?"rc-select":a,l=e.backfill,s=e.fieldNames,c=e.inputValue,f=e.searchValue,v=e.onSearch,d=e.autoClearSearchValue,p=d===void 0?!0:d,u=e.onSelect,h=e.onDeselect,m=e.dropdownMatchSelectWidth,g=m===void 0?!0:m,b=e.filterOption,S=e.filterSort,R=e.optionFilterProp,w=e.optionLabelProp,z=e.options,H=e.optionRender,I=e.children,_=e.defaultActiveFirstOption,F=e.menuItemSelectedIcon,j=e.virtual,W=e.direction,V=e.listHeight,Q=V===void 0?200:V,de=e.listItemHeight,ne=de===void 0?20:de,J=e.labelRender,se=e.value,K=e.defaultValue,oe=e.labelInValue,B=e.onChange,O=e.maxCount,Z=Mt(e,gi),ae=di(n),pe=Bn(r),fe=!!(!z&&I),Ne=o.useMemo(function(){return b===void 0&&r==="combobox"?!1:b},[b,r]),be=o.useMemo(function(){return jr(s,fe)},[JSON.stringify(s),fe]),Pe=wn("",{value:f!==void 0?f:c,postState:function(L){return L||""}}),we=re(Pe,2),Ie=we[0],P=we[1],y=hi(z,I,be,R,w),M=y.valueOptions,q=y.labelOptions,E=y.options,A=o.useCallback(function(x){var L=Lr(x);return L.map(function(N){var D,ee,ye,ot,Ye;if(bi(N))D=N;else{var ct;ye=N.key,ee=N.label,D=(ct=N.value)!==null&&ct!==void 0?ct:ye}var Be=M.get(D);if(Be){var ut;ee===void 0&&(ee=Be==null?void 0:Be[w||be.label]),ye===void 0&&(ye=(ut=Be==null?void 0:Be.key)!==null&&ut!==void 0?ut:D),ot=Be==null?void 0:Be.disabled,Ye=Be==null?void 0:Be.title}return{label:ee,value:D,key:ye,disabled:ot,title:Ye}})},[be,w,M]),ce=wn(K,{value:se}),X=re(ce,2),ie=X[0],me=X[1],je=o.useMemo(function(){var x,L=pe&&ie===null?[]:ie,N=A(L);return r==="combobox"&&Ia((x=N[0])===null||x===void 0?void 0:x.value)?[]:N},[ie,A,r,pe]),Me=li(je,M),ze=re(Me,2),ge=ze[0],Xe=ze[1],tt=o.useMemo(function(){if(!r&&ge.length===1){var x=ge[0];if(x.value===null&&(x.label===null||x.label===void 0))return[]}return ge.map(function(L){var N;return Y(Y({},L),{},{label:(N=typeof J=="function"?J(L):L.label)!==null&&N!==void 0?N:L.value})})},[r,ge,J]),Se=o.useMemo(function(){return new Set(ge.map(function(x){return x.value}))},[ge]);o.useEffect(function(){if(r==="combobox"){var x,L=(x=ge[0])===null||x===void 0?void 0:x.value;P(wa(L)?String(L):"")}},[ge]);var De=yr(function(x,L){var N=L??x;return te(te({},be.value,x),be.label,N)}),Ce=o.useMemo(function(){if(r!=="tags")return E;var x=nt(E),L=function(D){return M.has(D)};return nt(ge).sort(function(N,D){return N.value<D.value?-1:1}).forEach(function(N){var D=N.value;L(D)||x.push(De(D,N.label))}),x},[De,E,M,ge,r]),Re=si(Ce,be,Ie,Ne,R),He=o.useMemo(function(){return r!=="tags"||!Ie||Re.some(function(x){return x[R||"value"]===Ie})||Re.some(function(x){return x[be.value]===Ie})?Re:[De(Ie)].concat(nt(Re))},[De,R,r,Re,Ie,be]),ft=function x(L){var N=nt(L).sort(function(D,ee){return S(D,ee,{searchValue:Ie})});return N.map(function(D){return Array.isArray(D.options)?Y(Y({},D),{},{options:D.options.length>0?x(D.options):D.options}):D})},Ae=o.useMemo(function(){return S?ft(He):He},[He,S,Ie]),Ge=o.useMemo(function(){return Ba(Ae,{fieldNames:be,childrenAsData:fe})},[Ae,be,fe]),$e=function(L){var N=A(L);if(me(N),B&&(N.length!==ge.length||N.some(function(ye,ot){var Ye;return((Ye=ge[ot])===null||Ye===void 0?void 0:Ye.value)!==(ye==null?void 0:ye.value)}))){var D=oe?N:N.map(function(ye){return ye.value}),ee=N.map(function(ye){return Nn(Xe(ye.value))});B(pe?D:D[0],pe?ee:ee[0])}},Dt=o.useState(null),Ue=re(Dt,2),Ct=Ue[0],Ve=Ue[1],Pt=o.useState(0),lt=re(Pt,2),vt=lt[0],yt=lt[1],Rt=_!==void 0?_:r!=="combobox",st=o.useCallback(function(x,L){var N=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},D=N.source,ee=D===void 0?"keyboard":D;yt(L),l&&r==="combobox"&&x!==null&&ee==="keyboard"&&Ve(String(x))},[l,r]),qe=function(L,N,D){var ee=function(){var dt,at=Xe(L);return[oe?{label:at==null?void 0:at[be.label],value:L,key:(dt=at==null?void 0:at.key)!==null&&dt!==void 0?dt:L}:L,Nn(at)]};if(N&&u){var ye=ee(),ot=re(ye,2),Ye=ot[0],ct=ot[1];u(Ye,ct)}else if(!N&&h&&D!=="clear"){var Be=ee(),ut=re(Be,2),Et=ut[0],Gt=ut[1];h(Et,Gt)}},mt=yr(function(x,L){var N,D=pe?L.selected:!0;D?N=pe?[].concat(nt(ge),[x]):[x]:N=ge.filter(function(ee){return ee.value!==x}),$e(N),qe(x,D),r==="combobox"?Ve(""):(!Bn||p)&&(P(""),Ve(""))}),ht=function(L,N){$e(L);var D=N.type,ee=N.values;(D==="remove"||D==="clear")&&ee.forEach(function(ye){qe(ye.value,!1,D)})},Ee=function(L,N){if(P(L),Ve(null),N.source==="submit"){var D=(L||"").trim();if(D){var ee=Array.from(new Set([].concat(nt(Se),[D])));$e(ee),qe(D,!0),P("")}return}N.source!=="blur"&&(r==="combobox"&&$e(L),v==null||v(L))},gt=function(L){var N=L;r!=="tags"&&(N=L.map(function(ee){var ye=q.get(ee);return ye==null?void 0:ye.value}).filter(function(ee){return ee!==void 0}));var D=Array.from(new Set([].concat(nt(Se),nt(N))));$e(D),D.forEach(function(ee){qe(ee,!0)})},rt=o.useMemo(function(){var x=j!==!1&&g!==!1;return Y(Y({},y),{},{flattenOptions:Ge,onActiveValue:st,defaultActiveFirstOption:Rt,onSelect:mt,menuItemSelectedIcon:F,rawValues:Se,fieldNames:be,virtual:x,direction:W,listHeight:Q,listItemHeight:ne,childrenAsData:fe,maxCount:O,optionRender:H})},[O,y,Ge,st,Rt,mt,F,Se,be,j,g,W,Q,ne,fe,H]);return o.createElement(jn.Provider,{value:rt},o.createElement(Fa,it({},Z,{id:ae,prefixCls:i,ref:t,omitDomProps:pi,mode:r,displayValues:tt,onDisplayValuesChange:ht,direction:W,searchValue:Ie,onSearch:Ee,autoClearSearchValue:p,onSearchSplit:gt,dropdownMatchSelectWidth:g,OptionList:ii,emptyOptions:!Ge.length,activeValue:Ct,activeDescendantId:"".concat(ae,"_list_").concat(vt)})))}),Wn=Si;Wn.Option=An;Wn.OptGroup=Vn;const Ci=()=>{const[,e]=Hn(),[t]=cn("Empty"),r=new kt(e.colorBgBase).toHsl().l<.5?{opacity:.65}:{};return o.createElement("svg",{style:r,width:"184",height:"152",viewBox:"0 0 184 152",xmlns:"http://www.w3.org/2000/svg"},o.createElement("title",null,(t==null?void 0:t.description)||"Empty"),o.createElement("g",{fill:"none",fillRule:"evenodd"},o.createElement("g",{transform:"translate(24 31.67)"},o.createElement("ellipse",{fillOpacity:".8",fill:"#F5F5F7",cx:"67.797",cy:"106.89",rx:"67.797",ry:"12.668"}),o.createElement("path",{d:"M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z",fill:"#AEB8C2"}),o.createElement("path",{d:"M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z",fill:"url(#linearGradient-1)",transform:"translate(13.56)"}),o.createElement("path",{d:"M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z",fill:"#F5F5F7"}),o.createElement("path",{d:"M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z",fill:"#DCE0E6"})),o.createElement("path",{d:"M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z",fill:"#DCE0E6"}),o.createElement("g",{transform:"translate(149.65 15.383)",fill:"#FFF"},o.createElement("ellipse",{cx:"20.654",cy:"3.167",rx:"2.849",ry:"2.815"}),o.createElement("path",{d:"M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z"}))))},yi=()=>{const[,e]=Hn(),[t]=cn("Empty"),{colorFill:n,colorFillTertiary:r,colorFillQuaternary:a,colorBgContainer:i}=e,{borderColor:l,shadowColor:s,contentColor:c}=o.useMemo(()=>({borderColor:new kt(n).onBackground(i).toHexString(),shadowColor:new kt(r).onBackground(i).toHexString(),contentColor:new kt(a).onBackground(i).toHexString()}),[n,r,a,i]);return o.createElement("svg",{width:"64",height:"41",viewBox:"0 0 64 41",xmlns:"http://www.w3.org/2000/svg"},o.createElement("title",null,(t==null?void 0:t.description)||"Empty"),o.createElement("g",{transform:"translate(0 1)",fill:"none",fillRule:"evenodd"},o.createElement("ellipse",{fill:s,cx:"32",cy:"33",rx:"32",ry:"7"}),o.createElement("g",{fillRule:"nonzero",stroke:l},o.createElement("path",{d:"M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"}),o.createElement("path",{d:"M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z",fill:c}))))},wi=e=>{const{componentCls:t,margin:n,marginXS:r,marginXL:a,fontSize:i,lineHeight:l}=e;return{[t]:{marginInline:r,fontSize:i,lineHeight:l,textAlign:"center",[`${t}-image`]:{height:e.emptyImgHeight,marginBottom:r,opacity:e.opacityImage,img:{height:"100%"},svg:{maxWidth:"100%",height:"100%",margin:"auto"}},[`${t}-description`]:{color:e.colorTextDescription},[`${t}-footer`]:{marginTop:n},"&-normal":{marginBlock:a,color:e.colorTextDescription,[`${t}-description`]:{color:e.colorTextDescription},[`${t}-image`]:{height:e.emptyImgHeightMD}},"&-small":{marginBlock:r,color:e.colorTextDescription,[`${t}-image`]:{height:e.emptyImgHeightSM}}}}},Ii=Ln("Empty",e=>{const{componentCls:t,controlHeightLG:n,calc:r}=e,a=Lt(e,{emptyImgCls:`${t}-img`,emptyImgHeight:r(n).mul(2.5).equal(),emptyImgHeightMD:n,emptyImgHeightSM:r(n).mul(.875).equal()});return[wi(a)]});var Ri=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n};const Kr=o.createElement(Ci,null),Xr=o.createElement(yi,null),Bt=e=>{const{className:t,rootClassName:n,prefixCls:r,image:a=Kr,description:i,children:l,imageStyle:s,style:c,classNames:f,styles:v}=e,d=Ri(e,["className","rootClassName","prefixCls","image","description","children","imageStyle","style","classNames","styles"]),{getPrefixCls:p,direction:u,className:h,style:m,classNames:g,styles:b}=Mr("empty"),S=p("empty",r),[R,w,z]=Ii(S),[H]=cn("Empty"),I=typeof i<"u"?i:H==null?void 0:H.description,_=typeof I=="string"?I:"empty";let F=null;return typeof a=="string"?F=o.createElement("img",{alt:_,src:a}):F=a,R(o.createElement("div",Object.assign({className:Te(w,z,S,h,{[`${S}-normal`]:a===Xr,[`${S}-rtl`]:u==="rtl"},t,n,g.root,f==null?void 0:f.root),style:Object.assign(Object.assign(Object.assign(Object.assign({},b.root),m),v==null?void 0:v.root),c)},d),o.createElement("div",{className:Te(`${S}-image`,g.image,f==null?void 0:f.image),style:Object.assign(Object.assign(Object.assign({},s),b.image),v==null?void 0:v.image)},F),I&&o.createElement("div",{className:Te(`${S}-description`,g.description,f==null?void 0:f.description),style:Object.assign(Object.assign({},b.description),v==null?void 0:v.description)},I),l&&o.createElement("div",{className:Te(`${S}-footer`,g.footer,f==null?void 0:f.footer),style:Object.assign(Object.assign({},b.footer),v==null?void 0:v.footer)},l)))};Bt.PRESENTED_IMAGE_DEFAULT=Kr;Bt.PRESENTED_IMAGE_SIMPLE=Xr;const Ei=e=>{const{componentName:t}=e,{getPrefixCls:n}=o.useContext(un),r=n("empty");switch(t){case"Table":case"List":return et.createElement(Bt,{image:Bt.PRESENTED_IMAGE_SIMPLE});case"Select":case"TreeSelect":case"Cascader":case"Transfer":case"Mentions":return et.createElement(Bt,{image:Bt.PRESENTED_IMAGE_SIMPLE,className:`${r}-small`});case"Table.filter":return null;default:return et.createElement(Bt,null)}},xi=e=>{const n={overflow:{adjustX:!0,adjustY:!0,shiftY:!0},htmlRegion:e==="scroll"?"scroll":"visible",dynamicInset:!0};return{bottomLeft:Object.assign(Object.assign({},n),{points:["tl","bl"],offset:[0,4]}),bottomRight:Object.assign(Object.assign({},n),{points:["tr","br"],offset:[0,4]}),topLeft:Object.assign(Object.assign({},n),{points:["bl","tl"],offset:[0,-4]}),topRight:Object.assign(Object.assign({},n),{points:["br","tr"],offset:[0,-4]})}};function $i(e,t){return e||xi(t)}const wr=e=>{const{optionHeight:t,optionFontSize:n,optionLineHeight:r,optionPadding:a}=e;return{position:"relative",display:"block",minHeight:t,padding:a,color:e.colorText,fontWeight:"normal",fontSize:n,lineHeight:r,boxSizing:"border-box"}},Oi=e=>{const{antCls:t,componentCls:n}=e,r=`${n}-item`,a=`&${t}-slide-up-enter${t}-slide-up-enter-active`,i=`&${t}-slide-up-appear${t}-slide-up-appear-active`,l=`&${t}-slide-up-leave${t}-slide-up-leave-active`,s=`${n}-dropdown-placement-`,c=`${r}-option-selected`;return[{[`${n}-dropdown`]:Object.assign(Object.assign({},dn(e)),{position:"absolute",top:-9999,zIndex:e.zIndexPopup,boxSizing:"border-box",padding:e.paddingXXS,overflow:"hidden",fontSize:e.fontSize,fontVariant:"initial",backgroundColor:e.colorBgElevated,borderRadius:e.borderRadiusLG,outline:"none",boxShadow:e.boxShadowSecondary,[`
          ${a}${s}bottomLeft,
          ${i}${s}bottomLeft
        `]:{animationName:$o},[`
          ${a}${s}topLeft,
          ${i}${s}topLeft,
          ${a}${s}topRight,
          ${i}${s}topRight
        `]:{animationName:xo},[`${l}${s}bottomLeft`]:{animationName:Eo},[`
          ${l}${s}topLeft,
          ${l}${s}topRight
        `]:{animationName:Ro},"&-hidden":{display:"none"},[r]:Object.assign(Object.assign({},wr(e)),{cursor:"pointer",transition:`background ${e.motionDurationSlow} ease`,borderRadius:e.borderRadiusSM,"&-group":{color:e.colorTextDescription,fontSize:e.fontSizeSM,cursor:"default"},"&-option":{display:"flex","&-content":Object.assign({flex:"auto"},In),"&-state":{flex:"none",display:"flex",alignItems:"center"},[`&-active:not(${r}-option-disabled)`]:{backgroundColor:e.optionActiveBg},[`&-selected:not(${r}-option-disabled)`]:{color:e.optionSelectedColor,fontWeight:e.optionSelectedFontWeight,backgroundColor:e.optionSelectedBg,[`${r}-option-state`]:{color:e.colorPrimary}},"&-disabled":{[`&${r}-option-selected`]:{backgroundColor:e.colorBgContainerDisabled},color:e.colorTextDisabled,cursor:"not-allowed"},"&-grouped":{paddingInlineStart:e.calc(e.controlPaddingHorizontal).mul(2).equal()}},"&-empty":Object.assign(Object.assign({},wr(e)),{color:e.colorTextDisabled})}),[`${c}:has(+ ${c})`]:{borderEndStartRadius:0,borderEndEndRadius:0,[`& + ${c}`]:{borderStartStartRadius:0,borderStartEndRadius:0}},"&-rtl":{direction:"rtl"}})},nr(e,"slide-up"),nr(e,"slide-down"),rr(e,"move-up"),rr(e,"move-down")]},Mi=e=>{const{multipleSelectItemHeight:t,paddingXXS:n,lineWidth:r,INTERNAL_FIXED_ITEM_MARGIN:a}=e,i=e.max(e.calc(n).sub(r).equal(),0),l=e.max(e.calc(i).sub(a).equal(),0);return{basePadding:i,containerPadding:l,itemHeight:xe(t),itemLineHeight:xe(e.calc(t).sub(e.calc(e.lineWidth).mul(2)).equal())}},Di=e=>{const{multipleSelectItemHeight:t,selectHeight:n,lineWidth:r}=e;return e.calc(n).sub(t).div(2).sub(r).equal()},Pi=e=>{const{componentCls:t,iconCls:n,borderRadiusSM:r,motionDurationSlow:a,paddingXS:i,multipleItemColorDisabled:l,multipleItemBorderColorDisabled:s,colorIcon:c,colorIconHover:f,INTERNAL_FIXED_ITEM_MARGIN:v}=e;return{[`${t}-selection-overflow`]:{position:"relative",display:"flex",flex:"auto",flexWrap:"wrap",maxWidth:"100%","&-item":{flex:"none",alignSelf:"center",maxWidth:"100%",display:"inline-flex"},[`${t}-selection-item`]:{display:"flex",alignSelf:"center",flex:"none",boxSizing:"border-box",maxWidth:"100%",marginBlock:v,borderRadius:r,cursor:"default",transition:`font-size ${a}, line-height ${a}, height ${a}`,marginInlineEnd:e.calc(v).mul(2).equal(),paddingInlineStart:i,paddingInlineEnd:e.calc(i).div(2).equal(),[`${t}-disabled&`]:{color:l,borderColor:s,cursor:"not-allowed"},"&-content":{display:"inline-block",marginInlineEnd:e.calc(i).div(2).equal(),overflow:"hidden",whiteSpace:"pre",textOverflow:"ellipsis"},"&-remove":Object.assign(Object.assign({},Dr()),{display:"inline-flex",alignItems:"center",color:c,fontWeight:"bold",fontSize:10,lineHeight:"inherit",cursor:"pointer",[`> ${n}`]:{verticalAlign:"-0.2em"},"&:hover":{color:f}})}}}},Ti=(e,t)=>{const{componentCls:n,INTERNAL_FIXED_ITEM_MARGIN:r}=e,a=`${n}-selection-overflow`,i=e.multipleSelectItemHeight,l=Di(e),s=t?`${n}-${t}`:"",c=Mi(e);return{[`${n}-multiple${s}`]:Object.assign(Object.assign({},Pi(e)),{[`${n}-selector`]:{display:"flex",alignItems:"center",width:"100%",height:"100%",paddingInline:c.basePadding,paddingBlock:c.containerPadding,borderRadius:e.borderRadius,[`${n}-disabled&`]:{background:e.multipleSelectorBgDisabled,cursor:"not-allowed"},"&:after":{display:"inline-block",width:0,margin:`${xe(r)} 0`,lineHeight:xe(i),visibility:"hidden",content:'"\\a0"'}},[`${n}-selection-item`]:{height:c.itemHeight,lineHeight:xe(c.itemLineHeight)},[`${n}-selection-wrap`]:{alignSelf:"flex-start","&:after":{lineHeight:xe(i),marginBlock:r}},[`${n}-prefix`]:{marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(c.basePadding).equal()},[`${a}-item + ${a}-item,
        ${n}-prefix + ${n}-selection-wrap
      `]:{[`${n}-selection-search`]:{marginInlineStart:0},[`${n}-selection-placeholder`]:{insetInlineStart:0}},[`${a}-item-suffix`]:{minHeight:c.itemHeight,marginBlock:r},[`${n}-selection-search`]:{display:"inline-flex",position:"relative",maxWidth:"100%",marginInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(l).equal(),"\n          &-input,\n          &-mirror\n        ":{height:i,fontFamily:e.fontFamily,lineHeight:xe(i),transition:`all ${e.motionDurationSlow}`},"&-input":{width:"100%",minWidth:4.1},"&-mirror":{position:"absolute",top:0,insetInlineStart:0,insetInlineEnd:"auto",zIndex:999,whiteSpace:"pre",visibility:"hidden"}},[`${n}-selection-placeholder`]:{position:"absolute",top:"50%",insetInlineStart:e.calc(e.inputPaddingHorizontalBase).sub(c.basePadding).equal(),insetInlineEnd:e.inputPaddingHorizontalBase,transform:"translateY(-50%)",transition:`all ${e.motionDurationSlow}`}})}};function pn(e,t){const{componentCls:n}=e,r=t?`${n}-${t}`:"",a={[`${n}-multiple${r}`]:{fontSize:e.fontSize,[`${n}-selector`]:{[`${n}-show-search&`]:{cursor:"text"}},[`
        &${n}-show-arrow ${n}-selector,
        &${n}-allow-clear ${n}-selector
      `]:{paddingInlineEnd:e.calc(e.fontSizeIcon).add(e.controlPaddingHorizontal).equal()}}};return[Ti(e,t),a]}const Ni=e=>{const{componentCls:t}=e,n=Lt(e,{selectHeight:e.controlHeightSM,multipleSelectItemHeight:e.multipleItemHeightSM,borderRadius:e.borderRadiusSM,borderRadiusSM:e.borderRadiusXS}),r=Lt(e,{fontSize:e.fontSizeLG,selectHeight:e.controlHeightLG,multipleSelectItemHeight:e.multipleItemHeightLG,borderRadius:e.borderRadiusLG,borderRadiusSM:e.borderRadius});return[pn(e),pn(n,"sm"),{[`${t}-multiple${t}-sm`]:{[`${t}-selection-placeholder`]:{insetInline:e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal()},[`${t}-selection-search`]:{marginInlineStart:2}}},pn(r,"lg")]};function bn(e,t){const{componentCls:n,inputPaddingHorizontalBase:r,borderRadius:a}=e,i=e.calc(e.controlHeight).sub(e.calc(e.lineWidth).mul(2)).equal(),l=t?`${n}-${t}`:"";return{[`${n}-single${l}`]:{fontSize:e.fontSize,height:e.controlHeight,[`${n}-selector`]:Object.assign(Object.assign({},dn(e,!0)),{display:"flex",borderRadius:a,flex:"1 1 auto",[`${n}-selection-wrap:after`]:{lineHeight:xe(i)},[`${n}-selection-search`]:{position:"absolute",inset:0,width:"100%","&-input":{width:"100%",WebkitAppearance:"textfield"}},[`
          ${n}-selection-item,
          ${n}-selection-placeholder
        `]:{display:"block",padding:0,lineHeight:xe(i),transition:`all ${e.motionDurationSlow}, visibility 0s`,alignSelf:"center"},[`${n}-selection-placeholder`]:{transition:"none",pointerEvents:"none"},[["&:after",`${n}-selection-item:empty:after`,`${n}-selection-placeholder:empty:after`].join(",")]:{display:"inline-block",width:0,visibility:"hidden",content:'"\\a0"'}}),[`
        &${n}-show-arrow ${n}-selection-item,
        &${n}-show-arrow ${n}-selection-search,
        &${n}-show-arrow ${n}-selection-placeholder
      `]:{paddingInlineEnd:e.showArrowPaddingInlineEnd},[`&${n}-open ${n}-selection-item`]:{color:e.colorTextPlaceholder},[`&:not(${n}-customize-input)`]:{[`${n}-selector`]:{width:"100%",height:"100%",alignItems:"center",padding:`0 ${xe(r)}`,[`${n}-selection-search-input`]:{height:i,fontSize:e.fontSize},"&:after":{lineHeight:xe(i)}}},[`&${n}-customize-input`]:{[`${n}-selector`]:{"&:after":{display:"none"},[`${n}-selection-search`]:{position:"static",width:"100%"},[`${n}-selection-placeholder`]:{position:"absolute",insetInlineStart:0,insetInlineEnd:0,padding:`0 ${xe(r)}`,"&:after":{display:"none"}}}}}}}function Bi(e){const{componentCls:t}=e,n=e.calc(e.controlPaddingHorizontalSM).sub(e.lineWidth).equal();return[bn(e),bn(Lt(e,{controlHeight:e.controlHeightSM,borderRadius:e.borderRadiusSM}),"sm"),{[`${t}-single${t}-sm`]:{[`&:not(${t}-customize-input)`]:{[`${t}-selector`]:{padding:`0 ${xe(n)}`},[`&${t}-show-arrow ${t}-selection-search`]:{insetInlineEnd:e.calc(n).add(e.calc(e.fontSize).mul(1.5)).equal()},[`
            &${t}-show-arrow ${t}-selection-item,
            &${t}-show-arrow ${t}-selection-placeholder
          `]:{paddingInlineEnd:e.calc(e.fontSize).mul(1.5).equal()}}}},bn(Lt(e,{controlHeight:e.singleItemHeightLG,fontSize:e.fontSizeLG,borderRadius:e.borderRadiusLG}),"lg")]}const _i=e=>{const{fontSize:t,lineHeight:n,lineWidth:r,controlHeight:a,controlHeightSM:i,controlHeightLG:l,paddingXXS:s,controlPaddingHorizontal:c,zIndexPopupBase:f,colorText:v,fontWeightStrong:d,controlItemBgActive:p,controlItemBgHover:u,colorBgContainer:h,colorFillSecondary:m,colorBgContainerDisabled:g,colorTextDisabled:b,colorPrimaryHover:S,colorPrimary:R,controlOutline:w}=e,z=s*2,H=r*2,I=Math.min(a-z,a-H),_=Math.min(i-z,i-H),F=Math.min(l-z,l-H);return{INTERNAL_FIXED_ITEM_MARGIN:Math.floor(s/2),zIndexPopup:f+50,optionSelectedColor:v,optionSelectedFontWeight:d,optionSelectedBg:p,optionActiveBg:u,optionPadding:`${(a-t*n)/2}px ${c}px`,optionFontSize:t,optionLineHeight:n,optionHeight:a,selectorBg:h,clearBg:h,singleItemHeightLG:l,multipleItemBg:m,multipleItemBorderColor:"transparent",multipleItemHeight:I,multipleItemHeightSM:_,multipleItemHeightLG:F,multipleSelectorBgDisabled:g,multipleItemColorDisabled:b,multipleItemBorderColorDisabled:"transparent",showArrowPaddingInlineEnd:Math.ceil(e.fontSize*1.25),hoverBorderColor:S,activeBorderColor:R,activeOutlineColor:w,selectAffixPadding:s}},Gr=(e,t)=>{const{componentCls:n,antCls:r,controlOutlineWidth:a}=e;return{[`&:not(${n}-customize-input) ${n}-selector`]:{border:`${xe(e.lineWidth)} ${e.lineType} ${t.borderColor}`,background:e.selectorBg},[`&:not(${n}-disabled):not(${n}-customize-input):not(${r}-pagination-size-changer)`]:{[`&:hover ${n}-selector`]:{borderColor:t.hoverBorderHover},[`${n}-focused& ${n}-selector`]:{borderColor:t.activeBorderColor,boxShadow:`0 0 0 ${xe(a)} ${t.activeOutlineColor}`,outline:0},[`${n}-prefix`]:{color:t.color}}}},Ir=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},Gr(e,t))}),zi=e=>({"&-outlined":Object.assign(Object.assign(Object.assign(Object.assign({},Gr(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),Ir(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),Ir(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{background:e.colorBgContainerDisabled,color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${xe(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`}})}),Qr=(e,t)=>{const{componentCls:n,antCls:r}=e;return{[`&:not(${n}-customize-input) ${n}-selector`]:{background:t.bg,border:`${xe(e.lineWidth)} ${e.lineType} transparent`,color:t.color},[`&:not(${n}-disabled):not(${n}-customize-input):not(${r}-pagination-size-changer)`]:{[`&:hover ${n}-selector`]:{background:t.hoverBg},[`${n}-focused& ${n}-selector`]:{background:e.selectorBg,borderColor:t.activeBorderColor,outline:0}}}},Rr=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},Qr(e,t))}),Hi=e=>({"&-filled":Object.assign(Object.assign(Object.assign(Object.assign({},Qr(e,{bg:e.colorFillTertiary,hoverBg:e.colorFillSecondary,activeBorderColor:e.activeBorderColor,color:e.colorText})),Rr(e,{status:"error",bg:e.colorErrorBg,hoverBg:e.colorErrorBgHover,activeBorderColor:e.colorError,color:e.colorError})),Rr(e,{status:"warning",bg:e.colorWarningBg,hoverBg:e.colorWarningBgHover,activeBorderColor:e.colorWarning,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{borderColor:e.colorBorder,background:e.colorBgContainerDisabled,color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.colorBgContainer,border:`${xe(e.lineWidth)} ${e.lineType} ${e.colorSplit}`}})}),Li=e=>({"&-borderless":{[`${e.componentCls}-selector`]:{background:"transparent",border:`${xe(e.lineWidth)} ${e.lineType} transparent`},[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${xe(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`},[`&${e.componentCls}-status-error`]:{[`${e.componentCls}-prefix, ${e.componentCls}-selection-item`]:{color:e.colorError}},[`&${e.componentCls}-status-warning`]:{[`${e.componentCls}-prefix, ${e.componentCls}-selection-item`]:{color:e.colorWarning}}}}),qr=(e,t)=>{const{componentCls:n,antCls:r}=e;return{[`&:not(${n}-customize-input) ${n}-selector`]:{borderWidth:`0 0 ${xe(e.lineWidth)} 0`,borderStyle:`none none ${e.lineType} none`,borderColor:t.borderColor,background:e.selectorBg,borderRadius:0},[`&:not(${n}-disabled):not(${n}-customize-input):not(${r}-pagination-size-changer)`]:{[`&:hover ${n}-selector`]:{borderColor:t.hoverBorderHover},[`${n}-focused& ${n}-selector`]:{borderColor:t.activeBorderColor,outline:0},[`${n}-prefix`]:{color:t.color}}}},Er=(e,t)=>({[`&${e.componentCls}-status-${t.status}`]:Object.assign({},qr(e,t))}),Fi=e=>({"&-underlined":Object.assign(Object.assign(Object.assign(Object.assign({},qr(e,{borderColor:e.colorBorder,hoverBorderHover:e.hoverBorderColor,activeBorderColor:e.activeBorderColor,activeOutlineColor:e.activeOutlineColor,color:e.colorText})),Er(e,{status:"error",borderColor:e.colorError,hoverBorderHover:e.colorErrorHover,activeBorderColor:e.colorError,activeOutlineColor:e.colorErrorOutline,color:e.colorError})),Er(e,{status:"warning",borderColor:e.colorWarning,hoverBorderHover:e.colorWarningHover,activeBorderColor:e.colorWarning,activeOutlineColor:e.colorWarningOutline,color:e.colorWarning})),{[`&${e.componentCls}-disabled`]:{[`&:not(${e.componentCls}-customize-input) ${e.componentCls}-selector`]:{color:e.colorTextDisabled}},[`&${e.componentCls}-multiple ${e.componentCls}-selection-item`]:{background:e.multipleItemBg,border:`${xe(e.lineWidth)} ${e.lineType} ${e.multipleItemBorderColor}`}})}),ji=e=>({[e.componentCls]:Object.assign(Object.assign(Object.assign(Object.assign({},zi(e)),Hi(e)),Li(e)),Fi(e))}),Vi=e=>{const{componentCls:t}=e;return{position:"relative",transition:`all ${e.motionDurationMid} ${e.motionEaseInOut}`,input:{cursor:"pointer"},[`${t}-show-search&`]:{cursor:"text",input:{cursor:"auto",color:"inherit",height:"100%"}},[`${t}-disabled&`]:{cursor:"not-allowed",input:{cursor:"not-allowed"}}}},Ai=e=>{const{componentCls:t}=e;return{[`${t}-selection-search-input`]:{margin:0,padding:0,background:"transparent",border:"none",outline:"none",appearance:"none",fontFamily:"inherit","&::-webkit-search-cancel-button":{display:"none",appearance:"none"}}}},Wi=e=>{const{antCls:t,componentCls:n,inputPaddingHorizontalBase:r,iconCls:a}=e,i={[`${n}-clear`]:{opacity:1,background:e.colorBgBase,borderRadius:"50%"}};return{[n]:Object.assign(Object.assign({},dn(e)),{position:"relative",display:"inline-flex",cursor:"pointer",[`&:not(${n}-customize-input) ${n}-selector`]:Object.assign(Object.assign({},Vi(e)),Ai(e)),[`${n}-selection-item`]:Object.assign(Object.assign({flex:1,fontWeight:"normal",position:"relative",userSelect:"none"},In),{[`> ${t}-typography`]:{display:"inline"}}),[`${n}-selection-placeholder`]:Object.assign(Object.assign({},In),{flex:1,color:e.colorTextPlaceholder,pointerEvents:"none"}),[`${n}-arrow`]:Object.assign(Object.assign({},Dr()),{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:r,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,lineHeight:1,textAlign:"center",pointerEvents:"none",display:"flex",alignItems:"center",transition:`opacity ${e.motionDurationSlow} ease`,[a]:{verticalAlign:"top",transition:`transform ${e.motionDurationSlow}`,"> svg":{verticalAlign:"top"},[`&:not(${n}-suffix)`]:{pointerEvents:"auto"}},[`${n}-disabled &`]:{cursor:"not-allowed"},"> *:not(:last-child)":{marginInlineEnd:8}}),[`${n}-selection-wrap`]:{display:"flex",width:"100%",position:"relative",minWidth:0,"&:after":{content:'"\\a0"',width:0,overflow:"hidden"}},[`${n}-prefix`]:{flex:"none",marginInlineEnd:e.selectAffixPadding},[`${n}-clear`]:{position:"absolute",top:"50%",insetInlineStart:"auto",insetInlineEnd:r,zIndex:1,display:"inline-block",width:e.fontSizeIcon,height:e.fontSizeIcon,marginTop:e.calc(e.fontSizeIcon).mul(-1).div(2).equal(),color:e.colorTextQuaternary,fontSize:e.fontSizeIcon,fontStyle:"normal",lineHeight:1,textAlign:"center",textTransform:"none",cursor:"pointer",opacity:0,transition:`color ${e.motionDurationMid} ease, opacity ${e.motionDurationSlow} ease`,textRendering:"auto","&:before":{display:"block"},"&:hover":{color:e.colorIcon}},"@media(hover:none)":i,"&:hover":i}),[`${n}-status`]:{"&-error, &-warning, &-success, &-validating":{[`&${n}-has-feedback`]:{[`${n}-clear`]:{insetInlineEnd:e.calc(r).add(e.fontSize).add(e.paddingXS).equal()}}}}}},Ui=e=>{const{componentCls:t}=e;return[{[t]:{[`&${t}-in-form-item`]:{width:"100%"}}},Wi(e),Bi(e),Ni(e),Oi(e),{[`${t}-rtl`]:{direction:"rtl"}},Oo(e,{borderElCls:`${t}-selector`,focusElCls:`${t}-focused`})]},Ki=Ln("Select",(e,{rootPrefixCls:t})=>{const n=Lt(e,{rootPrefixCls:t,inputPaddingHorizontalBase:e.calc(e.paddingSM).sub(1).equal(),multipleSelectItemHeight:e.multipleItemHeight,selectHeight:e.controlHeight});return[Ui(n),ji(n)]},_i,{unitless:{optionLineHeight:!0,optionSelectedFontWeight:!0}});var Xi={icon:{tag:"svg",attrs:{viewBox:"64 64 896 896",focusable:"false"},children:[{tag:"path",attrs:{d:"M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"}}]},name:"down",theme:"outlined"},Gi=function(t,n){return o.createElement(Mo,it({},t,{ref:n,icon:Xi}))},Qi=o.forwardRef(Gi);function qi({suffixIcon:e,clearIcon:t,menuItemSelectedIcon:n,removeIcon:r,loading:a,multiple:i,hasFeedback:l,prefixCls:s,showSuffixIcon:c,feedbackIcon:f,showArrow:v,componentName:d}){const p=t??o.createElement(Do,null),u=b=>e===null&&!l&&!v?null:o.createElement(o.Fragment,null,c!==!1&&b,l&&f);let h=null;if(e!==void 0)h=u(e);else if(a)h=u(o.createElement(To,{spin:!0}));else{const b=`${s}-suffix`;h=({open:S,showSearch:R})=>u(S&&R?o.createElement(qo,{className:b}):o.createElement(Qi,{className:b}))}let m=null;n!==void 0?m=n:i?m=o.createElement(Po,null):m=null;let g=null;return r!==void 0?g=r:g=o.createElement(Tr,null),{clearIcon:p,suffixIcon:h,itemIcon:m,removeIcon:g}}function Yi(e,t){return t!==void 0?t:e!==null}var Zi=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n};const Yr="SECRET_COMBOBOX_MODE_DO_NOT_USE",Ji=(e,t)=>{var n,r,a,i,l;const{prefixCls:s,bordered:c,className:f,rootClassName:v,getPopupContainer:d,popupClassName:p,dropdownClassName:u,listHeight:h=256,placement:m,listItemHeight:g,size:b,disabled:S,notFoundContent:R,status:w,builtinPlacements:z,dropdownMatchSelectWidth:H,popupMatchSelectWidth:I,direction:_,style:F,allowClear:j,variant:W,dropdownStyle:V,transitionName:Q,tagRender:de,maxCount:ne,prefix:J,dropdownRender:se,popupRender:K,onDropdownVisibleChange:oe,onOpenChange:B,styles:O,classNames:Z}=e,ae=Zi(e,["prefixCls","bordered","className","rootClassName","getPopupContainer","popupClassName","dropdownClassName","listHeight","placement","listItemHeight","size","disabled","notFoundContent","status","builtinPlacements","dropdownMatchSelectWidth","popupMatchSelectWidth","direction","style","allowClear","variant","dropdownStyle","transitionName","tagRender","maxCount","prefix","dropdownRender","popupRender","onDropdownVisibleChange","onOpenChange","styles","classNames"]),{getPopupContainer:pe,getPrefixCls:fe,renderEmpty:Ne,direction:be,virtual:Pe,popupMatchSelectWidth:we,popupOverflow:Ie}=o.useContext(un),{showSearch:P,style:y,styles:M,className:q,classNames:E}=Mr("select"),[,A]=Hn(),ce=g??(A==null?void 0:A.controlHeight),X=fe("select",s),ie=fe(),me=_??be,{compactSize:je,compactItemClassnames:Me}=No(X,me),[ze,ge]=Bo("select",W,c),Xe=_o(X),[tt,Se,De]=Ki(X,Xe),Ce=o.useMemo(()=>{const{mode:D}=e;if(D!=="combobox")return D===Yr?"combobox":D},[e.mode]),Re=Ce==="multiple"||Ce==="tags",He=Yi(e.suffixIcon,e.showArrow),ft=(n=I??H)!==null&&n!==void 0?n:we,Ae=((r=O==null?void 0:O.popup)===null||r===void 0?void 0:r.root)||((a=M.popup)===null||a===void 0?void 0:a.root)||V,Ge=K||se,$e=B||oe,{status:Dt,hasFeedback:Ue,isFormItemInput:Ct,feedbackIcon:Ve}=o.useContext(zo),Pt=Ao(Dt,w);let lt;R!==void 0?lt=R:Ce==="combobox"?lt=null:lt=(Ne==null?void 0:Ne("Select"))||o.createElement(Ei,{componentName:"Select"});const{suffixIcon:vt,itemIcon:yt,removeIcon:Rt,clearIcon:st}=qi(Object.assign(Object.assign({},ae),{multiple:Re,hasFeedback:Ue,feedbackIcon:Ve,showSuffixIcon:He,prefixCls:X,componentName:"Select"})),qe=j===!0?{clearIcon:st}:j,mt=zn(ae,["suffixIcon","itemIcon"]),ht=Te(((i=Z==null?void 0:Z.popup)===null||i===void 0?void 0:i.root)||((l=E==null?void 0:E.popup)===null||l===void 0?void 0:l.root)||p||u,{[`${X}-dropdown-${me}`]:me==="rtl"},v,E.root,Z==null?void 0:Z.root,De,Xe,Se),Ee=Ho(D=>{var ee;return(ee=b??je)!==null&&ee!==void 0?ee:D}),gt=o.useContext(Lo),rt=S??gt,x=Te({[`${X}-lg`]:Ee==="large",[`${X}-sm`]:Ee==="small",[`${X}-rtl`]:me==="rtl",[`${X}-${ze}`]:ge,[`${X}-in-form-item`]:Ct},Fo(X,Pt,Ue),Me,q,f,E.root,Z==null?void 0:Z.root,v,De,Xe,Se),L=o.useMemo(()=>m!==void 0?m:me==="rtl"?"bottomRight":"bottomLeft",[m,me]),[N]=jo("SelectLike",Ae==null?void 0:Ae.zIndex);return tt(o.createElement(Wn,Object.assign({ref:t,virtual:Pe,showSearch:P},mt,{style:Object.assign(Object.assign(Object.assign(Object.assign({},M.root),O==null?void 0:O.root),y),F),dropdownMatchSelectWidth:ft,transitionName:Vo(ie,"slide-up",Q),builtinPlacements:$i(z,Ie),listHeight:h,listItemHeight:ce,mode:Ce,prefixCls:X,placement:L,direction:me,prefix:J,suffixIcon:vt,menuItemSelectedIcon:yt,removeIcon:Rt,allowClear:qe,notFoundContent:lt,className:x,getPopupContainer:d||pe,dropdownClassName:ht,disabled:rt,dropdownStyle:Object.assign(Object.assign({},Ae),{zIndex:N}),maxCount:Re?ne:void 0,tagRender:Re?de:void 0,dropdownRender:Ge,onDropdownVisibleChange:$e})))},rn=o.forwardRef(Ji),ki=Wo(rn,"dropdownAlign");rn.SECRET_COMBOBOX_MODE_DO_NOT_USE=Yr;rn.Option=An;rn.OptGroup=Vn;rn._InternalPanelDoNotUseOrYouWillBeFired=ki;const el=e=>{const{paddingXXS:t,lineWidth:n,tagPaddingHorizontal:r,componentCls:a,calc:i}=e,l=i(r).sub(n).equal(),s=i(t).sub(n).equal();return{[a]:Object.assign(Object.assign({},dn(e)),{display:"inline-block",height:"auto",marginInlineEnd:e.marginXS,paddingInline:l,fontSize:e.tagFontSize,lineHeight:e.tagLineHeight,whiteSpace:"nowrap",background:e.defaultBg,border:`${xe(e.lineWidth)} ${e.lineType} ${e.colorBorder}`,borderRadius:e.borderRadiusSM,opacity:1,transition:`all ${e.motionDurationMid}`,textAlign:"start",position:"relative",[`&${a}-rtl`]:{direction:"rtl"},"&, a, a:hover":{color:e.defaultColor},[`${a}-close-icon`]:{marginInlineStart:s,fontSize:e.tagIconSize,color:e.colorIcon,cursor:"pointer",transition:`all ${e.motionDurationMid}`,"&:hover":{color:e.colorTextHeading}},[`&${a}-has-color`]:{borderColor:"transparent",[`&, a, a:hover, ${e.iconCls}-close, ${e.iconCls}-close:hover`]:{color:e.colorTextLightSolid}},"&-checkable":{backgroundColor:"transparent",borderColor:"transparent",cursor:"pointer",[`&:not(${a}-checkable-checked):hover`]:{color:e.colorPrimary,backgroundColor:e.colorFillSecondary},"&:active, &-checked":{color:e.colorTextLightSolid},"&-checked":{backgroundColor:e.colorPrimary,"&:hover":{backgroundColor:e.colorPrimaryHover}},"&:active":{backgroundColor:e.colorPrimaryActive}},"&-hidden":{display:"none"},[`> ${e.iconCls} + span, > span + ${e.iconCls}`]:{marginInlineStart:l}}),[`${a}-borderless`]:{borderColor:"transparent",background:e.tagBorderlessBg}}},Un=e=>{const{lineWidth:t,fontSizeIcon:n,calc:r}=e,a=e.fontSizeSM;return Lt(e,{tagFontSize:a,tagLineHeight:xe(r(e.lineHeightSM).mul(a).equal()),tagIconSize:r(n).sub(r(t).mul(2)).equal(),tagPaddingHorizontal:8,tagBorderlessBg:e.defaultBg})},Kn=e=>({defaultBg:new kt(e.colorFillQuaternary).onBackground(e.colorBgContainer).toHexString(),defaultColor:e.colorText}),Zr=Ln("Tag",e=>{const t=Un(e);return el(t)},Kn);var tl=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n};const nl=o.forwardRef((e,t)=>{const{prefixCls:n,style:r,className:a,checked:i,onChange:l,onClick:s}=e,c=tl(e,["prefixCls","style","className","checked","onChange","onClick"]),{getPrefixCls:f,tag:v}=o.useContext(un),d=b=>{l==null||l(!i),s==null||s(b)},p=f("tag",n),[u,h,m]=Zr(p),g=Te(p,`${p}-checkable`,{[`${p}-checkable-checked`]:i},v==null?void 0:v.className,a,h,m);return u(o.createElement("span",Object.assign({},c,{ref:t,style:Object.assign(Object.assign({},r),v==null?void 0:v.style),className:g,onClick:d})))}),rl=e=>Uo(e,(t,{textColor:n,lightBorderColor:r,lightColor:a,darkColor:i})=>({[`${e.componentCls}${e.componentCls}-${t}`]:{color:n,background:a,borderColor:r,"&-inverse":{color:e.colorTextLightSolid,background:i,borderColor:i},[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}})),ol=Pr(["Tag","preset"],e=>{const t=Un(e);return rl(t)},Kn);function al(e){return typeof e!="string"?e:e.charAt(0).toUpperCase()+e.slice(1)}const sn=(e,t,n)=>{const r=al(n);return{[`${e.componentCls}${e.componentCls}-${t}`]:{color:e[`color${n}`],background:e[`color${r}Bg`],borderColor:e[`color${r}Border`],[`&${e.componentCls}-borderless`]:{borderColor:"transparent"}}}},il=Pr(["Tag","status"],e=>{const t=Un(e);return[sn(t,"success","Success"),sn(t,"processing","Info"),sn(t,"error","Error"),sn(t,"warning","Warning")]},Kn);var ll=function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)t.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(n[r[a]]=e[r[a]]);return n};const sl=o.forwardRef((e,t)=>{const{prefixCls:n,className:r,rootClassName:a,style:i,children:l,icon:s,color:c,onClose:f,bordered:v=!0,visible:d}=e,p=ll(e,["prefixCls","className","rootClassName","style","children","icon","color","onClose","bordered","visible"]),{getPrefixCls:u,direction:h,tag:m}=o.useContext(un),[g,b]=o.useState(!0),S=zn(p,["closeIcon","closable"]);o.useEffect(()=>{d!==void 0&&b(d)},[d]);const R=Ko(c),w=Xo(c),z=R||w,H=Object.assign(Object.assign({backgroundColor:c&&!z?c:void 0},m==null?void 0:m.style),i),I=u("tag",n),[_,F,j]=Zr(I),W=Te(I,m==null?void 0:m.className,{[`${I}-${c}`]:z,[`${I}-has-color`]:c&&!z,[`${I}-hidden`]:!g,[`${I}-rtl`]:h==="rtl",[`${I}-borderless`]:!v},r,a,F,j),V=K=>{K.stopPropagation(),f==null||f(K),!K.defaultPrevented&&b(!1)},[,Q]=da(sr(e),sr(m),{closable:!1,closeIconRender:K=>{const oe=o.createElement("span",{className:`${I}-close-icon`,onClick:V},K);return Go(K,oe,B=>({onClick:O=>{var Z;(Z=B==null?void 0:B.onClick)===null||Z===void 0||Z.call(B,O),V(O)},className:Te(B==null?void 0:B.className,`${I}-close-icon`)}))}}),de=typeof p.onClick=="function"||l&&l.type==="a",ne=s||null,J=ne?o.createElement(o.Fragment,null,ne,l&&o.createElement("span",null,l)):l,se=o.createElement("span",Object.assign({},S,{ref:t,className:W,style:H}),J,Q,R&&o.createElement(ol,{key:"preset",prefixCls:I}),w&&o.createElement(il,{key:"status",prefixCls:I}));return _(de?o.createElement(Qo,{component:"Tag"},se):se)}),cl=sl;cl.CheckableTag=nl;export{Ei as D,Bt as E,Wr as L,Qi as R,rn as S,cl as T,da as a,lr as m,sr as p,vl as u};
